import type { HeetApplicationApiResponse } from "~/db/types/applications";
import { withRetry, RetryError } from "~/utils/retry.server";
import { env } from "~/env.server";
import type { HeetOrganizationApiResponse, OrgLevel } from "~/db/types/organization";
import { HeetOrganizationApiResponseSchema } from "~/db/types/organization";
import { AppLoadContext } from '@remix-run/node';
import { getHeetApiUrl } from '~/utils/env.server';
import { ApiError, makeRequest, DEFAULT_API_CONFIG, buildQueryParams, createApiCache } from '~/utils/api-utils.server';

// API Error class specific to HEET
export class HeetApiError extends ApiError {
  constructor(
    message: string,
    status: number,
    statusText: string
  ) {
    super(message, status, statusText, "HEET");
  }
}

interface FetchApplicationsParams {
  pladaServiceIds?: string[];
  orgLevel4?: string;
  orgLevel5?: string;
}

// API Configuration for HEET
const HEET_API_CONFIG = {
  ...DEFAULT_API_CONFIG,
  headers: {
    ...DEFAULT_API_CONFIG.headers,
  },
  defaultParams: {
    level1: "TEST",
    level2: "CB & Tech",
    level3: "MB&G",
  },
} as const;

// Create organization cache with the API cache utility
const organizationCache = createApiCache<HeetOrganizationApiResponse[]>(24 * 60 * 60 * 1000); // 24 hours

// Helper function to make HEET-specific API requests
async function makeHeetRequest<T>(url: string, options: RequestInit = {}): Promise<T> {
  return makeRequest<T>(url, options, HEET_API_CONFIG, "HEET");
}

/**
 * HEET Service example showing proper environment variable usage
 */
export class HEETService {
  private apiUrl: string;
  
  constructor(private context?: AppLoadContext) {
    // Get API URL from environment, with context if available
    this.apiUrl = getHeetApiUrl(context);
    console.log('[HEET Service] Initialized with API URL:', this.apiUrl);
  }

  /**
   * Transforms HEET API response to database format
   */
  static transformApplication(data: HeetApplicationApiResponse) {
    return {
      applicationInstanceId: data.applicationInstanceId,
      name: data.name,
      shortName: data.shortName,
      description: data.description,
      criticality: data.criticality,
      status: data.status,
      strategicStatus: data.strategicStatus,
      
      // Flatten IT Organization
      orgLevel2: data.itOwningOrganisation.level2,
      orgLevel3: data.itOwningOrganisation.level3,
      orgLevel4: data.itOwningOrganisation.level4,
      orgLevel5: data.itOwningOrganisation.level5,
      
      pladaServiceId: data.pladaServiceId,
      pladaServiceName: data.pladaServiceName,
      
      // Take first owner if exists
      ownerPsid: data.pladaOwners[0]?.psid,
      ownerDisplayName: data.pladaOwners[0]?.displayName,
      ownerEmail: data.pladaOwners[0]?.emailAddress,
      
      // Take first delegate if exists
      delegatePsid: data.pladaDelegates[0]?.psid,
      delegateDisplayName: data.pladaDelegates[0]?.displayName,
      delegateEmail: data.pladaDelegates[0]?.emailAddress,
    };
  }

  /**
   * Fetches applications from HEET
   * @param params Optional parameters for filtering applications
   * @returns Promise resolving to application data
   */
  static async fetchApplications(params: FetchApplicationsParams = {}, context?: AppLoadContext) {
    const queryParams = buildQueryParams(params, HEET_API_CONFIG.defaultParams);
    const apiUrl = getHeetApiUrl(context);
    const url = `${apiUrl}/applications${
      queryParams.toString() ? `?${queryParams.toString()}` : ""
    }`;

    return makeHeetRequest<HeetApplicationApiResponse[]>(url, {
      method: "GET",
    });
  }

  /**
   * Fetches single application from HEET
   * @param applicationInstanceId The ID of the application to fetch
   * @returns Promise resolving to application data
   */
  static async fetchApplicationById(applicationInstanceId: string, context?: AppLoadContext) {
    const apiUrl = getHeetApiUrl(context);
    const url = `${apiUrl}/applications/${applicationInstanceId}`;

    return makeHeetRequest<HeetApplicationApiResponse>(url, {
      method: "GET",
    });
  }

  /**
   * Fetches organization data from the HEET API with caching
   * @returns Promise resolving to validated organization data
   * @throws Error if the API request fails
   */
  static async fetchOrganizations(context?: AppLoadContext): Promise<HeetOrganizationApiResponse[]> {
    try {      
      // Check cache first
      const cachedData = organizationCache.get();
      if (cachedData) {
        return cachedData;
      }
      
      // Get API URL from environment, with context if available
      const apiUrl = getHeetApiUrl(context);
      console.log('[HEET Service] Fetching organizations from:', apiUrl);

      console.time("HEET Organization API Call");
      const params = buildQueryParams(
        { level: "5" }, 
        HEET_API_CONFIG.defaultParams
      );

      const url = `${apiUrl}/organisations?${params.toString()}`;
      const rawData = await makeHeetRequest(url, {
        method: "GET",
      });

      console.timeEnd("HEET Organization API Call");

      // Parse and validate the response
      const validation = HeetOrganizationApiResponseSchema.safeParse(rawData);
      if (!validation.success) {
        console.error("[HEET Service] Validation error:", validation.error);
        throw new Error("Invalid organization data received from HEET API");
      }

      // Update cache
      organizationCache.set(validation.data);

      return validation.data;
    } catch (error) {
      console.error('[HEET Service] Error fetching organizations:', error);
      throw error;
    }
  }
}

// Helper to create the service with proper context
export function createHeetService(context?: AppLoadContext): HEETService {
  return new HEETService(context);
} 