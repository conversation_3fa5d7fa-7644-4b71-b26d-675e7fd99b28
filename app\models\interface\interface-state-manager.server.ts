/**
 * Simplified registry to track interface states
 */
export class InterfaceStateManager {
    private static instance: InterfaceStateManager;
    private states: Map<string, {
        state: string,
        startTime: Date
    }> = new Map();
    
    private constructor() {}
    
    /**
     * Get singleton instance
     */
    public static getInstance(): InterfaceStateManager {
        if (!InterfaceStateManager.instance) {
            InterfaceStateManager.instance = new InterfaceStateManager();
        }
        return InterfaceStateManager.instance;
    }
    
    /**
     * Update state for an interface
     */
    public updateState(interfaceId: string, state: string): void {
        this.states.set(interfaceId, {
            state,
            startTime: new Date()
        });
    }
    
    /**
     * Get current state for an interface
     */
    public getState(interfaceId: string): string | null {
        return this.states.get(interfaceId)?.state ?? null;
    }
    
    /**
     * Check if an interface has an active state
     */
    public hasActiveState(interfaceId: string): boolean {
        return this.states.has(interfaceId);
    }
    
    /**
     * Get all active states
     */
    public getActiveStates(): Array<{ 
        interfaceId: string, 
        state: string,
        startTime: Date 
    }> {
        return Array.from(this.states.entries()).map(([interfaceId, data]) => ({
            interfaceId,
            state: data.state,
            startTime: data.startTime
        }));
    }
    
    /**
     * Clear state for an interface
     */
    public clearState(interfaceId: string): void {
        this.states.delete(interfaceId);
    }
    
    /**
     * Clear all states
     */
    public clearAll(): void {
        this.states.clear();
    }
}

// Export singleton instance getter with backward compatible name
export const getInterfaceStateRegistry = () => InterfaceStateManager.getInstance(); 