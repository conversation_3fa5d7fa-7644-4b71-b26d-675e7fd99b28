# OMS v3 Technical Reference Guide

This technical reference focuses on key aspects of the OMS v3 system, including dashboard components, dataset SLA configuration, synchronization mechanisms, and status calculation logic.

## Dashboard

The Dashboard provides an operational overview with several key components:

### Status Summary Cards
- Three status cards showing total counts of:
  - **Applications**: Total monitored applications in the system
  - **Interfaces**: Active system interface connections
  - **Datasets**: Data collections associated with interfaces

### Interface Status Distribution
- **Visual Chart**: Bar chart displaying distribution of interface statuses
- **Status Categories**:
  - Red (SLA Breached): Interfaces with breached service level agreements
  - Amber (SLA at Risk): Interfaces approaching SLA breach thresholds
  - Green (On Schedule): Interfaces operating within SLA parameters

### Status Breakdown Cards
- **SLA Breached Card**: Shows count and percentage of breached interfaces
- **SLA at Risk Card**: Shows count and percentage of at-risk interfaces
- **On Schedule Card**: Shows count and percentage of on-schedule interfaces

## Dataset SLA Configuration

The system supports advanced SLA configuration with multiple recurrence patterns:

### Recurrence Patterns
- **Daily**: Set SLAs that repeat every X day(s)
- **Weekly**: Configure SLAs to repeat on specific days of the week (e.g., Monday, Wednesday, Friday)
- **Monthly**: Set SLAs that repeat on specific days of the month or specific weekdays (e.g., first Monday)
- **Yearly**: Configure SLAs that repeat on specific days of specific months or specific weekday patterns

### SLA Time Configuration
- **Time Format**: HH:MM:SS in UTC timezone
- **Interval Options**: Configure SLAs to repeat every X units (days, weeks, months, years)
- **End Conditions**: Set SLAs to end after specific occurrences or on a specific date, or never end

### Bulk SLA Updates
- Select multiple datasets at once to apply the same SLA configuration
- Updates automatically recalculate expected arrival times for all selected datasets
- SLA data is stored in RRULE format (e.g., `FREQ=DAILY;TZID=UTC;INTERVAL=1;BYHOUR=14;BYMINUTE=30;BYSECOND=0`)

## Interface and Dataset Synchronization

### Interface Synchronization
- Data is fetched from external systems (HEET for applications, DLAS for interfaces)
- Synchronization can be performed:
  - **Manually**: Using the "Sync" button on interface pages or "Sync All Interfaces" button
  - **Automatically**: According to the interval set in Settings (1-60 minutes)

### Sync Process
1. Interfaces sync status is set to "PENDING" during synchronization
2. System fetches updated information from external systems
3. Expected arrival times are recalculated for all associated datasets
4. Events are synced from DLAS using the related drilldown key
5. Sync status is updated to "SUCCESS" or "ERROR" upon completion

### Bulk Sync Options
- **Single Interface Sync**: Updates a specific interface and its datasets
- **Multiple Interface Sync**: Select multiple interfaces for batch synchronization
- **Auto-Refresh Toggle**: Enable/disable automatic synchronization at the configured interval

## RAG Status Calculation

### Dataset RAG Status Logic
- **Red (SLA Breached)**: Dataset is expected today, but:
  - Expected arrival time has passed AND
  - Dataset has not arrived OR arrived after the expected time
- **Amber (SLA at Risk)**: Dataset is expected within 30 minutes and:
  - Has not yet arrived today
- **Green (On Schedule)**: All other scenarios (not breached or at risk)

### Interface RAG Status Logic
- **Red**: If any dataset included in RAG calculation is in "red" status
- **Amber**: If any dataset included in RAG calculation is in "amber" status (and none are red)
- **Green**: If all datasets included in RAG calculation are in "green" status

### RAG Inclusion Toggle
- Datasets can be individually included/excluded from RAG status calculations
- The `includeInRagStatus` flag determines if a dataset affects the parent interface's status
- Bulk toggle functionality allows updating multiple datasets at once

## Arrival Time Calculation

### Expected Arrival Time
- Calculated based on the dataset's SLA RRULE configuration
- Uses RRule library to determine the next or most recent occurrence
- For daily SLAs, calculates the expected time for the current day
- For weekly, monthly, and yearly patterns, finds the next occurrence based on the pattern

### Last Arrival Time
- Updated when a dataset synchronization event is recorded
- Used in comparison with expected arrival time to determine if SLAs are being met
- Stored in UTC format for consistent time zone handling

## Data Source Integration

### HEET Integration
- Provides application data including:
  - Application details
  - Organizational structure (Level 2-5 hierarchy)
  - PLADA service IDs and metadata

### DLAS Integration
- Provides interface and dataset information:
  - Interface connections between applications
  - Dataset details associated with interfaces
  - Event IDs and event logging

### Synchronization Architecture
- Uses retry mechanisms for resilient external API calls
- Implements caching to reduce API load
- Monitors memory consumption during large sync operations
- Employs batch processing for large datasets 