import { Clock } from 'lucide-react';
import type { InterfaceWithRagStatus } from '~/models/interface/interface-rag-status.server';

interface InterfaceListInfoProps {
  interfaces: InterfaceWithRagStatus[];
  hasSearchQuery: boolean;
  lastUpdated?: string;
}

export function InterfaceListInfo({
  interfaces,
  hasSearchQuery,
  lastUpdated
}: InterfaceListInfoProps) {
  return (
    <div>
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground mb-2">
          {interfaces.length === 0 && hasSearchQuery
            ? "No interfaces found. Try adjusting your search."
            : interfaces.length === 0
            ? "Please search for interfaces using the search field above."
            : `Showing ${interfaces.length} ${interfaces.length === 1 ? "interface" : "interfaces"}`}
        </p>
        
        {lastUpdated && interfaces.length > 0 && (
          <div className="flex items-center text-xs text-muted-foreground">
            <Clock className="mr-1 h-3 w-3" />
            Last updated: {new Date(lastUpdated).toLocaleString()}
          </div>
        )}
      </div>
    </div>
  );
} 