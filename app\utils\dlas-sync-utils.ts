import { DLASSyncState, SyncProgress, SyncStatus } from '~/types/dlas-sync';

/**
 * Format date and time for display (YYYY-MM-DD HH:MM)
 */
export const formatDateTimeDisplay = (dateString: string | null): string => {
  if (!dateString) return 'N/A';
  
  const date = new Date(dateString);
  return date.toLocaleString([], { 
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/(\d+)\/(\d+)\/(\d+)/, '$3-$1-$2');
};

/**
 * Calculate progress percentage based on dataset completion
 */
export const calculateProgressPercent = (progress: SyncProgress | null | undefined): number => {
  if (!progress) return 0;
  
  // If we have dataset information, use that for overall progress
  if (progress.totalDatasets > 0) {
    return Math.round((progress.processedDatasets / progress.totalDatasets) * 100);
  }
  
  // Fallback to application progress if no dataset info
  return progress.totalItems > 0
    ? Math.round((progress.processedItems / progress.totalItems) * 100)
    : 0;
};

/**
 * Create a default sync status object
 */
export const createDefaultStatus = (): SyncStatus => ({
  state: DLASSyncState.IDLE,
  progress: {
    // Application level
    totalItems: 0,
    processedItems: 0,
    successCount: 0,
    errorCount: 0,
    errors: [],
    
    // Interface level
    totalInterfaces: 0,
    processedInterfaces: 0,
    successInterfaces: 0,
    
    // Dataset level
    totalDatasets: 0,
    processedDatasets: 0,
    successDatasets: 0,
    
    // Timing
    startTime: null,
    endTime: null,
    elapsedTimeMs: 0,
  },
});

/**
 * Update status from API data with safe fallbacks
 */
export const createStatusFromApiData = (apiStatus: any, safeProgress: any): SyncStatus => ({
  state: apiStatus.state || DLASSyncState.IDLE,
  progress: {
    // Application level
    totalItems: safeProgress.totalItems || 0,
    processedItems: safeProgress.processedItems || 0,
    successCount: safeProgress.successCount || 0,
    errorCount: safeProgress.errorCount || 0,
    errors: safeProgress.errors || [],
    
    // Interface level
    totalInterfaces: safeProgress.totalInterfaces || 0,
    processedInterfaces: safeProgress.processedInterfaces || 0,
    successInterfaces: safeProgress.successInterfaces || 0,
    
    // Dataset level
    totalDatasets: safeProgress.totalDatasets || 0,
    processedDatasets: safeProgress.processedDatasets || 0,
    successDatasets: safeProgress.successDatasets || 0,
    
    // Timing
    startTime: safeProgress.startTime || null,
    endTime: safeProgress.endTime || null,
    elapsedTimeMs: safeProgress.elapsedTimeMs || 0,
  }
}); 