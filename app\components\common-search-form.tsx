import { Form } from '@remix-run/react';
import { Input } from '~/components/ui/input';
import { Button } from '~/components/ui/button';
import { Search, X } from 'lucide-react';
import { Badge } from '~/components/ui/badge';
import { useState, useEffect, useRef } from 'react';

/**
 * Props for the common search form component
 */
export interface CommonSearchFormProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  hasFilters: boolean;
  onClearFilters: () => void;
  formRef?: React.RefObject<HTMLFormElement>;
  onSubmit: (event: React.FormEvent<HTMLFormElement>) => void;
  placeholder?: string;
  // For displaying active filters
  showActiveFilters?: boolean;
  searchParams?: URLSearchParams;
  setSearchParams?: (params: URLSearchParams) => void;
  removeFilterCallback?: (key: string) => void;
  additionalFilters?: React.ReactNode;
}

/**
 * Common search form component that can be used across the application
 * for searching by ID or name
 */
export function CommonSearchForm({
  searchValue,
  onSearchChange,
  hasFilters,
  onClearFilters,
  formRef,
  onSubmit,
  placeholder = "Search by ID or name... (press Enter to search)",
  showActiveFilters = false,
  searchParams,
  setSearchParams,
  removeFilterCallback,
  additionalFilters
}: CommonSearchFormProps) {
  // Use local state to manage input value
  const [localSearchValue, setLocalSearchValue] = useState(searchValue);
  
  // Use a ref to track if the change is from props or internal
  const isInternalChange = useRef(false);
  
  // Sync local state with prop when searchValue changes
  useEffect(() => {
    if (isInternalChange.current) {
      isInternalChange.current = false;
      return;
    }
    
    setLocalSearchValue(searchValue);
  }, [searchValue]);
  
  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    
    // Flag that we're making an internal change to prevent feedback loops
    isInternalChange.current = true;
    
    // Update internal state
    setLocalSearchValue(value);
    
    // Pass the value to parent component
    onSearchChange(value);
  };
  
  // Default handler for removing query filter if no custom callback provided
  const handleRemoveQueryFilter = () => {
    if (setSearchParams && searchParams) {
      const newParams = new URLSearchParams(searchParams);
      newParams.delete("query");
      setSearchParams(newParams);
      onSearchChange('');
    }
  };

  return (
    <Form ref={formRef} onSubmit={onSubmit} className="space-y-4">
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            name="query"
            placeholder={placeholder}
            className="pl-8"
            value={localSearchValue}
            onChange={handleInputChange}
          />
        </div>
        
        {hasFilters && (
          <Button 
            variant="outline" 
            size="icon"
            onClick={onClearFilters}
            title="Clear filters"
            type="button"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
      
      {/* Active filters display */}
      {showActiveFilters && searchParams && hasFilters && (
        <div className="flex flex-wrap gap-2 mt-2">
          {searchParams.get("query") && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: {searchParams.get("query")}
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-3 w-3 ml-1 p-0" 
                onClick={removeFilterCallback ? () => removeFilterCallback("query") : handleRemoveQueryFilter}
                type="button"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          
          {/* Display level4 filter if present */}
          {searchParams.get("level4") !== "all" && searchParams.get("level4") && (
            <Badge variant="outline" className="flex items-center gap-1">
              Level 4: {searchParams.get("level4")}
              {removeFilterCallback && (
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-3 w-3 ml-1 p-0" 
                  onClick={() => removeFilterCallback("level4")}
                  type="button"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </Badge>
          )}
          
          {/* Display level5 filter if present */}
          {searchParams.get("level5") !== "all" && searchParams.get("level5") && (
            <Badge variant="outline" className="flex items-center gap-1">
              Level 5: {searchParams.get("level5")}
              {removeFilterCallback && (
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-3 w-3 ml-1 p-0" 
                  onClick={() => removeFilterCallback("level5")}
                  type="button"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </Badge>
          )}
          
          {/* Render any additional filters */}
          {additionalFilters}
        </div>
      )}
    </Form>
  );
} 