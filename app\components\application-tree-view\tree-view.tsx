"use client"

import * as React from "react"
import { ChevronR<PERSON>, Folder<PERSON>losed, FolderO<PERSON>, AppWindow } from "lucide-react"
import { cn } from "~/utils/cn"
import { Badge } from "~/components/ui/badge"

export interface RagStatus {
  red: number
  amber: number
  green: number
  none: number
  total: number
}

/**
 * Tree node data structure
 */
export interface TreeNodeData {
  id: string
  name: string
  type: 'level4' | 'level5' | 'application'
  children?: TreeNodeData[]
  status?: RagStatus
  count?: number // Count of applications
}

interface TreeViewProps {
  data: TreeNodeData[]
  onNodeSelect?: (node: TreeNodeData) => void
  className?: string
  initialExpandedIds?: string[]
  activeNodeId?: string
  onToggleNode?: (nodeId: string) => void
}

/**
 * Tree View Component
 */
export function TreeView({ 
  data, 
  onNodeSelect, 
  className,
  initialExpandedIds = [],
  activeNodeId,
  onToggleNode
}: TreeViewProps & { activeNodeId?: string }) {
  // Remove the internal expandedIds state completely
  // Instead, create a derived value
  const expandedIdsSet = React.useMemo(() => 
    new Set(initialExpandedIds), [initialExpandedIds]);
  
  const toggleNode = (nodeId: string) => {
    if (onToggleNode) {
      onToggleNode(nodeId);
    }
    // Remove the else case - no more internal state management
  }

  return (
    <div className={cn(
      "tree-view space-y-1 pb-14", // Increased bottom padding to ensure content is not hidden
      className
    )}>
      {data.map(node => (
        <TreeNode
          key={node.id}
          node={node}
          level={0}
          expandedIds={expandedIdsSet}
          toggleNode={toggleNode}
          onNodeSelect={onNodeSelect}
          isActive={node.id === activeNodeId}
          activeNodeId={activeNodeId}
        />
      ))}
    </div>
  )
}

/**
 * Status Indicator Component
 * Shows colored indicators based on RAG status
 */
function StatusIndicator({ status }: { status?: RagStatus }) {
  if (!status) return null
  
  // Show only one dot based on priority: red > amber > green
  let dotClass = "bg-gray-200"
  let statusTitle = "No status"
  
  if (status.red > 0) {
    dotClass = "bg-destructive"
    statusTitle = `${status.red} interfaces with issues`
  } else if (status.amber > 0) {
    dotClass = "bg-amber-500"
    statusTitle = `${status.amber} interfaces with warnings`
  } else if (status.green > 0) {
    dotClass = "bg-emerald-500"
    statusTitle = `${status.green} interfaces with no issues`
  }
  
  return (
    <div className="flex items-center ml-auto">
      <span 
        className={`h-2 w-2 rounded-full ${dotClass}`}
        title={statusTitle}
      />
    </div>
  )
}

/**
 * Get badge variant based on RAG status
 */
function getBadgeVariant(node: TreeNodeData) {
  if (node.status) {
    if (node.status.red > 0) return "destructive"
    if (node.status.amber > 0) return "secondary"
    if (node.status.green > 0) return "default"
  }
  return "default"
}

/**
 * TreeNode Component
 */
interface TreeNodeProps {
  node: TreeNodeData
  level: number
  expandedIds: Set<string>
  toggleNode: (nodeId: string) => void
  onNodeSelect?: (node: TreeNodeData) => void
  isActive?: boolean
  activeNodeId?: string
}

function TreeNode({ 
  node, 
  level, 
  expandedIds, 
  toggleNode, 
  onNodeSelect,
  isActive = false,
  activeNodeId
}: TreeNodeProps) {
  const hasChildren = node.children && node.children.length > 0
  
  // Simplified check - we now always use a Set
  const isExpanded = hasChildren && expandedIds.has(node.id)
  
  const indentation = `${level * 16}px`
  
  // Determine node icon based on type and expanded state
  const getNodeIcon = () => {
    if (node.type === 'application') {
      return <AppWindow className="h-4 w-4 text-muted-foreground" />
    }
    
    if (isExpanded) {
      return <FolderOpen className="h-4 w-4 text-muted-foreground" />
    }
    
    return <FolderClosed className="h-4 w-4 text-muted-foreground" />
  }
  
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    
    if (hasChildren) {
      toggleNode(node.id)
    }
    
    if (onNodeSelect) {
      onNodeSelect(node)
    }
  }
  
  const handleChevronClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (hasChildren) {
      console.log('Chevron clicked on node:', node.id, 'Type:', node.type, 'Current expanded state:', isExpanded);
      toggleNode(node.id)
    }
  }
  
  return (
    <div>
      <div 
        className={cn(
          "group flex items-center py-1 px-2 rounded-md cursor-pointer text-sm",
          "hover:bg-accent",
          isActive && "bg-accent" // Highlight if this is the active node
        )}
        style={{ paddingLeft: indentation }}
        onClick={handleClick}
      >
        {/* Expand/collapse chevron */}
        <div 
          className={cn(
            "w-4 h-4 mr-1 flex items-center justify-center",
            !hasChildren && "invisible",
            isExpanded && "transform rotate-90 transition-transform"
          )}
          onClick={handleChevronClick}
        >
          <ChevronRight className="h-3 w-3" />
        </div>
        
        {/* Node icon */}
        <span className="mr-1.5">{getNodeIcon()}</span>
        
        {/* Node name */}
        <span className="truncate">{node.name}</span>
        
        {/* Status indicator on the right */}
        <StatusIndicator status={node.status} />
      </div>
      
      {/* Children nodes */}
      {isExpanded && hasChildren && (
        <div className={cn("tree-view space-y-1")}>
          {node.children!.map(childNode => (
            <TreeNode
              key={childNode.id}
              node={childNode}
              level={level + 1}
              expandedIds={expandedIds}
              toggleNode={toggleNode}
              onNodeSelect={onNodeSelect}
              isActive={childNode.id === activeNodeId}
              activeNodeId={activeNodeId}
            />
          ))}
        </div>
      )}
    </div>
  )
}