import { ApplicationModel } from '~/models/application.server';
import { InterfaceModel } from '~/models/interface.server';
import { InterfaceService } from '~/services/interfaces.server';
import { DLASSyncProgressTracker } from './dlas-sync-progress.server';
import { DatasetModel } from '~/models/dataset.server';
import { getInterfaceStateRegistry } from '~/models/interface/interface-state-manager.server';
import { DLASSyncConfig } from './dlas-sync-config.server';
import { Logger } from '~/utils/sync-utils.server';
import { chunk } from '~/utils/sync-utils.server';

export interface DLASSyncResult {
  success: boolean;
  message?: string;
}

/**
 * Core processor for DLAS sync operations
 * Handles the actual synchronization logic
 */
export class DLASSyncProcessor {
  private isRunning: boolean = false;
  private defaultConfig: DLASSyncConfig = {
    enabled: true,
    batchSize: 100,
    chunkSize: 10,
    delayBetweenBatches: 1000,
    maxConcurrentOperations: 5,
    autoRetry: true,
    maxRetries: 3,
    retryDelay: 1000
  };
  private config: DLASSyncConfig;
  private abortController: AbortController = new AbortController();
  private logger: Logger;
  private progressTracker: DLASSyncProgressTracker;
  private interfaceDatasetCountMap: Map<string, number> = new Map();
  private stateManager = getInterfaceStateRegistry();
  
  constructor(progressTracker: DLASSyncProgressTracker) {
    this.logger = new Logger('DLAS Processor');
    this.progressTracker = progressTracker;
    this.isRunning = false;
    this.config = { ...this.defaultConfig };
    this.interfaceDatasetCountMap = new Map();
  }
  
  /**
   * Get total dataset count for specific application or all applications
   */
  private async getTotalDatasetCount(applicationId?: string): Promise<number> {
    try {
      if (applicationId) {
        // Get application with its interface count
        const application = await ApplicationModel.findById(applicationId);
        if (!application) return 0;

        // Get all interfaces for this application to sum their dataset counts
        const interfaces = await InterfaceModel.findAll({
          appId: applicationId
        });
        
        // Store dataset counts in the map and sum up dataset counts from all interfaces
        const totalDatasets = interfaces.reduce((sum, interface_) => {
          const datasetCount = interface_.datasetCount || 0;
          this.interfaceDatasetCountMap.set(interface_.omsInterfaceId, datasetCount);
          return sum + datasetCount;
        }, 0);
        
        this.logger.log(`Found ${totalDatasets} datasets for application ${applicationId}`);
        return totalDatasets;
      } else {
        // Get all interfaces to populate the dataset count map
        const allInterfaces = await InterfaceModel.findAll();
        
        // Store dataset counts in the map
        allInterfaces.forEach(interface_ => {
          this.interfaceDatasetCountMap.set(interface_.omsInterfaceId, interface_.datasetCount || 0);
        });
        
        // Get total dataset count across all applications
        const count = await DatasetModel.countByOrgFilters();
        this.logger.log(`Found ${count} total datasets`);
        return count;
      }
    } catch (error) {
      this.logger.error('Error getting dataset count:', error);
      return 0; // Fallback to 0 if count fails
    }
  }
  
  /**
   * Get valid applications based on filter
   */
  private async getValidApplications(applicationId?: string): Promise<any[]> {
    const applications = applicationId 
      ? [await ApplicationModel.findById(applicationId)]
      : await ApplicationModel.findAll();

    // Filter out null result if application not found
    const validApplications = applications.filter(app => app !== null);
    
    if (validApplications.length === 0) {
      this.logger.log(applicationId 
        ? `Application ${applicationId} not found`
        : 'No applications found to sync'
      );
    }

    return validApplications;
  }
  
  /**
   * Initialize progress tracking with counts
   */
  private async initializeProgress(validApplications: any[], applicationId?: string): Promise<void> {
    // Get total interface count from application records
    const totalInterfaces = validApplications.reduce((sum, app) => 
      sum + (app.interfaceCount || 0), 0);

    // Get total dataset count for filtered scope
    const totalDatasets = await this.getTotalDatasetCount(applicationId);
    
    // Initialize progress tracking with all counts
    this.progressTracker.startSync(validApplications.length, totalInterfaces, totalDatasets);
    
    if (validApplications.length > 0) {
      this.logger.log(
        applicationId
          ? `Processing application ${applicationId}: ${totalInterfaces} interfaces, ${totalDatasets} datasets`
          : `Found ${validApplications.length} applications, ${totalInterfaces} interfaces, ${totalDatasets} datasets to sync`
      );
    }
  }
  
  /**
   * Process events for a single interface
   */
  private async processInterfaceEvents(
    interfaceObj: any, 
    signal: AbortSignal
  ): Promise<any | null> {
    if (signal.aborted) return null;

    try {
      if (interfaceObj.relatedDrilldownKey) {
        this.logger.log(`Syncing events for interface: ${interfaceObj.omsInterfaceId} (${interfaceObj.interfaceName})`);
        
        // Update interface state to processing
        this.stateManager.updateState(interfaceObj.omsInterfaceId, 'processing');
        
        const result = await InterfaceService.syncEvents(
          interfaceObj.omsInterfaceId, 
          interfaceObj.relatedDrilldownKey
        );

        if (!result.success) {
          this.logger.warn(`Sync result for interface ${interfaceObj.omsInterfaceId}: ${result.error || 'Unknown error'}`);
          this.progressTracker.recordInterfaceError(
            interfaceObj.omsInterfaceId,
            interfaceObj.interfaceName,
            result.error || 'Unknown error'
          );
          
          // Update state to error
          this.stateManager.updateState(interfaceObj.omsInterfaceId, 'error');
        } else {
          this.logger.log(`Successfully synced events for interface ${interfaceObj.omsInterfaceId}: ${result.newEventCount} new events`);
          this.progressTracker.recordInterfaceSuccess(
            interfaceObj.omsInterfaceId,
            interfaceObj.interfaceName
          );
          
          // Update state to completed
          this.stateManager.updateState(interfaceObj.omsInterfaceId, 'completed');
        }
        return result;
      }
      return null;
    } catch (error) {
      this.logger.error(`Error syncing events for interface ${interfaceObj.omsInterfaceId}:`, error);
      this.progressTracker.recordInterfaceError(
        interfaceObj.omsInterfaceId,
        interfaceObj.interfaceName,
        error
      );
      
      // Update state to error
      this.stateManager.updateState(interfaceObj.omsInterfaceId, 'error');
      return null;
    } finally {
      // Clear state if aborted
      if (signal.aborted) {
        this.stateManager.clearState(interfaceObj.omsInterfaceId);
      }
    }
  }
  
  /**
   * Process interfaces for a single application
   */
  private async processApplicationInterfaces(
    app: any,
    signal: AbortSignal,
    date?: string
  ): Promise<void> {
    if (signal.aborted) return;

    try {
      this.logger.log(`Syncing application: ${app.applicationInstanceId} (${app.name})`);
      
      // Sync interfaces for this application
      await InterfaceModel.syncFromDLAS(app.applicationInstanceId, {
        batchSize: this.config.chunkSize,
        parallelProcessing: true,
        date, // Pass the date parameter
      });
      
      this.logger.log(`Syncing events for interfaces of application: ${app.applicationInstanceId}`);
      
      // Get all interfaces for this application
      const appInterfaces = await InterfaceModel.findAll({
        appId: app.applicationInstanceId
      });
      
      this.logger.log(`Found ${appInterfaces.length} interfaces to sync events for`);
      
      // Process interfaces in batches for parallel event syncing
      const CONCURRENCY_LIMIT = 3;
      
      // Process interfaces in batches to control concurrency
      for (let i = 0; i < appInterfaces.length; i += CONCURRENCY_LIMIT) {
        if (signal.aborted) break;
        
        const batch = appInterfaces.slice(i, i + CONCURRENCY_LIMIT);
        await Promise.all(batch.map(interfaceObj => 
          this.processInterfaceEvents(interfaceObj, signal)
        ));
        
        // Optional: Add a small delay between batches to avoid API rate limits
        if (i + CONCURRENCY_LIMIT < appInterfaces.length && !signal.aborted) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
      
      this.progressTracker.recordApplicationSuccess(
        app.applicationInstanceId,
        app.name
      );
    } catch (error) {
      this.progressTracker.recordApplicationError(
        app.applicationInstanceId,
        app.name,
        error
      );
      this.logger.error(`Failed to sync application: ${app.applicationInstanceId}`, error);
    }
  }
  
  /**
   * Process applications in batches
   */
  private async processApplicationBatches(
    applications: any[],
    signal: AbortSignal,
    date?: string
  ): Promise<void> {
    const batches = chunk(applications, this.config.batchSize);
    
    for (let i = 0; i < batches.length; i++) {
      if (signal.aborted) {
        this.logger.log('Sync aborted');
        break;
      }
      
      const batch = batches[i];
      this.progressTracker.updateBatch(i);
      
      this.logger.log(`Processing batch ${i + 1}/${batches.length} (${batch.length} applications)`);
      
      // Process each application in the batch sequentially
      for (const app of batch) {
        if (signal.aborted) break;
        await this.processApplicationInterfaces(app, signal, date);
      }
      
      // Add delay between batches to avoid overwhelming the API
      if (i < batches.length - 1 && !signal.aborted) {
        await new Promise(resolve => setTimeout(resolve, this.config.delayBetweenBatches));
      }
    }
  }
  
  /**
   * Process the sync job, handling all applications or a specific application
   * @param signal AbortSignal to allow canceling the operation
   * @param applicationId Optional application ID to sync only that application
   * @param date Optional date parameter in YYYY-MM-DD format
   */
  public async processSyncJob(signal: AbortSignal, applicationId?: string, date?: string): Promise<void> {
    try {
      // Get and validate applications
      const validApplications = await this.getValidApplications(applicationId?.toString());
      
      if (validApplications.length === 0) {
        const errorMessage = applicationId 
          ? `No application found with ID: ${applicationId}`
          : 'No applications found to sync';
        
        // Initialize progress tracking with 0 counts to show we attempted to sync
        await this.initializeProgress([], applicationId?.toString());
        
        this.logger.warn(errorMessage);
        this.progressTracker.errorSync(errorMessage);
        return;
      }

      // Initialize progress tracking with actual application counts
      await this.initializeProgress(validApplications, applicationId?.toString());

      // Process applications in batches
      await this.processApplicationBatches(validApplications, signal, date);
    } catch (error) {
      this.logger.error('Error during sync job', error);
      throw error;
    }
  }

  async startSync(config?: Partial<DLASSyncConfig>): Promise<DLASSyncResult> {
    try {
      if (this.isRunning) {
        return {
          success: false,
          message: 'Sync is already running'
        };
      }

      this.isRunning = true;
      this.config = {
        ...this.defaultConfig,
        ...config
      };
      
      // Start sync in background
      void this.processSyncJob(this.abortController.signal);
      
      return {
        success: true,
        message: 'Sync started'
      };
    } catch (error) {
      console.error('Error starting sync:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
} 