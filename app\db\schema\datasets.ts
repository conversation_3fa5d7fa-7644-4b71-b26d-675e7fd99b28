import { sql } from 'drizzle-orm';
import { text, integer, sqliteTable, index } from 'drizzle-orm/sqlite-core';
import { interfaces } from './interfaces';

// Datasets table schema
export const datasets = sqliteTable('datasets', {
  // Primary Key
  datasetName: text('dataset_name').primaryKey(),
  
  // Basic Information
  status: text('status'),
  direction: text('direction'),
  eimInterfaceId: text('eim_interface_id'),
  datasetStatus: text('dataset_status'),
  description: text('description'),
  
  // Interface Information
  interfaceSerial: integer('interface_serial'),
  omsInterfaceId: text('oms_interface_id')
    .references(() => interfaces.omsInterfaceId),
  transferType: text('transfer_type'),
  frequency: text('frequency'),
  
  // Data Classification
  primaryDataTerm: text('primary_data_term'), // Stores the 'name' field
  productType: text('product_type'), // Stored as JSON string
  
  // Business Context Information
  country: text('country'),
  legalEntity: text('legal_entity'),
  legalEntityCode: text('legal_entity_code'),
  lineOfBusinessName: text('line_of_business_name'),
  
  // Related Information
  relatedDrilldownList: text('related_drilldown_list'), // Stored as JSON string
  
  // Event Tracking
  eventCount: integer('event_count').default(0).notNull(),
  
  // RAG Status Configuration
  includeInRagStatus: integer('include_in_rag_status', { mode: 'boolean' }).default(true).notNull(),
  
  // Additional Application-specific fields
  sla: text('sla'),
  lastArrivalTime: integer('last_arrival_time', { mode: 'timestamp' }),
  expectedArrivalTime: integer('expected_arrival_time', { mode: 'timestamp' }),
  
  // Metadata
  createdAt: integer('created_at', { mode: 'timestamp' })
    .default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' })
    .default(sql`CURRENT_TIMESTAMP`),
}, (table) => ({
  // Indexes for frequently queried columns
  omsInterfaceIdIdx: index('datasets_oms_interface_id_idx').on(table.omsInterfaceId),
  interfaceSerialIdx: index('datasets_interface_serial_idx').on(table.interfaceSerial),
  statusIdx: index('datasets_status_idx').on(table.status),
})); 