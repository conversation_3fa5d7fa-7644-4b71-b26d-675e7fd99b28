import { env } from '~/env.server';

/**
 * DLAS Sync Job Configuration
 */
export interface DLASSyncConfig {
  enabled: boolean;
  applicationId?: string;  // Optional application ID for targeted sync
  batchSize: number;
  chunkSize: number;
  delayBetweenBatches: number;
  maxConcurrentOperations: number;
  autoRetry: boolean;
  maxRetries: number;
  retryDelay: number;
}

/**
 * Get default configuration values from environment
 */
export function getDefaultDLASSyncConfig(): DLASSyncConfig {
  const batchSize = process.env.SYNC_BATCH_SIZE 
    ? parseInt(process.env.SYNC_BATCH_SIZE) 
    : 5;
  const chunkSize = process.env.SYNC_CHUNK_SIZE 
    ? parseInt(process.env.SYNC_CHUNK_SIZE) 
    : 20;
  const delayBetweenBatches = process.env.SYNC_DELAY_BETWEEN_BATCHES 
    ? parseInt(process.env.SYNC_DELAY_BETWEEN_BATCHES) 
    : 2000;
  const maxConcurrentOperations = process.env.SYNC_MAX_CONCURRENT_OPERATIONS 
    ? parseInt(process.env.SYNC_MAX_CONCURRENT_OPERATIONS) 
    : 2;
  const autoRetry = process.env.SYNC_AUTO_RETRY 
    ? process.env.SYNC_AUTO_RETRY === 'true' 
    : true;
  const maxRetries = process.env.SYNC_MAX_RETRIES 
    ? parseInt(process.env.SYNC_MAX_RETRIES) 
    : 3;
  const retryDelay = process.env.SYNC_RETRY_DELAY 
    ? parseInt(process.env.SYNC_RETRY_DELAY) 
    : 5000;
  return {
    enabled: true,
    batchSize,
    chunkSize,
    delayBetweenBatches,
    maxConcurrentOperations,
    autoRetry,
    maxRetries,
    retryDelay
  };
}

/**
 * Merge configuration options with defaults
 */
export function mergeDLASSyncConfig(options: Partial<DLASSyncConfig>): DLASSyncConfig {
  return { ...getDefaultDLASSyncConfig(), ...options };
}

/**
 * Helper function to calculate the next scheduled run time based on configured hour and minute
 */
export function getNextScheduledRunTime(): Date {
  const now = new Date();
  const targetTime = new Date(now);
  
  // Set to configured time today
  targetTime.setHours(env.SCHEDULED_SYNC_HOUR, env.SCHEDULED_SYNC_MINUTE, 0, 0);
  
  // If it's already past the target time, set to tomorrow
  if (now > targetTime) {
    targetTime.setDate(targetTime.getDate() + 1);
  }
  
  return targetTime;
} 