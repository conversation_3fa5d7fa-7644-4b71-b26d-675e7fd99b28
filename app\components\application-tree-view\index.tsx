"use client"

import * as React from "react"
import { useNavigate, useLocation } from "@remix-run/react"
import { TreeView, type TreeNodeData } from "./tree-view"
import type { ApplicationWithRagStatus } from "~/models/application.server"
import { cn } from "~/utils/cn"
import { Menu } from "lucide-react"
import { Button } from "~/components/ui/button"
import { useApplicationTreeData } from "~/hooks/use-application-tree-data"

interface ApplicationTreeViewProps {
  applications: ApplicationWithRagStatus[]
  className?: string
  isCollapsed?: boolean
  routeKey: 'applications' | 'interfaces' | 'dashboard'
  preserveState?: boolean
}

/**
 * Application Tree View Component 
 * Shows a hierarchical view of applications organized by org4 and org5 levels
 */
export function ApplicationTreeView({ 
  applications, 
  className,
  isCollapsed = false,
  routeKey,
  preserveState = true
}: ApplicationTreeViewProps) {
  const [collapsed, setCollapsed] = React.useState(isCollapsed)
  const navigate = useNavigate()
  const location = useLocation()
  
  // Use our new hook for tree data and state management
  const {
    treeData,
    expandedIds,
    activeNodeId,
    filters,
    setFilters
  } = useApplicationTreeData({
    applications,
    preserveState,
    routeKey
  })
  
  // Handle node selection
  const handleNodeSelect = (node: TreeNodeData) => {
    const newFilters = { ...filters }
    
    if (node.type === 'level4') {
      newFilters.orgLevel4 = node.name
      newFilters.orgLevel5 = undefined
      newFilters.query = undefined
    } 
    else if (node.type === 'level5') {
      // Find parent level4
      for (const l4 of treeData) {
        const l5 = l4.children?.find(l5 => l5.id === node.id)
        if (l5) {
          newFilters.orgLevel4 = l4.name
          newFilters.orgLevel5 = node.name
          newFilters.query = undefined
          break
        }
      }
    } 
    else if (node.type === 'application') {
      newFilters.orgLevel4 = undefined
      newFilters.orgLevel5 = undefined
      newFilters.query = node.id
    }
    
    setFilters(newFilters)
  }
  
  return (
    <div className={cn(
      "h-[calc(100vh-5rem-1px)] flex flex-col border-r transition-all duration-300 relative",
      collapsed ? "w-12" : "w-72", 
      className
    )}>
      {/* Tree view content */}
      <div className="flex-1 min-h-0">
        {!collapsed ? (
          <div className="h-full overflow-auto">
            <div className="p-1">
              <TreeView 
                data={treeData} 
                onNodeSelect={handleNodeSelect}
                initialExpandedIds={expandedIds}
                activeNodeId={activeNodeId}
              />
            </div>
          </div>
        ) : (
          <div className="h-full"></div>
        )}
      </div>
      
      {/* Collapse/expand button */}
      <div className={cn(
        "sticky bottom-0 left-0 right-0 p-2 border-t bg-background flex items-center shadow-[0_-1px_2px_rgba(0,0,0,0.05)]",
        collapsed ? "justify-center" : "justify-end"
      )}>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setCollapsed(!collapsed)}
          className="h-6 w-6"
          title={collapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          <Menu className="h-3 w-3" />
        </Button>
      </div>
    </div>
  )
}

export * from "./tree-view"
export * from "./application-tree-data" 