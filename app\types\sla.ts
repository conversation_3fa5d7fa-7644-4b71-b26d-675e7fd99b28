/**
 * Represents a day of the week in RRULE format
 */
export type WeekDay = 'MO' | 'TU' | 'WE' | 'TH' | 'FR' | 'SA' | 'SU';

/**
 * Represents a week number in a month
 * -1 represents the last week
 */
export type WeekNumber = 1 | 2 | 3 | 4 | -1;

export type RecurrencePattern = 'daily' | 'weekly' | 'monthly' | 'yearly';
export type EndType = 'never' | 'after' | 'on';

export interface SLABase {
  time: string;
  pattern: RecurrencePattern;
  interval: number;
  endType: EndType;
  endAfterCount?: number;
  endOnDate?: string;
}

export interface DailySLA extends SLABase {
  pattern: 'daily';
}

export interface WeeklySLA extends SLABase {
  pattern: 'weekly';
  weekDays: WeekDay[];
}

export interface MonthlySLA extends SLABase {
  pattern: 'monthly';
  byDay?: {
    week: WeekNumber;
    day: WeekDay;
  };
  byDate?: number;
}

export interface YearlySLA extends SLABase {
  pattern: 'yearly';
  month: number;
  byDay?: {
    week: WeekNumber;
    day: WeekDay;
  };
  byDate?: number;
}

export type SLAConfig = DailySLA | WeeklySLA | MonthlySLA | YearlySLA; 