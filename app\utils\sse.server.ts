import { PassThrough } from "stream";

/**
 * A function that returns an event stream Response which can be used to send server-sent events.
 * 
 * @param request - The incoming request object
 * @param init - Function that initializes the event stream and returns a cleanup function
 * @returns A Response object with a readable stream
 */
export function eventStream(
  request: Request,
  init: (send: (event: { event: string; data: any }) => void, close: () => void) => (() => void) | void
) {
  // Create a new PassThrough stream
  const stream = new PassThrough();
  
  // Flag to track if the stream has ended
  let hasEnded = false;
  
  // Track if cleanup has been called
  let cleanupCalled = false;
  
  // Set up error handler for the stream
  stream.on('error', (err) => {
    console.error('Stream error:', err);
    if (!hasEnded) {
      hasEnded = true;
      stream.end();
    }
  });
  
  // Check if the client closed the connection
  request.signal.addEventListener("abort", () => {
    if (!hasEnded) {
      console.log('Client aborted connection');
      hasEnded = true;
      stream.end();
      
      // Call cleanup if it hasn't been called yet
      if (!cleanupCalled && cleanupFn) {
        cleanupCalled = true;
        try {
          cleanupFn();
        } catch (error) {
          console.error('Error during cleanup after abort:', error);
        }
      }
    }
  });
  
  // Function to send an event to the client with error handling
  const send = (event: { event: string; data: any }) => {
    if (hasEnded) {
      console.log('Attempted to send event to ended stream');
      return;
    }
    
    try {
      const { event: eventName, data } = event;
      // Serialize data if it's an object
      const serializedData = typeof data === "object" ? JSON.stringify(data) : data;
      
      // Format the event according to the SSE spec
      stream.write(`event: ${eventName}\n`);
      stream.write(`data: ${serializedData}\n\n`);
    } catch (error) {
      console.error('Error sending SSE event:', error);
    }
  };
  
  // Function to close the stream
  const close = () => {
    if (!hasEnded) {
      hasEnded = true;
      stream.end();
    }
  };
  
  // Initialize the event stream and get the cleanup function
  let cleanupFn: (() => void) | void;
  
  try {
    cleanupFn = init(send, close);
  } catch (error) {
    console.error('Error initializing event stream:', error);
    close();
  }
  
  // Set up a heartbeat to keep the connection alive
  const heartbeatInterval = setInterval(() => {
    if (!hasEnded) {
      try {
        stream.write(`:heartbeat\n\n`);
      } catch (error) {
        console.error('Error sending heartbeat:', error);
        clearInterval(heartbeatInterval);
        close();
      }
    } else {
      clearInterval(heartbeatInterval);
    }
  }, 30000); // Send heartbeat every 30 seconds
  
  // Set up clean-up for the heartbeat and resources
  // Clean up when the request is aborted or the stream ends
  const cleanup = () => {
    clearInterval(heartbeatInterval);
    
    if (!hasEnded) {
      hasEnded = true;
      stream.end();
    }
    
    // Call cleanup if it hasn't been called yet
    if (!cleanupCalled && cleanupFn) {
      cleanupCalled = true;
      try {
        cleanupFn();
      } catch (error) {
        console.error('Error during cleanup:', error);
      }
    }
  };
  
  // Set up additional cleanup listeners
  stream.on('end', cleanup);
  stream.on('close', cleanup);
  
  // Return a Response with the stream
  return new Response(stream as any, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      "Connection": "keep-alive",
    },
  });
}; 