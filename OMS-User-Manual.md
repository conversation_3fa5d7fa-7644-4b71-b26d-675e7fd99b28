# OMS v3 User Manual

This guide will help you understand and use the OMS v3 system for monitoring your applications, interfaces, and datasets.

## Main Views

### Dashboard View

![Dashboard](https://placeholder.for/dashboard-screenshot)

The Dashboard is your central command center where you can see:

- **Status Summary Cards**: Shows you the total counts of applications, interfaces, and datasets in your system
- **Interface Status Chart**: Visual representation of how many interfaces are on schedule (green), at risk (yellow), or breached (red)
- **Status Breakdown Cards**: Detailed cards showing the percentage and count of interfaces in each status category

Use the Dashboard to quickly identify problems that need your attention, especially interfaces with red or amber status.

### Applications View

![Applications](https://placeholder.for/applications-screenshot)

The Applications view lists all the applications in your system. Here you can:

- View application details including name, type, status, and organizational information
- Filter applications by organization level (Level 4/Level 5)
- See how many interfaces are connected to each application
- Access PLADA service information when available
- Sync application data with the HEET system

#### Searching for Applications

To find specific applications:
1. Enter search terms in the search box at the top of the Applications page
2. You can search by application name, ID, or PLADA service ID
3. Press Enter to execute the search
4. Use the organization filters on the right to narrow results further
5. Click the X button to clear all filters

### Interfaces View

![Interfaces](https://placeholder.for/interfaces-screenshot)

The Interfaces view shows all connections between your applications. Here you can:

- See interface details including source and target applications
- Check the status of each interface (green, yellow, or red)
- View when data was last synchronized
- Access datasets connected to each interface
- Manually sync interface data or configure auto-sync

#### Searching for Interfaces

To find specific interfaces:
1. Enter search terms in the search box at the top of the Interfaces page
2. You can search by interface name, ID, or related application name
3. Press Enter to execute the search
4. Use the filter controls to filter by:
   - Organization level (Level 4/Level 5)
   - Status (Red/Amber/Green)
   - Related application
5. Click the X button to clear all filters

### Datasets View

![Datasets](https://placeholder.for/datasets-screenshot)

The Datasets view (accessed through an interface) shows you the data collections associated with an interface. Here you can:

- View dataset status and timing information
- Configure SLA settings for when data should arrive
- Include or exclude datasets from status calculations
- View events associated with each dataset
- Update SLA settings for multiple datasets at once

### Events View

![Events](https://placeholder.for/events-screenshot)

The Events view shows a chronological record of activities related to interfaces and datasets. Here you can:

- See when data arrived
- Check if events were successful or had errors
- Filter events by date, type, or status
- Track the history of data transfers

### API Documentation View

![API Documentation](https://placeholder.for/api-docs-screenshot)

The API Documentation view provides interactive documentation for all available API endpoints in the system. Here you can:

- Browse available API endpoints and understand their purpose
- See detailed parameter information for each endpoint
- Test API endpoints directly from the documentation
- View sample responses and error codes
- Copy API responses to your clipboard

#### Using the API Documentation

1. Navigate to the API Documentation page from the sidebar
2. Browse the list of available endpoints
3. Click on an endpoint to expand its details
4. Switch to the "Try It" tab to test the endpoint
5. Fill in the required parameters
6. Click "Send Request" to test the endpoint
7. View the response and copy it using the "Copy" button

The following API endpoints are available:

- **Applications Sync API** (`POST /api/applications/sync`): Synchronize applications from HEET and their interfaces from DLAS
- **DLAS Sync GET API** (`GET /api/dlas-sync`): Get current DLAS synchronization status and configuration
- **DLAS Sync POST API** (`POST /api/dlas-sync`): Control DLAS synchronization operations

### Settings View

![Settings](https://placeholder.for/settings-screenshot)

The Settings view allows you to configure system behavior:

- Set how many items show in Quick Access (1-20 items)
- Configure how often data automatically syncs (1-60 minutes)
- Reset settings to defaults if needed

## Key Features

### Dataset SLA Configuration

SLAs (Service Level Agreements) determine when datasets should arrive. The system supports various recurrence patterns:

#### Setting Up SLAs

1. Go to an interface's Datasets tab
2. Select one or more datasets
3. Click "Update SLA"
4. Configure the following options:

**Time Settings:**
- Enter the expected arrival time in HH:MM:SS format (in UTC timezone)

**Recurrence Pattern:**
- **Daily**: Set datasets to arrive every X day(s)
- **Weekly**: Configure arrivals on specific days of the week (e.g., Monday, Wednesday, Friday)
- **Monthly**: Set arrivals on specific days of the month or specific weekdays (e.g., first Monday)
- **Yearly**: Configure arrivals on specific days of specific months

**Interval Options:**
- Specify how often the pattern repeats (every 1 day, every 2 weeks, etc.)

**End Conditions:**
- Never end
- End after X occurrences
- End on a specific date

The system stores these settings in RRULE format, which looks like:
`FREQ=DAILY;TZID=UTC;INTERVAL=1;BYHOUR=14;BYMINUTE=30;BYSECOND=0`

### Interface and Dataset Synchronization

#### Manual Synchronization

To manually sync an interface:
1. Go to the Interfaces view
2. Find the interface you want to sync
3. Click the sync button (circular arrows icon)
4. Wait for the sync to complete

To sync multiple interfaces at once:
1. Select the checkboxes next to multiple interfaces
2. Click the "Sync X Interfaces" button that appears
3. The system will update all selected interfaces

#### Automatic Synchronization

To enable automatic synchronization:
1. Toggle the "Auto-refresh ON/OFF" button in the Interfaces view
2. Data will refresh according to the interval set in Settings
3. You can also adjust this interval in the Settings view

#### What Happens During Sync

When syncing occurs:
1. The system connects to external systems (HEET and DLAS)
2. It fetches the latest application and interface information
3. It recalculates when datasets should arrive
4. It retrieves event information for tracking arrival times
5. Status indicators update based on the latest information

### Status Calculation and Monitoring

The system uses a Red/Amber/Green (RAG) status system to help you monitor your interfaces and datasets:

#### Dataset Status Logic

- **Red (SLA Breached)**: The dataset was expected today, but has either:
  - Not arrived yet and the expected time has passed, or
  - Arrived later than the expected time
- **Amber (SLA at Risk)**: The dataset is expected within the next 30 minutes but hasn't arrived yet
- **Green (On Schedule)**: The dataset is on schedule (either has arrived on time or isn't yet due)

#### Interface Status Logic

An interface's status is determined by its datasets:
- **Red**: If any dataset included in the calculation is in "red" status
- **Amber**: If any dataset is in "amber" status (and none are red)
- **Green**: If all included datasets are in "green" status

#### Controlling Which Datasets Affect Status

You can control which datasets affect an interface's status:
1. Go to the Datasets tab for an interface
2. Find the dataset you want to include/exclude
3. Toggle the inclusion switch
4. The interface status will now be calculated based on your selection

To update multiple datasets at once:
1. Select several datasets using the checkboxes
2. Click "Toggle RAG Inclusion"
3. Choose whether to include or exclude the selected datasets

### Arrival Time Calculation

The system tracks two important times for each dataset:

#### Expected Arrival Time

- Calculated from the dataset's SLA settings
- For daily patterns, it calculates today's expected time
- For weekly, monthly, and yearly patterns, it finds the next expected occurrence
- All times are in UTC format for consistency across time zones

#### Last Arrival Time

- Records when data actually arrived
- Updated when a synchronization event is detected
- Used to determine if data arrived on time
- Stored in UTC format

The system compares these times to determine if SLAs are being met and calculates the appropriate status color.

## API Integration

### Using the OMS APIs

OMS v3 provides a set of RESTful APIs that allow you to programmatically interact with the system:

#### Available APIs

- **Applications Sync API**: Trigger synchronization of applications and their interfaces
- **DLAS Sync API**: Control and monitor the DLAS synchronization process

#### Authentication and Requests

All API requests are made using standard HTTP methods with form data or query parameters. The system uses the same authentication as the web interface.

#### Testing APIs

You can test all available APIs directly from the API Documentation page without writing any code. This allows you to:
1. Understand the required parameters
2. See sample request formats
3. View sample responses
4. Copy response data for use in your applications

## Data Sources

The OMS system integrates with two main external systems:

### HEET Integration

HEET provides application information including:
- Application details and status
- Organizational structure (Level 2-5 hierarchy)
- PLADA service IDs and metadata

### DLAS Integration

DLAS provides interface and dataset information:
- Interface connections between applications
- Dataset details and status
- Event IDs and event logging

These integrations ensure that the OMS system has the latest information about your applications and interfaces. 