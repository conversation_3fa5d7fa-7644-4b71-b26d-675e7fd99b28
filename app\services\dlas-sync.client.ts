/**
 * Client-side constants and types for DLAS sync
 * This file contains only types and constants that are safe to use on the client
 */

/**
 * DLAS Sync Job States
 */
export const DLASSyncState = {
  IDLE: 'idle',
  RUNNING: 'running',
  PAUSED: 'paused',
  ERROR: 'error',
} as const;

export type DLASSyncState = typeof DLASSyncState[keyof typeof DLASSyncState];

/**
 * DLAS Sync Job Configuration
 */
export interface DLASSyncConfig {
  batchSize: number;
  chunkSize: number;
  delayBetweenBatches: number;
  maxConcurrentOperations: number;
  autoRetry: boolean;
  maxRetries: number;
  retryDelay: number;
}

/**
 * DLAS Sync Progress Status
 */
export interface DLASSyncProgress {
  state: DLASSyncState;
  totalItems: number;
  processedItems: number;
  successCount: number;
  errorCount: number;
  skippedCount: number;
  currentBatch: number;
  totalBatches: number;
  errors: Array<{ message: string, item?: string }>;
  startTime: string | null;
  endTime: string | null;
  elapsedTimeMs: number;
  estimatedTimeRemainingMs: number | null;
} 