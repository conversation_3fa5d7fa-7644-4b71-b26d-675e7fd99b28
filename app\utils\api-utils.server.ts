import { withRetry, RetryError } from "~/utils/retry.server";
import type { AppLoadContext } from '@remix-run/node';
import { 
  getApiRetryMaxAttempts, 
  getApiRetryInitialDelay, 
  getApiRetryMaxDelay 
} from '~/utils/env.server';

/**
 * Generic API Error class that can be extended by specific services
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public statusText: string,
    public serviceName: string = "API"
  ) {
    super(message);
    this.name = `${serviceName}ApiError`;
  }
}

/**
 * API Configuration interface
 */
export interface ApiConfig {
  headers: Record<string, string>;
  retry: {
    maxAttempts: number;
    initialDelay: number;
    maxDelay: number;
    shouldRetry: (error: unknown) => boolean;
  };
}

/**
 * Default API configuration that can be extended by specific services
 */
export const DEFAULT_API_CONFIG: ApiConfig = {
  headers: {
    "Content-Type": "application/json",
  },
  retry: {
    maxAttempts: 3,
    initialDelay: 1000,
    maxDelay: 5000,
    shouldRetry: (error: unknown) => {
      // Don't retry on 4xx errors (client errors)
      if (error instanceof ApiError && error.status >= 400 && error.status < 500) {
        return false;
      }
      return true;
    },
  },
};

/**
 * Get API configuration with values from environment variables
 */
export function getApiConfig(context?: AppLoadContext): ApiConfig {
  return {
    headers: {
      "Content-Type": "application/json",
    },
    retry: {
      maxAttempts: getApiRetryMaxAttempts(context),
      initialDelay: getApiRetryInitialDelay(context),
      maxDelay: getApiRetryMaxDelay(context),
      shouldRetry: (error: unknown) => {
        // Don't retry on 4xx errors (client errors)
        if (error instanceof ApiError && error.status >= 400 && error.status < 500) {
          return false;
        }
        return true;
      },
    },
  };
}

/**
 * Generic function to make API requests with retry logic
 * @param url The URL to make the request to
 * @param options Request options
 * @param config API configuration including retry logic
 * @param serviceName Name of the service making the request (for error reporting)
 * @returns Promise resolving to the JSON response
 */
export async function makeRequest<T>(
  url: string, 
  options: RequestInit = {}, 
  config: ApiConfig = DEFAULT_API_CONFIG,
  serviceName: string = "API",
  context?: AppLoadContext
): Promise<T> {
  // Use environment-based config if no specific config is provided
  const apiConfig = config === DEFAULT_API_CONFIG ? getApiConfig(context) : config;
  
  try {
    const response = await withRetry(
      async () => {
        const res = await fetch(url, {
          ...options,
          headers: {
            ...apiConfig.headers,
            ...options.headers,
          },
        });

        if (!res.ok) {
          throw new ApiError(
            "API request failed",
            res.status,
            res.statusText,
            serviceName
          );
        }

        return res;
      },
      apiConfig.retry
    );

    return response.json();
  } catch (error) {
    if (error instanceof RetryError) {
      const lastError = error.lastError;
      if (lastError instanceof ApiError) {
        throw lastError;
      }
    }
    
    throw new ApiError(
      error instanceof Error ? error.message : "Unknown error occurred",
      500,
      "Internal Server Error",
      serviceName
    );
  }
}

/**
 * Helper function to build query parameters from an object
 */
export function buildQueryParams(params: Record<string, any>, defaultParams: Record<string, string> = {}): URLSearchParams {
  const queryParams = new URLSearchParams();
  
  // Add default parameters
  Object.entries(defaultParams).forEach(([key, value]) => {
    queryParams.append(key, value);
  });

  // Add additional parameters
  Object.entries(params).forEach(([key, value]) => {
    if (value === undefined || value === null || value === '') return;
    
    if (Array.isArray(value)) {
      value.forEach(item => {
        if (item !== undefined && item !== null && item !== '') {
          queryParams.append(key, item.toString());
        }
      });
    } else if (value !== 'all') {
      queryParams.append(key, value.toString());
    }
  });

  return queryParams;
}

/**
 * Creates a simple cache mechanism for API responses
 */
export function createApiCache<T>(cacheDuration: number = 24 * 60 * 60 * 1000) {
  let cache: {
    data: T | null;
    timestamp: number;
  } = {
    data: null,
    timestamp: 0,
  };

  return {
    get: () => {
      const now = Date.now();
      if (cache.data && now - cache.timestamp < cacheDuration) {
        return cache.data;
      }
      return null;
    },
    set: (data: T) => {
      cache = {
        data,
        timestamp: Date.now(),
      };
    },
    clear: () => {
      cache = {
        data: null,
        timestamp: 0,
      };
    }
  };
} 