import { useEffect, useState, useCallback, useRef } from "react";
import { <PERSON>, useFetcher, useNavigate } from "@remix-run/react";
import { Card, CardContent } from "~/components/ui/card";
import { AppWindow, Share2, AlertCircle, AlertTriangle, CheckCircle2, Clock, <PERSON><PERSON>, <PERSON>nO<PERSON>, Building } from "lucide-react";
import type { QuickAccessItem } from "~/utils/quick-access";
import { getQuickAccessItems, formatTimestamp, togglePinnedStatus, updateQuickAccessItemsWithRagStatus } from "~/utils/quick-access";
import { Button } from "~/components/ui/button";

// Helper functions moved outside component
function deriveRagStatus(counts: { red: number; amber: number; green: number } | undefined): string {
  if (!counts) return "green";
  if (counts.red > 0) return "red";
  if (counts.amber > 0) return "amber";
  return "green";
}

function getStatusTooltip(status: string, type: 'Application' | 'Interface'): string {
  const prefix = type === 'Application' ? 'Application' : 'Interface';
  switch (status) {
    case "red":
      return `${prefix} has SLA breached interfaces`;
    case "amber":
      return `${prefix} has interfaces at risk of breaching SLA`;
    case "green":
      return `All ${prefix.toLowerCase()} interfaces are on schedule`;
    default:
      return "Unknown Status";
  }
}

export function QuickAccess() {
  const [items, setItems] = useState<QuickAccessItem[]>([]);
  const ragStatusFetcher = useFetcher();
  const navigate = useNavigate();
  const fetchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isMountedRef = useRef(true);

  // Memoized function to load items from storage
  const loadItems = useCallback(() => {
    if (!isMountedRef.current) return;
    
    const quickAccessItems = getQuickAccessItems();
    setItems(quickAccessItems);
    
    // Clear any pending fetch timeout
    if (fetchTimeoutRef.current) {
      clearTimeout(fetchTimeoutRef.current);
    }
    
    // Debounce the RAG status fetch to prevent rapid successive calls
    fetchTimeoutRef.current = setTimeout(() => {
      if (isMountedRef.current && quickAccessItems.length > 0) {
        fetchRagStatus(quickAccessItems);
      }
    }, 100);
  }, []);

  // Function to fetch RAG status for interfaces and applications
  const fetchRagStatus = useCallback((quickAccessItems: QuickAccessItem[]) => {
    if (!isMountedRef.current || ragStatusFetcher.state === 'submitting') {
      return;
    }

    const interfaceIds: string[] = [];
    const applicationIds: string[] = [];
    
    quickAccessItems.forEach(item => {
      if (item.type === 'Interface' && item.id) {
        interfaceIds.push(item.id);
      } else if (item.type === 'Application' && item.id) {
        applicationIds.push(item.id);
      }
    });
    
    if (interfaceIds.length === 0 && applicationIds.length === 0) {
      return;
    }

    try {
      const formData = new FormData();
      interfaceIds.forEach(id => formData.append('interfaceIds[]', id));
      applicationIds.forEach(id => formData.append('applicationIds[]', id));
      
      ragStatusFetcher.submit(formData, {
        method: 'post',
        action: '/api/quick-access-rag-status',
        preventScrollReset: true,
      });
    } catch (error) {
      console.error('Error fetching RAG status:', error);
    }
  }, [ragStatusFetcher]);

  const handleTogglePin = useCallback((e: React.MouseEvent, item: QuickAccessItem) => {
    e.preventDefault();
    e.stopPropagation();
    
    const newPinnedStatus = togglePinnedStatus(item.id, item.type);
    
    setItems(prevItems => {
      if (newPinnedStatus) {
        return prevItems.map(i => 
          i.id === item.id && i.type === item.type 
            ? { ...i, pinned: true } 
            : i
        );
      } else {
        return prevItems.filter(i => 
          !(i.id === item.id && i.type === item.type)
        );
      }
    });
  }, []);

  // Handle navigation to item with org levels preserved
  const handleNavigation = useCallback((e: React.MouseEvent, item: QuickAccessItem) => {
    e.preventDefault();
    
    // If item has org level information, include it in navigation
    if (item.orgLevel4 || item.orgLevel5) {
      const [basePath, existingParams = ''] = item.path.split('?');
      const searchParams = new URLSearchParams(existingParams);
      
      // Add org level parameters if they exist in the item
      if (item.orgLevel4) {
        searchParams.set('level4', item.orgLevel4);
      }
      if (item.orgLevel5) {
        searchParams.set('level5', item.orgLevel5);
      }
      
      // Build the new path with org levels
      const queryString = searchParams.toString();
      const navigatePath = queryString ? `${basePath}?${queryString}` : basePath;
      
      navigate(navigatePath);
    } else {
      // Default navigation if no org levels
      navigate(item.path);
    }
  }, [navigate]);

  const renderRagStatusBar = useCallback((item: QuickAccessItem) => {
    if (!item.ragStatus) {
      return null;
    }

    const { red, amber, green } = item.ragStatus;
    const total = red + amber + green;
    
    if (total === 0) {
      return null;
    }
    
    const status = deriveRagStatus(item.ragStatus);
    
    return (
      <div className="mt-2">
        <div title={getStatusTooltip(status, item.type)}>
          {red > 0 ? (
            <AlertCircle className="h-6 w-6 text-red-500" />
          ) : amber > 0 ? (
            <AlertTriangle className="h-6 w-6 text-amber-500" />
          ) : (
            <CheckCircle2 className="h-6 w-6 text-green-500" />
          )}
        </div>
      </div>
    );
  }, []);

  // Define the expected response type
  interface RagStatusResponse {
    success: boolean;
    results: {
      interfaces: Record<string, { counts: { red: number; amber: number; green: number } }>;
      applications: Record<string, { counts: { red: number; amber: number; green: number } }>;
    };
  }

  // Update items when RAG status data is received
  useEffect(() => {
    if (!isMountedRef.current) return;

    if (
      ragStatusFetcher.data && 
      typeof ragStatusFetcher.data === 'object' && 
      ragStatusFetcher.data !== null &&
      'success' in ragStatusFetcher.data && 
      (ragStatusFetcher.data as RagStatusResponse).success
    ) {
      const data = ragStatusFetcher.data as RagStatusResponse;
      updateQuickAccessItemsWithRagStatus(data.results);
      loadItems();
    }
  }, [ragStatusFetcher.data, loadItems]);

  // Main effect for loading items and setting up intervals
  useEffect(() => {
    isMountedRef.current = true;
    
    // Initial load
    loadItems();
    
    // Set up interval for periodic updates
    const intervalId = setInterval(() => {
      if (isMountedRef.current) {
        loadItems();
      }
    }, 60000);
    
    // Handle storage changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'oms_quick_access' && isMountedRef.current) {
        loadItems();
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      isMountedRef.current = false;
      clearInterval(intervalId);
      window.removeEventListener('storage', handleStorageChange);
      
      // Clear any pending fetch timeout
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
    };
  }, [loadItems]);

  if (items.length === 0) {
    return null;
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold tracking-tight">Quick Access</h2>
      </div>
      <Card>
        <CardContent className="p-4">
          <div className="space-y-2">
            {items.map((item) => (
              <div key={`${item.type}-${item.id}`} className="relative group">
                <div
                  onClick={(e) => handleNavigation(e, item)}
                  className="flex items-center p-3 pr-12 rounded-md hover:bg-muted transition-colors cursor-pointer"
                >
                  <div className="flex items-center gap-3 flex-grow">
                    <div className="bg-gray-100 p-2 rounded-md">
                      {item.type === 'Application' && <AppWindow className="h-5 w-5 text-primary" />}
                      {item.type === 'Interface' && <Share2 className="h-5 w-5 text-primary" />}
                    </div>
                    <div className="min-w-0 flex-grow">
                      <div className="font-medium truncate flex items-center gap-2">
                        {item.name}
                        {item.ragStatus && (
                          <div title={getStatusTooltip(deriveRagStatus(item.ragStatus), item.type)}>
                            {item.ragStatus.red > 0 ? (
                              <AlertCircle className="h-5 w-5 text-red-500" />
                            ) : item.ragStatus.amber > 0 ? (
                              <AlertTriangle className="h-5 w-5 text-amber-500" />
                            ) : (
                              <CheckCircle2 className="h-5 w-5 text-green-500" />
                            )}
                          </div>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground flex items-center gap-1">
                        <Clock className="h-3 w-3 flex-shrink-0" /> {formatTimestamp(item.timestamp)}
                        {(item.orgLevel4 || item.orgLevel5) && (
                          <span className="ml-2 flex items-center gap-1">
                            <Building className="h-3 w-3 flex-shrink-0" />
                            {item.orgLevel4 && <span>{item.orgLevel4}</span>}
                            {item.orgLevel4 && item.orgLevel5 && <span>•</span>}
                            {item.orgLevel5 && <span>{item.orgLevel5}</span>}
                          </span>
                        )}
                      </div>                      
                    </div>
                  </div>
                  
                  <div className="flex items-center shrink-0 gap-2">
                    <span className={`text-xs px-2 py-1 rounded-full whitespace-nowrap ${
                      item.type === 'Application' 
                        ? 'bg-blue-50 text-blue-700' 
                        : 'bg-purple-50 text-purple-700'
                    }`}>
                      {item.type}
                    </span>
                  </div>
                </div>
                
                <Button
                  onClick={(e) => handleTogglePin(e, item)}
                  variant="ghost"
                  size="icon"
                  className={`absolute right-2 top-1/2 -translate-y-1/2 z-10 ${
                    item.pinned 
                      ? 'opacity-100' 
                      : 'opacity-0 group-hover:opacity-100 transition-opacity'
                  }`}
                  title={item.pinned ? "Unpin item" : "Pin item"}
                >
                  {item.pinned ? (
                    <PinOff className="h-4 w-4 text-primary" />
                  ) : (
                    <Pin className="h-4 w-4 text-muted-foreground" />
                  )}
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 