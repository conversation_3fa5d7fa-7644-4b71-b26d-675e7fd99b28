"use client"

import * as React from "react"
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table"

import { DataTablePagination } from "~/components/ui/data-table/data-table-pagination"
import { DataTableToolbar } from "~/components/ui/data-table/data-table-toolbar"

import type { DatasetWithSLA } from "~/db/types/datasets"
import { getColumns } from "./columns"
import { Button } from "~/components/ui/button"
import { Clock, EyeOff } from "lucide-react"
import type { RAGStatus } from "~/routes/interfaces.$interfaceId.datasets"

// Define the extended dataset type
type DatasetWithRAGStatus = DatasetWithSLA & { ragStatus: RAGStatus }

interface DataTableProps {
  data: DatasetWithRAGStatus[]
  selectedDatasets: string[]
  onSelectionChange: (selected: string[]) => void
  onViewEvents?: (datasetName: string) => void
  onUpdateSLA?: () => void
  onToggleRagInclusion?: (datasetName: string, include: boolean) => void
  onBulkToggleRagInclusion?: () => void
}

export function DataTable({
  data,
  selectedDatasets,
  onSelectionChange,
  onViewEvents,
  onUpdateSLA,
  onToggleRagInclusion,
  onBulkToggleRagInclusion,
}: DataTableProps) {
  const [rowSelection, setRowSelection] = React.useState({})
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [sorting, setSorting] = React.useState<SortingState>([])

  const columns = React.useMemo(
    () => getColumns({ 
      onViewEvents,
      onUpdateSLA: (datasetName) => {
        onSelectionChange([datasetName]);
        onUpdateSLA?.();
      },
      onToggleRagInclusion
    }),
    [onViewEvents, onUpdateSLA, onSelectionChange, onToggleRagInclusion]
  )

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    enableRowSelection: true,
    onRowSelectionChange: (updater) => {
      const newSelection = typeof updater === 'function' ? updater(rowSelection) : updater;
      setRowSelection(newSelection);
      
      // Convert row selection to dataset IDs
      const selectedIds = Object.keys(newSelection).map(
        (index) => data[parseInt(index)].datasetName
      );
      onSelectionChange(selectedIds);
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  })

  // Sync external selection with table selection
  React.useEffect(() => {
    const selectionState = data.reduce((acc, dataset, index) => {
      if (selectedDatasets.includes(dataset.datasetName)) {
        acc[index] = true;
      }
      return acc;
    }, {} as Record<number, boolean>);
    
    setRowSelection(selectionState);
  }, [selectedDatasets, data]);

  return (
    <div className="space-y-4">
      {selectedDatasets.length > 0 && (
        <div className="flex items-center gap-2">
          <Button
            onClick={onUpdateSLA}
            className="w-[200px]"
          >
            <Clock className="mr-2 h-4 w-4" />
            Update SLA ({selectedDatasets.length})
          </Button>
          
          <Button
            onClick={onBulkToggleRagInclusion}
            className="w-[250px]"
          >
            <EyeOff className="mr-2 h-4 w-4" />
            Toggle RAG Inclusion ({selectedDatasets.length})
          </Button>
        </div>
      )}
      <DataTableToolbar 
        table={table}
        filterColumn="datasetName"
        facetedFilters={[
          {
            column: "ragStatus",
            title: "Status",
            options: [
              { label: "SLA Breached (Red)", value: "red" },
              { label: "SLA At Risk (Amber)", value: "amber" },
              { label: "On Schedule (Green)", value: "green" },
            ],
          },
          // Only add legalEntityCode filter if there are non-null values
          ...(data.some(item => item.legalEntityCode) ? [{
            column: "legalEntityCode",
            title: "Legal Entity",
            options: Array.from(
              new Set(
                data
                  .map(item => item.legalEntityCode)
                  .filter(Boolean)
              )
            )
            .map(value => ({
              label: value as string,
              value: value as string,
            }))
          }] : []),
          {
            column: "productType",
            title: "Product Type",
            options: Array.from(
              new Set(
                data.flatMap(item => {
                  const types = item.productType;
                  return Array.isArray(types) ? types : [types];
                })
                .filter(Boolean)
              )
            )
            .map(value => ({
              label: value as string,
              value: value as string,
            })),
          },
        ]}
      />
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} />
    </div>
  )
} 