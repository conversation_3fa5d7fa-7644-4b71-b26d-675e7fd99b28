# API and Sync Utilities

This directory contains shared utilities for API interactions and synchronization operations across the application.

## API Utilities

The `api-utils.server.ts` file provides generalized components for interacting with external APIs. These utilities ensure consistent error handling, request behaviors, and caching across different service implementations.

### Main Components

- **ApiError**: Base error class that can be extended by specific services
- **makeRequest**: Generic function for making API requests with retry logic
- **buildQueryParams**: Helper to build URL query parameters from objects
- **createApiCache**: Factory function to create a simple caching mechanism

### Usage Example

```typescript
import { makeRequest, DEFAULT_API_CONFIG, ApiError } from '~/utils/api-utils.server';

// Extend the base API error class for your service
class MyServiceApiError extends ApiError {
  constructor(message: string, status: number, statusText: string) {
    super(message, status, statusText, "MyService");
  }
}

// Extend the default API configuration
const MY_SERVICE_CONFIG = {
  ...DEFAULT_API_CONFIG,
  headers: {
    ...DEFAULT_API_CONFIG.headers,
    "X-Custom-Header": "custom-value",
  },
};

// Make a request using the generic function
async function fetchData() {
  return makeRequest(
    'https://api.example.com/data',
    { method: 'GET' },
    MY_SERVICE_CONFIG,
    "MyService"
  );
}
```

## Sync Utilities

The `sync-utils.server.ts` file provides tools for synchronizing large datasets efficiently, with built-in support for:

- Batching operations
- Memory monitoring
- Parallel processing
- Database operations

### Main Components

- **ArrayProcessor**: Utilities for processing large arrays in chunks
- **DatabaseOperations**: Batch database operations with chunking
- **MemoryMonitor**: Monitor memory usage during sync operations
- **SyncLogger**: Consistent logging for sync processes

### Usage Example

```typescript
import { 
  ArrayProcessor, 
  DatabaseOperations, 
  MemoryMonitor, 
  SyncLogger,
  getSyncConfig 
} from '~/utils/sync-utils.server';

async function syncData(data: any[]) {
  // Create a logger for the sync process
  const logger = new SyncLogger('DataSync');
  
  // Initialize memory monitor
  const memoryMonitor = new MemoryMonitor(true);
  
  // Get sync configuration
  const config = getSyncConfig({
    batchSize: 50,
    parallelProcessing: true,
    memoryMonitoring: true
  });
  
  // Process data in batches
  logger.startTimer('processing');
  memoryMonitor.logUsage('start');
  
  await ArrayProcessor.processBatches(
    data,
    config.BATCH_SIZE,
    async (batch) => {
      // Process each batch
      return await processBatch(batch);
    },
    { 
      parallel: config.ENABLE_PARALLEL,
      onBatchComplete: (result, index) => {
        logger.progress(index + 1, Math.ceil(data.length / config.BATCH_SIZE), 'Batch Processing');
      }
    }
  );
  
  memoryMonitor.logUsage('end');
  logger.endTimer('processing');
  logger.summarize('Data sync completed successfully');
}
```

## UI Utilities

The `cn.ts` file provides a utility for handling Tailwind CSS class names, while `format-sla.ts` provides formatting for SLA data.

### Usage Example

```typescript
// Using the cn utility for conditional class names
import { cn } from '~/utils/cn';

const buttonClass = cn(
  'px-4 py-2 rounded',
  isActive && 'bg-blue-500 text-white',
  !isActive && 'bg-gray-200 text-gray-700'
);

// Using the SLA formatter
import { formatSLA } from '~/utils/format-sla';

const readableSLA = formatSLA('FREQ=WEEKLY;INTERVAL=2;BYDAY=MO,WE,FR;BYHOUR=09;BYMINUTE=30');
// Returns: "09:30 on Monday, Wednesday, Friday every 2 weeks"
```

## Benefits of the Refactored Approach

1. **Reduced Duplication**: Common API and sync logic is now centralized
2. **Consistency**: Error handling, retry logic, and logging are standardized
3. **Extensibility**: Easy to extend for new services or sync operations
4. **Better Memory Management**: Improved utilities for monitoring and managing memory
5. **Improved Error Handling**: Standardized API error classes with better context
6. **Scalability**: Batch processing and parallelization are built-in
7. **Proper Code Separation**: Following Remix conventions with .server.ts for server-only code

## Additional Improvements

The refactored utilities include several enhancements over the original implementation:

- Enhanced memory monitoring with differential tracking
- Batch operation callbacks for progress tracking
- Built-in caching mechanism for API responses
- Standardized query parameter building
- Improved timer and logging utilities 