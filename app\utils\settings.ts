// Constants for settings
const SETTINGS_STORAGE_KEY = 'oms_settings';

// Default values
const DEFAULT_MAX_QUICK_ACCESS_ITEMS = 5;
// Auto sync has been replaced with a fixed schedule (6:00 AM daily)
const DEFAULT_INTERFACE_LOAD_LIMIT = 100;
const DEFAULT_API_TIMEOUT_MS = 30000; // 30 seconds
const DEFAULT_MAX_RETRIES = 3;
const DEFAULT_RETRY_DELAY_MS = 1000; // 1 second

// Settings interface
export interface AppSettings {
  maxQuickAccessItems: number;
  interfaceLoadLimit: number;
  apiTimeoutMs: number;
  maxRetries: number;
  retryDelayMs: number;
}

// Default settings
const DEFAULT_SETTINGS: AppSettings = {
  maxQuickAccessItems: DEFAULT_MAX_QUICK_ACCESS_ITEMS,
  interfaceLoadLimit: DEFAULT_INTERFACE_LOAD_LIMIT,
  apiTimeoutMs: DEFAULT_API_TIMEOUT_MS,
  maxRetries: DEFAULT_MAX_RETRIES,
  retryDelayMs: DEFAULT_RETRY_DELAY_MS,
};

/**
 * Get application settings from localStorage
 */
export function getSettings(): AppSettings {
  if (typeof window === 'undefined') return DEFAULT_SETTINGS;
  
  try {
    const storedSettings = localStorage.getItem(SETTINGS_STORAGE_KEY);
    if (!storedSettings) return DEFAULT_SETTINGS;
    
    const settings = JSON.parse(storedSettings) as Partial<AppSettings>;
    
    // Merge with defaults in case new settings have been added
    return {
      ...DEFAULT_SETTINGS,
      ...settings,
    };
  } catch (error) {
    console.error('Failed to retrieve settings:', error);
    return DEFAULT_SETTINGS;
  }
}

/**
 * Update application settings
 */
export function updateSettings(settings: Partial<AppSettings>): void {
  if (typeof window === 'undefined') return;
  
  try {
    const currentSettings = getSettings();
    const updatedSettings: AppSettings = {
      ...currentSettings,
      ...settings,
    };
    
    localStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(updatedSettings));
    
    // Dispatch an event so other components can react to settings changes
    window.dispatchEvent(new CustomEvent('settings-updated', { 
      detail: updatedSettings 
    }));
  } catch (error) {
    console.error('Failed to update settings:', error);
  }
}

/**
 * Reset all settings to defaults
 */
export function resetSettings(): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(DEFAULT_SETTINGS));
    
    // Dispatch an event so other components can react to settings changes
    window.dispatchEvent(new CustomEvent('settings-updated', { 
      detail: DEFAULT_SETTINGS 
    }));
  } catch (error) {
    console.error('Failed to reset settings:', error);
  }
}

/**
 * Get a readable format of the auto sync interval
 */
export function getReadableInterval(ms: number): string {
  const minutes = Math.floor(ms / 60000);
  
  if (minutes === 1) {
    return '1 minute';
  }
  
  return `${minutes} minutes`;
}

// Export constants for use elsewhere
export const MAX_QUICK_ACCESS_ITEMS_KEY = 'maxQuickAccessItems'; 