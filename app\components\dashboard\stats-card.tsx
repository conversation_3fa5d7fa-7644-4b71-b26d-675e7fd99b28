import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "~/components/ui/card";
import { AppWindow, Share2, Database } from "lucide-react";

interface StatsCardProps {
  title: string;
  value: number;
  description: string;
  icon: 'application' | 'interface' | 'dataset';
}

export function StatsCard({ title, value, description, icon }: StatsCardProps) {
  const Icon = {
    application: AppWindow,
    interface: Share2,
    dataset: Database
  }[icon];

  return (
    <Card className="hover:shadow-md transition-all">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          <span className="flex items-center">
            <Icon className="h-4 w-4 mr-2 text-primary" />
            {title}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-3xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground mt-1">
          {description}
        </p>
      </CardContent>
    </Card>
  );
} 