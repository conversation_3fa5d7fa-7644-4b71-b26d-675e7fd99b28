import { db } from "~/db/db.server";
import { interfaces } from "~/db/schema/interfaces";
import { datasets } from "~/db/schema/datasets";
import { eq, and, or, inArray } from "drizzle-orm";
import type { SyncConfig, InterfaceSearchOptions } from "./interface-types.server";
import { DLASService } from "~/services/dlas.server";
import { getDatasets, setDatasets, getRagStatus, setRagStatus, clearCache } from "./interface-cache.server";
import { InterfaceModel } from "./interface-model.server";

/**
 * Default sync configuration
 */
const DEFAULT_SYNC_CONFIG: SyncConfig = {
    batchSize: 100,
    chunkSize: 50,
    enableParallel: true
};

/**
 * Simplified interface synchronization
 */
export async function updateSyncStatus(interfaceId: string, status: string): Promise<void> {
    await db.update(interfaces)
        .set({ 
            lastSyncStatus: status,
            lastSyncTime: new Date() 
        })
        .where(eq(interfaces.omsInterfaceId, interfaceId));
}

/**
 * Main sync method for interfaces and their datasets
 */
export async function syncFromDLAS(
    appId: string, 
    options: InterfaceSearchOptions & Partial<SyncConfig> = {}
): Promise<void> {
    const config = { ...DEFAULT_SYNC_CONFIG, ...options };
    console.log(`Starting sync for app ${appId}`);

    try {
        // 1. Fetch data from DLAS
        const dlasData = await fetchDLASData(appId, config.date);
        console.log(`Retrieved ${dlasData.interfaces.length} interfaces and ${dlasData.datasets.length} datasets`);

        // 2. Process interfaces
        await processInterfaces(dlasData.interfaces, config);

        // 3. Process datasets
        await processDatasets(dlasData.datasets, dlasData.interfaces, config);

        // 4. Update counts and finalize
        await finalizeSync(appId);

        console.log(`Sync completed successfully for app ${appId}`);
    } catch (error) {
        console.error(`Sync failed for app ${appId}:`, error);
        throw error;
  }
}

/**
 * Fetch data from DLAS
 */
async function fetchDLASData(appId: string, date?: string) {
    const response = await DLASService.fetchInterfaces(appId, undefined, date);
    return {
        interfaces: response.interface.interface_dlas_logged,
        datasets: response.dataset.dataset_logged_list
    };
}

/**
 * Process interfaces in batches
 */
async function processInterfaces(
    dlasInterfaces: any[], 
    config: SyncConfig
): Promise<void> {
    const transformedInterfaces = dlasInterfaces.map(i => 
        DLASService.transformInterface(i)
    );

    // Find existing interfaces
    const existingIds = new Set(
        (await db.select({ id: interfaces.omsInterfaceId })
            .from(interfaces)
            .where(inArray(
                interfaces.omsInterfaceId, 
                transformedInterfaces.map(i => i.omsInterfaceId)
            )))
        .map(r => r.id)
    );

    // Separate inserts and updates
    const toInsert = transformedInterfaces.filter(i => !existingIds.has(i.omsInterfaceId));
    const toUpdate = transformedInterfaces.filter(i => existingIds.has(i.omsInterfaceId));

    // Process in batches
    for (let i = 0; i < toInsert.length; i += config.batchSize) {
        const batch = toInsert.slice(i, i + config.batchSize);
        await db.insert(interfaces).values(batch);
    }

    for (let i = 0; i < toUpdate.length; i += config.batchSize) {
        const batch = toUpdate.slice(i, i + config.batchSize);
        await Promise.all(
            batch.map(interface_ =>
                db.update(interfaces)
                    .set(interface_)
                    .where(eq(interfaces.omsInterfaceId, interface_.omsInterfaceId))
            )
        );
  }
}

/**
 * Process datasets in batches
 */
async function processDatasets(
    dlasDatasets: any[],
    dlasInterfaces: any[],
    config: SyncConfig
  ): Promise<void> {
    // Build dataset to interface mapping
    const datasetMapping = new Map<string, string>();
    dlasInterfaces.forEach(interface_ => {
        interface_.RelatedDatasetList.logged_dataset.forEach((dataset: string) => {
            datasetMapping.set(dataset, interface_.InterfaceId);
        });
    });

    // Transform and process datasets in chunks
    for (let i = 0; i < dlasDatasets.length; i += config.chunkSize) {
        const chunk = dlasDatasets.slice(i, i + config.chunkSize);
        const transformedDatasets = chunk
            .filter(d => datasetMapping.has(d.DatasetName))
            .map(d => ({
                ...DLASService.transformDataset(d, datasetMapping.get(d.DatasetName)!),
                includeInRagStatus: d.transferType?.toUpperCase() === "FILE"
            }));
    
    // Find existing datasets
        const existingNames = new Set(
            (await db.select({ name: datasets.datasetName })
                .from(datasets)
                .where(inArray(
      datasets.datasetName,
                    transformedDatasets.map(d => d.datasetName)
                )))
            .map(r => r.name)
        );

        // Separate inserts and updates
        const toInsert = transformedDatasets.filter(d => !existingNames.has(d.datasetName));
        const toUpdate = transformedDatasets.filter(d => existingNames.has(d.datasetName));

        // Process batch
        if (toInsert.length > 0) {
            await db.insert(datasets).values(toInsert);
        }

        if (toUpdate.length > 0) {
            await Promise.all(
                toUpdate.map(dataset =>
                    db.update(datasets)
                        .set(dataset)
                        .where(eq(datasets.datasetName, dataset.datasetName))
                )
      );
    }
  }
}

/**
 * Finalize sync by updating counts and clearing cache
 */
async function finalizeSync(appId: string): Promise<void> {
    const interfaces = await InterfaceModel.findAll({ appId });
    
    await Promise.all(interfaces.map(async interface_ => {
      await InterfaceModel.updateDatasetCount(interface_.omsInterfaceId);
        await updateSyncStatus(interface_.omsInterfaceId, 'SUCCESS');
    }));

    // Clear cache for all processed interfaces
    interfaces.forEach(i => clearCache(i.omsInterfaceId));
} 