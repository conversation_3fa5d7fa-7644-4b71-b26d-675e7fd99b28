---
description: 
globs: 
alwaysApply: false
---
# Technology Stack

## Core Technologies

### Framework
- Remix.js - Full stack web framework
- React - UI library
- TypeScript - Type-safe JavaScript

### Database
- Drizzle ORM - Type-safe ORM
- SQLite - Development database

### Styling
- Tailwind CSS - Utility-first CSS framework
- PostCSS - CSS processing

### UI Components
- Shadcn UI - Component library
- Radix UI - Headless UI components

### Development Tools
- ESLint - Code linting
- Prettier - Code formatting
- Vite - Development server and bundler

## Key Files
- [package.json](mdc:package.json) - Dependencies and scripts
- [vite.config.ts](mdc:vite.config.ts) - Vite configuration
- [tailwind.config.ts](mdc:tailwind.config.ts) - Tailwind configuration
- [.eslintrc.json](mdc:.eslintrc.json) - ESLint rules
- [.prettierrc](mdc:.prettierrc) - Prettier configuration

## Environment
- [.env.example](mdc:.env.example) - Environment variables template
- Node.js runtime
- npm package manager

