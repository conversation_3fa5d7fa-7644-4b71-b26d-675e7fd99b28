import { useState, useEffect } from 'react';

export interface CustomDashboard {
  id: string;
  name: string;
  applicationIds: string[];
  createdAt: string;
  updatedAt: string;
}

const STORAGE_KEY = 'oms_custom_dashboards';
const ACTIVE_DASHBOARD_KEY = 'oms_active_dashboard';

export function useCustomDashboards() {
  const [dashboards, setDashboards] = useState<CustomDashboard[]>([]);
  const [activeDashboardId, setActiveDashboardId] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Load dashboards from local storage
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    try {
      const storedDashboards = localStorage.getItem(STORAGE_KEY);
      const storedActiveDashboard = localStorage.getItem(ACTIVE_DASHBOARD_KEY);
      
      if (storedDashboards) {
        const parsedDashboards = JSON.parse(storedDashboards);
        setDashboards(Array.isArray(parsedDashboards) ? parsedDashboards : []);
      }
      
      if (storedActiveDashboard) {
        setActiveDashboardId(storedActiveDashboard);
      }
      
      setIsInitialized(true);
    } catch (error) {
      console.error("Error loading dashboards from localStorage:", error);
      // Fallback to empty state
      setDashboards([]);
      setActiveDashboardId(null);
      setIsInitialized(true);
    }
  }, []);

  // Save dashboards to local storage
  const saveDashboards = (newDashboards: CustomDashboard[]) => {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newDashboards));
      setDashboards(newDashboards);
    } catch (error) {
      console.error("Error saving dashboards to localStorage:", error);
    }
  };

  // Create a new dashboard
  const createDashboard = (name: string, applicationIds: string[] = []) => {
    if (!isInitialized) return { id: '', name: '', applicationIds: [], createdAt: '', updatedAt: '' };
    
    const newDashboard: CustomDashboard = {
      id: crypto.randomUUID(),
      name,
      applicationIds,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const newDashboards = [...dashboards, newDashboard];
    saveDashboards(newDashboards);
    
    // Set as active if it's the first dashboard
    if (newDashboards.length === 1) {
      setActiveDashboard(newDashboard.id);
    }

    return newDashboard;
  };

  // Update a dashboard
  const updateDashboard = (id: string, updates: Partial<CustomDashboard>) => {
    if (!isInitialized) return;
    
    const newDashboards = dashboards.map(dashboard => {
      if (dashboard.id === id) {
        return {
          ...dashboard,
          ...updates,
          updatedAt: new Date().toISOString(),
        };
      }
      return dashboard;
    });
    saveDashboards(newDashboards);
  };

  // Delete a dashboard
  const deleteDashboard = (id: string) => {
    if (!isInitialized) return;
    
    const newDashboards = dashboards.filter(dashboard => dashboard.id !== id);
    saveDashboards(newDashboards);
    
    // Clear active dashboard if it was deleted
    if (activeDashboardId === id) {
      setActiveDashboard(newDashboards[0]?.id || null);
    }
  };

  // Set active dashboard
  const setActiveDashboard = (id: string | null) => {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem(ACTIVE_DASHBOARD_KEY, id || '');
      setActiveDashboardId(id);
    } catch (error) {
      console.error("Error setting active dashboard in localStorage:", error);
    }
  };

  // Get active dashboard
  const getActiveDashboard = () => {
    if (!isInitialized || !activeDashboardId) return null;
    return dashboards.find(d => d.id === activeDashboardId) || null;
  };

  return {
    dashboards,
    activeDashboardId,
    createDashboard,
    updateDashboard,
    deleteDashboard,
    setActiveDashboard,
    getActiveDashboard,
    isInitialized,
  };
} 