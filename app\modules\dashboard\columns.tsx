"use client"

import { ColumnDef } from "@tanstack/react-table"
import { DataTableColumnHeader } from "~/components/ui/data-table/data-table-column-header"
import { Badge } from "~/components/ui/badge"
import { AlertCircle, AlertTriangle, CheckCircle2, Link as LinkIcon } from "lucide-react"
import { Button } from "~/components/ui/button"

// Interface for application data from quick-access component
export interface DashboardApplication {
  id: string
  name: string
  status: {
    red: number
    amber: number
    green: number
  }
  interfaceCount: number
}

// Helper function to provide tooltip text for RAG status
function getStatusTooltip(status: string): string {
  switch (status) {
    case "red":
      return "SLA Breached - Dataset completed after Expected Delivery Time or is overdue"
    case "amber":
      return "SLA at Risk - Dataset is within 30 minutes of Expected Delivery Time"
    case "green":
      return "On Schedule - All datasets are on time or completed as expected"
    default:
      return "Unknown Status"
  }
}

// Helper function to derive the overall status from RAG counts
function deriveRagStatus(counts?: { red: number; amber: number; green: number }): string {
  if (!counts) return "unknown"
  if (counts.red > 0) return "red"
  if (counts.amber > 0) return "amber"
  return "green"
}

// Helper function for reuse in column definitions
const createColumns = (onViewInterfaces?: (id: string) => void): ColumnDef<DashboardApplication>[] => [
  {
    accessorKey: "id",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="ID" />
    ),
    cell: ({ row }) => {
      const applicationId = row.getValue("id") as string
      
      return (
        <div className="w-[100px] font-medium">
          <a
            href={`https://itid.service-now.com/nav_to.do?uri=%2Fcmdb_ci_business_app.do%3Fsysparm_query%3Du_app_instance_idSTARTSWITH${applicationId}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 underline hover:text-blue-800"
            onClick={(e) => e.stopPropagation()}
          >
            {applicationId}
          </a>
        </div>
      )
    },
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => {
      return (
        <div className="max-w-[500px] truncate">
          {row.getValue("name")}
        </div>
      )
    },
    enableSorting: true,
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const ragStatus = row.original.status
      const status = deriveRagStatus(ragStatus)
      
      return (
        <div className="flex justify-center" title={getStatusTooltip(status)}>
          {status === "red" ? (
            <AlertCircle className="h-5 w-5 text-red-500" />
          ) : status === "amber" ? (
            <AlertTriangle className="h-5 w-5 text-amber-500" />
          ) : (
            <CheckCircle2 className="h-5 w-5 text-green-500" />
          )}
        </div>
      )
    },
    meta: {
      width: "100px",
    },
    filterFn: (row, id, value) => {
      const status = deriveRagStatus(row.original.status)
      return value.includes(status)
    },
  },
  {
    accessorKey: "interfaceCount",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Interfaces" />
    ),
    cell: ({ row }) => {
      const interfaceCount = row.getValue("interfaceCount") as number
      const applicationId = row.getValue("id") as string
      
      return (
        <div className="flex items-center justify-center">
          {interfaceCount > 0 ? (
            <Button 
              variant="link" 
              size="sm" 
              className="p-0 h-auto flex items-center gap-1 text-inherit hover:text-primary"
              onClick={() => {
                if (onViewInterfaces) {
                  onViewInterfaces(applicationId);
                }
              }}
            >
              <span>{interfaceCount}</span>
              <LinkIcon className="h-3.5 w-3.5 text-primary" />
            </Button>
          ) : (
            <span className="text-muted-foreground">0</span>
          )}
        </div>
      )
    },
    meta: {
      width: "100px",
    },
    enableSorting: true,
  },
];

// Export for external use
export const columns: ColumnDef<DashboardApplication>[] = createColumns();

// Export function to create columns with callback function
export function getColumnsWithCallbacks(onViewInterfaces?: (id: string) => void) {
  return createColumns(onViewInterfaces);
} 