import { ActionFunction, LoaderFunction, json } from "@remix-run/node";
import { eventStream } from "~/utils/sse.server";
import { getDLASSyncService, getNextScheduledRunTime } from "~/services/dlas-sync.server";

// Define types for send and close functions
type SendEventFn = (event: { event: string; data: any }) => void;
type CloseFn = () => void;

// Define a type for the client connections
interface ClientConnection {
  send: SendEventFn;
  lastActive: number;
}

// Map of clients connected to the event stream
const clients = new Map<string, ClientConnection>();

// Set up periodic cleanup of stale connections
const STALE_CONNECTION_THRESHOLD_MS = 60000; // 1 minute
let cleanupInterval: NodeJS.Timeout | null = null;

/**
 * Set up an interval to clean up stale connections
 */
function setupCleanupInterval() {
  if (cleanupInterval) return;
  
  cleanupInterval = setInterval(() => {
    const now = Date.now();
    
    for (const [id, client] of clients.entries()) {
      // Remove clients inactive for more than the threshold
      if (now - client.lastActive > STALE_CONNECTION_THRESHOLD_MS) {
        clients.delete(id);
        console.log(`Removed stale client: ${id}`);
      }
    }
    
    // If no clients remain, clear the interval
    if (clients.size === 0) {
      if (cleanupInterval) {
        clearInterval(cleanupInterval);
        cleanupInterval = null;
      }
    }
  }, 30000); // Run cleanup every 30 seconds
}

/**
 * Safely send an event to a client, handling potential errors
 */
function safeSendToClient(client: ClientConnection, id: string, event: string, data: any): boolean {
  try {
    client.send({ event, data });
    client.lastActive = Date.now();
    return true;
  } catch (error) {
    console.error(`Error sending to client ${id}:`, error);
    clients.delete(id);
    return false;
  }
}

/**
 * Function used by the sync service to broadcast updates to all connected clients
 */
export const sendSyncUpdateToClients = (data: any) => {
  // If no clients, nothing to do
  if (clients.size === 0) return;
  
  let successCount = 0;
  
  for (const [id, client] of clients.entries()) {
    if (safeSendToClient(client, id, 'sync-update', data)) {
      successCount++;
    }
  }
  
  if (successCount > 0) {
    console.log(`Sent update to ${successCount} clients`);
  }
};

/**
 * Server-Sent Events endpoint for DLAS sync status
 */
export const loader: LoaderFunction = async ({ request }) => {
  return eventStream(request, function setup(send, close) {
    // Generate a unique ID for this client
    const clientId = `client-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    
    // Store the client
    clients.set(clientId, {
      send,
      lastActive: Date.now()
    });
    
    // Set up cleanup interval if needed
    setupCleanupInterval();
    
    // Send the current status immediately so client doesn't have to wait
    const syncService = getDLASSyncService();
    const { state, progress } = syncService.getSyncStatus();
    
    // Get next scheduled run time
    const nextScheduledRunTime = getNextScheduledRunTime();
    
    // Convert Date objects to ISO strings for JSON serialization
    send({
      event: 'sync-update',
      data: {
        type: 'syncStatus',
        data: {
          state,
          totalItems: progress.totalItems,
          processedItems: progress.processedItems,
          successCount: progress.successCount,
          errorCount: progress.errorCount,
          errors: progress.errors,
          startTime: progress.startTime ? progress.startTime.toISOString() : null,
          endTime: progress.endTime ? progress.endTime.toISOString() : null,
          elapsedTimeMs: progress.elapsedTimeMs,
          nextScheduledRunTime: nextScheduledRunTime.toISOString(),
          scheduledSyncInfo: {
            hour: 6,
            minute: 0,
            description: 'Daily sync at 6:00 AM'
          }
        }
      }
    });
    
    // Return a cleanup function
    return function cleanup() {
      clients.delete(clientId);
      console.log(`Client disconnected: ${clientId}`);
    };
  });
};

/**
 * Action endpoint to test Server-Sent Events
 */
export const action: ActionFunction = async ({ request }) => {
  try {
    if (clients.size === 0) {
      return json({ success: false, message: 'No connected clients' });
    }
    
    // Just a test endpoint to send a test message to all clients
    for (const [id, client] of clients.entries()) {
      safeSendToClient(client, id, 'sync-update', {
        type: 'test',
        message: 'This is a test message',
        timestamp: new Date().toISOString()
      });
    }
    
    return json({ success: true, message: `Test message sent to ${clients.size} clients` });
  } catch (error) {
    console.error('Error sending test message:', error);
    return json(
      { success: false, message: 'Failed to send test message' },
      { status: 500 }
    );
  }
}; 