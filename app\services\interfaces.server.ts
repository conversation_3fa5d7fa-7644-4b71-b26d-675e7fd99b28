import { withRetry } from '~/utils/retry.server';
import { SyncStatus } from '~/db/types/interfaces';
import { env } from '~/env.server';
import { DLASService, DLASApiError } from '~/services/dlas.server';
import type { InterfaceEvent } from '~/db/types/events';
import { DatasetModel } from '~/models/dataset.server';
import { EventModel } from '~/models/event.server';
import { InterfaceModel } from '~/models/interface.server';
import { DLASEventIDsResponse } from '~/db/types/events';
import { distance, closest } from 'fastest-levenshtein';

// Types for our sync results
export interface SyncResult {
  success: boolean;
  message?: string;
  error?: string;
  newEventCount?: number;
  totalEvents?: number;
  skippedEvents?: number;
  updatedDatasets?: string[];
}

/**
 * Service for handling server-side interface operations
 */
export class InterfaceService {

  /**
   * Syncs events for a specific interface
   * This is the main method to be called by clients and other services
   * It wraps the core implementation with retry logic for reliability
   * @param interfaceId The OMS interface ID
   * @param relatedDrilldownKey The related drilldown key
   * @returns Response data from the sync operation
   */
  static async syncEvents(interfaceId: string, relatedDrilldownKey: number): Promise<SyncResult> {
    return withRetry(async () => {
      return this.syncEventsImpl(interfaceId, relatedDrilldownKey);
    });
  }
  
  /**
   * Updates the sync status for an interface
   * @param interfaceId The OMS interface ID
   * @param status The new sync status
   */
  static async updateSyncStatus(interfaceId: string, status: SyncStatus): Promise<void> {
    return withRetry(async () => {
      await InterfaceModel.updateSyncStatus(interfaceId, status);
    });
  }

  /**
   * Core implementation of event synchronization for a specific interface
   * This method contains the actual sync logic but does NOT include retry capabilities
   * IMPORTANT: Do not call this method directly from outside the service - use syncEvents instead
   * @param interfaceId The OMS interface ID
   * @param relatedDrilldownKey The related drilldown key
   * @returns Result of the sync operation
   * @private
   */
  static async syncEventsImpl(interfaceId: string, relatedDrilldownKey: number): Promise<SyncResult> {
    try {
      // Update sync status to "in progress"
      await InterfaceService.updateSyncStatus(interfaceId, "pending");
      
      // Step 1: Fetch event IDs from DLAS API
      const eventIds = await DLASService.fetchEventIds(relatedDrilldownKey);
      console.log("Fetched event IDs for", eventIds);
      if (eventIds.length === 0) {
        return {
          success: true,
          message: "No new events found",
          newEventCount: 0,
          totalEvents: 0
        };
      }
      
      // Step 2: Find which events are not yet in our database
      const newEventIds = await EventModel.findNewEventIds(eventIds);
      
      if (newEventIds.length === 0) {
        return {
          success: true,
          message: "All events are already synchronized",
          newEventCount: 0,
          totalEvents: eventIds.length
        };
      }
      
      // Step 3: Fetch, transform and store new events - OPTIMIZED FOR PERFORMANCE
      // Process in batches to limit API load while speeding up processing
      const BATCH_SIZE = 50; // Adjust based on API limits
      const batches = [];
      
      for (let i = 0; i < newEventIds.length; i += BATCH_SIZE) {
        batches.push(newEventIds.slice(i, i + BATCH_SIZE));
      }
      
      let allTransformedEvents: InterfaceEvent[] = [];
      
      // Process batches in parallel with a concurrency limit
      const CONCURRENCY_LIMIT = 2; // Adjust based on API rate limits
      for (let i = 0; i < batches.length; i += CONCURRENCY_LIMIT) {
        const batchesToProcess = batches.slice(i, i + CONCURRENCY_LIMIT);
        const batchResults = await Promise.all(
          batchesToProcess.map(batch => InterfaceService.fetchAndTransformEvents(batch))
        );
        
        // Combine results
        allTransformedEvents = [...allTransformedEvents, ...batchResults.flat()];
      }
      
      // Process and store all events
      const { updatedDatasets } = await InterfaceService.processAndStoreEvents(allTransformedEvents);
      
      // Step 4: Update expected arrival times
      await InterfaceModel.updateExpectedArrivalTimes(interfaceId);
      
      // Step 5: Update sync status to "complete"
      await InterfaceService.updateSyncStatus(interfaceId, "success");
      
      return {
        success: true,
        message: `Synchronized ${newEventIds.length} new events`,
        newEventCount: newEventIds.length,
        totalEvents: eventIds.length,
        updatedDatasets
      };
    } catch (error) {
      console.error("Error syncing events:", error);
      
      // Update sync status to "failed"
      await InterfaceService.updateSyncStatus(interfaceId, "error");
      
      if (error instanceof DLASApiError) {
        return {
          success: false,
          error: `DLAS API Error: ${error.message}`
        };
      }
      
      return {
        success: false,
        error: "Failed to sync events"
      };
    }
  }
  
  /**
   * Process and store events, updating dataset arrival times
   * @param transformedEvents Array of transformed interface events
   * @returns Object containing transformed events and updated datasets
   */
  private static async processAndStoreEvents(
    transformedEvents: InterfaceEvent[]
  ): Promise<{ transformedEvents: InterfaceEvent[], updatedDatasets: string[] }> {
    // Group by dataset and track latest dates
    const datasetLatestDates = new Map<string, Date>();
    const affectedDatasets = new Set<string>();
    
    transformedEvents.forEach(event => {
      if (!event.datasetName || !event.createdDateTime) return;
      
      // Update latest dates if applicable
      const existingDate = datasetLatestDates.get(event.datasetName);
      const eventDate = new Date(event.createdDateTime);
      
      if (!existingDate || eventDate > existingDate) {
        datasetLatestDates.set(event.datasetName, eventDate);
      }
      
      // Track affected datasets
      affectedDatasets.add(event.datasetName);
    });
    
    // Update dataset last arrival times
    const updatedDatasets = await DatasetModel.updateLastArrivalTime(datasetLatestDates);
    
    // Store the transformed events
    await EventModel.storeEvents(transformedEvents);
    
    // Update event counts for all affected datasets
    await DatasetModel.updateEventCount(Array.from(affectedDatasets));
    
    return {
      transformedEvents,
      updatedDatasets
    };
  }

  /**
   * Fetches and transforms events from DLAS
   * @param eventIds Array of event IDs to fetch
   * @returns Array of transformed interface events
   */
  private static async fetchAndTransformEvents(eventIds: string[]): Promise<InterfaceEvent[]> {
    // Fetch events from DLAS
    const dlasEvents = await DLASService.fetchEvents(eventIds);
    
    // Transform events
    return dlasEvents.map(event => {
      const transformedEvent = DLASService.transformEvent(event);
      
      // Apply business logic for dataset name transformation
      if (transformedEvent.milestoneType === 'DATA_INFLOW') {
        transformedEvent.datasetName = `C_${transformedEvent.reportedForId}_${transformedEvent.datasetName}`;
      } else if (transformedEvent.milestoneType === 'DATA_OUTFLOW') {
        transformedEvent.datasetName = `P_${transformedEvent.reportedForId}_${transformedEvent.datasetName}`;
      }
      
      return transformedEvent;
    });
  }

  private static async normalizeEventDatasetNames(
    events: InterfaceEvent[],
    interfaceId: string
  ): Promise<InterfaceEvent[]> {
    const datasetNames = await InterfaceModel.getInterfaceDatasetNames(interfaceId);
    const datasetNameMap = new Map<string, string>();
    
    return events.map(event => {
      if (!event.datasetName) return event;
      
      // Direct match check
      if (datasetNames.includes(event.datasetName)) return event;
      
      // Cached result check
      if (datasetNameMap.has(event.datasetName)) {
        event.datasetName = datasetNameMap.get(event.datasetName)!;
        return event;
      }
      
      // Try quick hyphen-to-underscore transformation
      const hyphenToUnderscore = event.datasetName.replace(/-/g, '_');
      if (datasetNames.includes(hyphenToUnderscore)) {
        datasetNameMap.set(event.datasetName, hyphenToUnderscore);
        event.datasetName = hyphenToUnderscore;
        return event;
      }
      
      // Use fastest-levenshtein for advanced matching
      const bestMatch = closest(event.datasetName, datasetNames);
      
      // Only use the match if similarity is high enough
      const maxLength = Math.max(event.datasetName.length, bestMatch.length);
      const similarity = 1 - (distance(event.datasetName, bestMatch) / maxLength);
      
      if (similarity >= 0.8) {
        datasetNameMap.set(event.datasetName, bestMatch);
        event.datasetName = bestMatch;        
        console.log("Updating event dataset name for", event.datasetName, "to", bestMatch);
      }
      
      return event;
    });
  }
} 