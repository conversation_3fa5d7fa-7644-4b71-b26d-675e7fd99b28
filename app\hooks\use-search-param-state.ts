import { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from '@remix-run/react';

/**
 * Hook to manage search input and URL search parameters
 */
export function useSearchParamState(initialQuery: string = '') {
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchValue, setSearchValue] = useState(initialQuery);
  
  // Track whether we're updating from URL or from user input
  const [isUpdatingFromUrl, setIsUpdatingFromUrl] = useState(false);

  // Update search value when URL params change, but only if not being modified by user
  useEffect(() => {
    setIsUpdatingFromUrl(true);
    const queryParam = searchParams.get('query') || '';
    
    // Only update if the value is actually different
    if (queryParam !== searchValue) {
      setSearchValue(queryParam);
    }
    
    // Reset the flag after state is updated
    setTimeout(() => setIsUpdatingFromUrl(false), 50);
  }, [searchParams]);

  // Handle search input change
  const handleSearchChange = useCallback((value: string) => {
    // Don't process if we're currently updating from URL params
    if (isUpdatingFromUrl) return;
    
    setSearchValue(value);
  }, [isUpdatingFromUrl]);

  // Check if filters are applied
  const hasFilters = useCallback((additionalChecks: Record<string, boolean> = {}) => {
    const hasQueryParam = !!searchParams.get('query');
    const hasLevel4Filter = searchParams.get('level4') !== 'all' && !!searchParams.get('level4');
    const hasLevel5Filter = searchParams.get('level5') !== 'all' && !!searchParams.get('level5');
    
    // Combine with any additional checks
    return hasQueryParam || hasLevel4Filter || hasLevel5Filter || Object.values(additionalChecks).some(Boolean);
  }, [searchParams]);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setSearchValue('');
    setSearchParams({});
  }, [setSearchParams]);

  // Remove a specific filter
  const removeFilter = useCallback((paramName: string) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.delete(paramName);
    setSearchParams(newParams);
    
    if (paramName === 'query') {
      setSearchValue('');
    }
  }, [searchParams, setSearchParams]);

  return {
    searchValue,
    searchParams,
    setSearchParams,
    handleSearchChange,
    hasFilters,
    clearFilters,
    removeFilter
  };
} 