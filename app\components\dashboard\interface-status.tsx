import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "~/components/ui/card";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Triangle, CheckCircle2 } from "lucide-react";

interface InterfaceStatusProps {
  stats: {
    red: number;
    amber: number;
    green: number;
    total: number;
  };
}

export function InterfaceStatus({ stats }: InterfaceStatusProps) {
  // Calculate percentages for each status
  const redPercent = Math.round((stats.red / stats.total) * 100);
  const amberPercent = Math.round((stats.amber / stats.total) * 100);
  const greenPercent = Math.round((stats.green / stats.total) * 100);

  return (
    <div className="col-span-full md:col-span-1 space-y-4">
    <Card className="border-l-4 border-l-destructive">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium flex items-center">
          <AlertCircle className="h-4 w-4 mr-2 text-destructive" />
          SLA Breached
        </CardTitle>
        <div className="h-6 w-6 rounded-full bg-destructive flex items-center justify-center text-white text-xs font-bold">
          {stats.red}
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-xs text-muted-foreground">
          {redPercent}% of total interfaces
        </p>
        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
          <div 
            className="bg-destructive h-2 rounded-full" 
            style={{ width: `${redPercent}%` }} 
          />
        </div>
      </CardContent>
    </Card>
    
    <Card className="border-l-4 border-l-yellow-500">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium flex items-center">
          <AlertTriangle className="h-4 w-4 mr-2 text-yellow-500" />
          SLA at Risk
        </CardTitle>
        <div className="h-6 w-6 rounded-full bg-yellow-500 flex items-center justify-center text-white text-xs font-bold">
          {stats.amber}
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-xs text-muted-foreground">
          {amberPercent}% of total interfaces
        </p>
        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
          <div 
            className="bg-yellow-500 h-2 rounded-full" 
            style={{ width: `${amberPercent}%` }}
          />
        </div>
      </CardContent>
    </Card>
    
    <Card className="border-l-4 border-l-green-500">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium flex items-center">
          <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
          On Schedule
        </CardTitle>
        <div className="h-6 w-6 rounded-full bg-green-500 flex items-center justify-center text-white text-xs font-bold">
          {stats.green}
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-xs text-muted-foreground">
          {greenPercent}% of total interfaces
        </p>
        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
          <div 
            className="bg-green-500 h-2 rounded-full" 
            style={{ width: `${greenPercent}%` }}
          />
        </div>
      </CardContent>
    </Card>
  </div>
  );
} 