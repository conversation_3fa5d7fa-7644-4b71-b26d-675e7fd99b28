import express from 'express';
import compression from 'compression';
import { createRequestHandler } from '@remix-run/express';
import * as build from './build/server/index.js';
import path from 'path';
import { fileURLToPath } from 'url';
import * as dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Debug: Log environment variables
console.log('Environment variables loaded:');
console.log('HEET_API_URL:', process.env.HEET_API_URL || 'Not defined');
console.log('NODE_ENV:', process.env.NODE_ENV || 'Not defined');

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const BASE_PATH = '/' + (process.env.APP_NAME || 'oms');

// Create the Express app
const app = express();

// Enable gzip compression for all responses
app.use(compression());

// Serve static assets from the "public" folder (for favicons, images, etc.)
app.use(`${BASE_PATH}`, express.static(path.join(__dirname, 'public'), {
  maxAge: '1y', // Cache assets for up to 1 year
  immutable: true,
}));

// Serve static assets from the build/client directory (where Vite puts compiled assets)
app.use(`${BASE_PATH}/assets`, express.static(path.join(__dirname, 'build/client/assets'), {
  maxAge: '1y',
  immutable: true,
}));

// Handle all other requests with Remix
app.all(
  `${BASE_PATH}/*`,
  createRequestHandler({
    build,
    mode: process.env.NODE_ENV,
    getLoadContext(req, res) {
      return {
        // Pass environment variables explicitly to the Remix application
        env: {
          HEET_API_URL: process.env.HEET_API_URL,
          DLAS_API_URL: process.env.DLAS_API_URL,
          // Add any other environment variables needed by your app
          NODE_ENV: process.env.NODE_ENV,
          DATABASE_URL: process.env.DATABASE_URL,
        }
      };
    }
  })
);

// Redirect root to the base path
app.get('/', (req, res) => {
  res.redirect(BASE_PATH);
});

const port = process.env.APP_PORT || 5173;

app.listen(port, 'localhost', () => {
  console.log(`Server listening on port ${port}`);
  console.log(`Application available at http://localhost:${port}${BASE_PATH}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`Static assets served from:`);
  console.log(`  - ${path.join(__dirname, 'public')} (as ${BASE_PATH}/*)`);
  console.log(`  - ${path.join(__dirname, 'build/client/assets')} (as ${BASE_PATH}/assets/*)`);
});

// Handle uncaught exceptions to prevent the server from crashing
process.on('uncaughtException', (err) => {
  console.error('Uncaught exception:', err);
}); 