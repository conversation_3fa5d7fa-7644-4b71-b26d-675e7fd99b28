"use client"

import * as React from "react"
import { Row } from "@tanstack/react-table"
import { Co<PERSON>, MoreHorizontal, Pen, Trash } from "lucide-react"

import { But<PERSON> } from "~/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu"

interface DataTableRowActionsProps<TData> {
  row: Row<TData>
  actions?: {
    label: string
    icon?: React.ComponentType<{ className?: string }>
    shortcut?: string
    action: (row: TData) => void
  }[]
}

export function DataTableRowActions<TData>({
  row,
  actions = [],
}: DataTableRowActionsProps<TData>) {
  const defaultActions = [
    {
      label: "Edit",
      icon: Pen,
      shortcut: "⌘E",
      action: (row: TData) => console.log("Edit", row),
    },
    {
      label: "Copy ID",
      icon: Copy,
      shortcut: "⌘C",
      action: (row: TData) => {
        const id = (row as any).id
        if (id) navigator.clipboard.writeText(id.toString())
      },
    },
    {
      label: "Delete",
      icon: Trash,
      shortcut: "⌘⌫",
      action: (row: TData) => console.log("Delete", row),
    },
  ]

  const allActions = [...defaultActions, ...actions]

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
        >
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[160px]">
        {allActions.map((action, index) => (
          <React.Fragment key={action.label}>
            <DropdownMenuItem onClick={() => action.action(row.original)}>
              {action.icon && (
                <action.icon className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
              )}
              {action.label}
              {action.shortcut && (
                <DropdownMenuShortcut>{action.shortcut}</DropdownMenuShortcut>
              )}
            </DropdownMenuItem>
            {index < allActions.length - 1 && <DropdownMenuSeparator />}
          </React.Fragment>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
} 