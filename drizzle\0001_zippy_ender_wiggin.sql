PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_applications` (
	`application_instance_id` text PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`short_name` text,
	`description` text,
	`criticality` text NOT NULL,
	`status` text NOT NULL,
	`strategic_status` text NOT NULL,
	`org_level2` text NOT NULL,
	`org_level3` text NOT NULL,
	`org_level4` text NOT NULL,
	`org_level5` text NOT NULL,
	`plada_service_id` text NOT NULL,
	`plada_service_name` text NOT NULL,
	`owner_psid` text,
	`owner_display_name` text,
	`owner_email` text,
	`delegate_psid` text,
	`delegate_display_name` text,
	`delegate_email` text,
	`created_at` integer DEFAULT CURRENT_TIMESTAMP,
	`updated_at` integer DEFAULT CURRENT_TIMESTAMP
);
--> statement-breakpoint
INSERT INTO `__new_applications`("application_instance_id", "name", "short_name", "description", "criticality", "status", "strategic_status", "org_level2", "org_level3", "org_level4", "org_level5", "plada_service_id", "plada_service_name", "owner_psid", "owner_display_name", "owner_email", "delegate_psid", "delegate_display_name", "delegate_email", "created_at", "updated_at") SELECT "application_instance_id", "name", "short_name", "description", "criticality", "status", "strategic_status", "org_level2", "org_level3", "org_level4", "org_level5", "plada_service_id", "plada_service_name", "owner_psid", "owner_display_name", "owner_email", "delegate_psid", "delegate_display_name", "delegate_email", "created_at", "updated_at" FROM `applications`;--> statement-breakpoint
DROP TABLE `applications`;--> statement-breakpoint
ALTER TABLE `__new_applications` RENAME TO `applications`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE INDEX `status_idx` ON `applications` (`status`);--> statement-breakpoint
CREATE INDEX `org_idx` ON `applications` (`org_level4`,`org_level5`);--> statement-breakpoint
CREATE INDEX `plada_service_idx` ON `applications` (`plada_service_id`);--> statement-breakpoint
ALTER TABLE `datasets` ADD `country` text;--> statement-breakpoint
ALTER TABLE `datasets` ADD `legal_entity` text;--> statement-breakpoint
ALTER TABLE `datasets` ADD `legal_entity_code` text;--> statement-breakpoint
ALTER TABLE `datasets` ADD `line_of_business_name` text;--> statement-breakpoint
ALTER TABLE `datasets` ADD `expected_arrival_time` integer;