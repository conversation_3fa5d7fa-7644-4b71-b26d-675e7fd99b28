import { sql } from 'drizzle-orm';
import { text, integer, sqliteTable, index } from 'drizzle-orm/sqlite-core';

// Interface table schema
export const interfaces = sqliteTable('interfaces', {
  // Primary Key (SHA-256 hash of composite key fields)
  omsInterfaceId: text('oms_interface_id').primaryKey(),
  
  // Basic Information
  status: text('status'),
  direction: text('direction'),
  eimInterfaceId: text('eim_interface_id'),
  interfaceName: text('interface_name'),
  
  // Application Information
  sendAppId: text('send_app_id').notNull(),
  sendAppName: text('send_app_name'),
  receivedAppId: text('received_app_id').notNull(),
  receivedAppName: text('received_app_name'),
  
  // Interface Details
  transferType: text('transfer_type').notNull(),
  frequency: text('frequency').notNull(),
  technology: text('technology'),
  pattern: text('pattern'),
  
  // Related Information
  relatedDrilldownKey: integer('related_drilldown_key').notNull(),
  relatedDatasetList: text('related_dataset_list'), // Store as JSON string
  
  // Optional Information
  demiseDate: text('demise_date'),
  
  // Sync Information
  lastSyncTime: integer('last_sync_time', { mode: 'timestamp' }),
  lastSyncStatus: text('last_sync_status').default('pending'), // New column: 'success', 'error', or 'pending'
  
  // Dataset Information
  datasetCount: integer('dataset_count').default(0), // New column: Count of related datasets
  
  // Metadata
  createdAt: integer('created_at', { mode: 'timestamp' })
    .default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' })
    .default(sql`CURRENT_TIMESTAMP`),
}, (table) => ({
  // Indexes for frequently queried columns
  sendAppIdx: index('send_app_idx').on(table.sendAppId),
  receivedAppIdx: index('received_app_idx').on(table.receivedAppId),
  interfaceNameIdx: index('interface_name_idx').on(table.interfaceName),
})); 