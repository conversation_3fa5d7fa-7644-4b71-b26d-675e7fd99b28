import { useState, useEffect } from 'react';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Card } from '~/components/ui/card';
import { Plus, Edit2, Trash2, Copy, Check, Settings, ChevronDown } from 'lucide-react';
import type { CustomDashboard } from '~/hooks/use-custom-dashboards';
import type { ApplicationWithRagStatus } from '~/models/application.server';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup
} from '~/components/ui/dropdown-menu';
import { Link, useSearchParams } from '@remix-run/react';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogFooter,
  DialogTrigger
} from '~/components/ui/dialog';

interface CustomDashboardManagerProps {
  dashboards: CustomDashboard[];
  applications?: ApplicationWithRagStatus[];
  activeDashboardId: string | null;
  onCreateDashboard: (name: string, applicationIds: string[]) => CustomDashboard;
  onUpdateDashboard: (id: string, updates: Partial<CustomDashboard>) => void;
  onDeleteDashboard: (id: string) => void;
  onSelectDashboard: (id: string | null) => void;
  onEditDashboard?: (id: string) => void;
  compact?: boolean;
}

export function CustomDashboardManager({
  dashboards,
  applications,
  activeDashboardId,
  onCreateDashboard,
  onUpdateDashboard,
  onDeleteDashboard,
  onSelectDashboard,
  onEditDashboard,
  compact = false,
}: CustomDashboardManagerProps) {
  const [newDashboardName, setNewDashboardName] = useState('');
  const [selectedDashboard, setSelectedDashboard] = useState<CustomDashboard | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editingName, setEditingName] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchParams] = useSearchParams();
  
  // Get the edit parameter from URL
  const editParamId = searchParams.get('edit');
  
  // Sync the edit parameter with internal state
  useEffect(() => {
    let isMounted = true;
    
    // If URL has an edit parameter, update the editing state
    if (editParamId) {
      const dashboard = dashboards.find(d => d.id === editParamId);
      if (dashboard && isMounted) {
        setSelectedDashboard(dashboard);
        if (!isEditing) {
          setEditingName(dashboard.name);
        }
      }
    } else {
      // If URL doesn't have an edit parameter, clear the editing state
      if (isMounted) {
        setSelectedDashboard(null);
        setIsEditing(false);
      }
    }
    
    return () => {
      isMounted = false;
    };
  }, [dashboards, editParamId, isEditing]);

  const handleCreateDashboard = () => {
    if (!newDashboardName.trim()) return;
    const newDashboard = onCreateDashboard(newDashboardName.trim(), []);
    setNewDashboardName('');
    setIsCreateDialogOpen(false);
    if (onEditDashboard) {
      onEditDashboard(newDashboard.id);
    }
  };

  const handleSaveDashboardName = () => {
    if (selectedDashboard && editingName.trim()) {
      onUpdateDashboard(selectedDashboard.id, { name: editingName.trim() });
      setIsEditing(false);
    }
  };

  // Helper function to determine the card class based on dashboard status
  const getDashboardCardClass = (dashboard: CustomDashboard) => {
    const classes = ['p-4'];
    
    // Active dashboard highlight
    if (dashboard.id === activeDashboardId) {
      classes.push('border-primary');
    }
    
    // Edit mode highlight - use the URL parameter directly
    if (dashboard.id === editParamId) {
      classes.push('border-2 border-blue-500 bg-blue-50/30 dark:bg-blue-900/10');
    }
    
    return classes.join(' ');
  };

  // Render dropdown version (for dashboard header)
  if (compact) {
    return (
      <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="flex items-center gap-1">
            <Settings className="h-4 w-4 mr-1" />
            Custom Dashboards
            <ChevronDown className="h-4 w-4 ml-1" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>Select Dashboard</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          <DropdownMenuItem
            onClick={() => {
              onSelectDashboard(null);
              setIsDropdownOpen(false);
            }}
            className="flex items-center justify-between"
          >
            <span className="flex-1">All Applications</span>
            {!activeDashboardId && <Check className="h-4 w-4" />}
          </DropdownMenuItem>
          
          {dashboards.map(dashboard => (
            <div key={dashboard.id} className="flex items-center px-2 py-1.5 hover:bg-accent hover:text-accent-foreground">
              <div 
                className="flex-1 cursor-pointer"
                onClick={() => {
                  onSelectDashboard(dashboard.id);
                  setIsDropdownOpen(false);
                }}
              >
                <span className="mr-2">{dashboard.name}</span>
                {dashboard.id === activeDashboardId && <Check className="h-4 w-4 inline" />}
              </div>
              <div className="flex gap-1">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-6 w-6 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    if (onEditDashboard) {
                      onEditDashboard(dashboard.id);
                    }
                  }}
                >
                  <Edit2 className="h-3.5 w-3.5" />
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-6 w-6 p-0 text-destructive"
                  onClick={(e) => {
                    e.stopPropagation();
                    if (confirm('Are you sure you want to delete this dashboard?')) {
                      onDeleteDashboard(dashboard.id);
                    }
                  }}
                >
                  <Trash2 className="h-3.5 w-3.5" />
                </Button>
              </div>
            </div>
          ))}
          
          <DropdownMenuSeparator />
          
          <DropdownMenuGroup>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Dashboard
                </DropdownMenuItem>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create Dashboard</DialogTitle>
                </DialogHeader>
                <div className="py-4">
                  <Input
                    placeholder="Dashboard name"
                    value={newDashboardName}
                    onChange={(e) => setNewDashboardName(e.target.value)}
                  />
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateDashboard} disabled={!newDashboardName.trim()}>
                    Create
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem asChild>
              <Link to="/custom-dashboards" className="flex items-center">
                <Settings className="h-4 w-4 mr-2" />
                Manage All Dashboards
              </Link>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // Full dashboard manager view
  return (
    <div className="space-y-4">
      {/* Create New Dashboard */}
      <div className="flex items-center gap-2">
        <Input
          placeholder="New dashboard name"
          value={newDashboardName}
          onChange={(e) => setNewDashboardName(e.target.value)}
          className="flex-1"
        />
        <Button
          onClick={handleCreateDashboard}
          disabled={!newDashboardName.trim()}
          className="whitespace-nowrap"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create
        </Button>
      </div>

      {/* Dashboard List */}
      <div className="space-y-2">
        {dashboards.map((dashboard) => (
          <Card
            key={dashboard.id}
            className={getDashboardCardClass(dashboard)}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                {isEditing && selectedDashboard?.id === dashboard.id ? (
                  <Input
                    value={editingName}
                    onChange={(e) => setEditingName(e.target.value)}
                    className="max-w-sm"
                  />
                ) : (
                  <div
                    className="cursor-pointer"
                    onClick={() => onSelectDashboard(dashboard.id)}
                  >
                    <h3 className="font-medium">{dashboard.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {dashboard.applicationIds.length} applications selected
                    </p>
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-2">
                {isEditing && selectedDashboard?.id === dashboard.id ? (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setIsEditing(false);
                        setSelectedDashboard(null);
                        // Remove the edit parameter from URL
                        if (onEditDashboard) {
                          onEditDashboard('');
                        }
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleSaveDashboardName}
                    >
                      Save
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedDashboard(dashboard);
                        setIsEditing(true);
                        setEditingName(dashboard.name);
                        if (onEditDashboard) {
                          onEditDashboard(dashboard.id);
                        }
                      }}
                    >
                      <Edit2 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        if (confirm('Are you sure you want to delete this dashboard?')) {
                          onDeleteDashboard(dashboard.id);
                        }
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </>
                )}
              </div>
            </div>
          </Card>
        ))}
        
        {dashboards.length === 0 && (
          <div className="text-center p-4 text-muted-foreground border border-dashed rounded-md">
            No dashboards created yet. Create your first dashboard to get started.
          </div>
        )}
      </div>
    </div>
  );
} 