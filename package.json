{"name": "my-oms-v3", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "node server.js", "start:dev": "remix-serve ./build/server/index.js", "typecheck": "tsc", "postinstall": "npm run build"}, "dependencies": {"@hookform/resolvers": "^4.1.0", "@libsql/client": "^0.14.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@remix-run/css-bundle": "^2.16.0", "@remix-run/express": "^2.16.0", "@remix-run/node": "^2.15.3", "@remix-run/react": "^2.15.3", "@remix-run/serve": "^2.15.3", "@tanstack/react-table": "^8.21.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "compression": "^1.8.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.39.3", "express": "^4.21.2", "fastest-levenshtein": "^1.0.16", "isbot": "^4.1.0", "lucide-react": "^0.475.0", "nanoid": "^5.1.0", "openapi3-ts": "^4.4.0", "react": "^18.2.0", "react-day-picker": "^9.5.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "rrule": "^2.8.1", "sonner": "^1.7.4", "swagger-parser": "^10.0.3", "tailwind-merge": "^3.0.1", "tiny-invariant": "^1.3.3", "zod": "^3.24.2"}, "devDependencies": {"@remix-run/dev": "^2.15.3", "@shadcn/ui": "^0.0.4", "@types/node": "^22.13.4", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.16", "drizzle-kit": "^0.30.4", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.2", "typescript": "^5.1.6", "vite": "^5.1.0", "vite-tsconfig-paths": "^4.2.1"}, "engines": {"node": ">=20.0.0"}}