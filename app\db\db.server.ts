import { createClient } from '@libsql/client';
import { drizzle } from 'drizzle-orm/libsql';
import { migrate } from 'drizzle-orm/libsql/migrator';
import * as schema from './schema';
import { getEnv, isDevelopment } from '../utils/env.server';

// Get database configuration
const databaseUrl = getEnv('DATABASE_URL', undefined, 'file:./dev.db');
const nodeEnv = getEnv('NODE_ENV', undefined, 'development');

// Log database connection information
console.log(`Connecting to database with URL: ${databaseUrl}`);
console.log(`Current NODE_ENV: ${nodeEnv}`);

// Initialize the SQLite client
const client = createClient({
  url: databaseUrl,
});

// Initialize the Drizzle ORM
export const db = drizzle(client, { schema });

// Global flag to prevent multiple initialization attempts during hot-reloading
let dbInitialized = false;

/**
 * This function only initializes the database for development
 * It does NOT handle schema migrations - those should be done separately with drizzle-kit
 */
async function initializeDatabaseIfNeeded() {
  // Skip if already initialized in this session
  if (dbInitialized) {
    return;
  }
  
  dbInitialized = true;
  
  try {
    // Check if database is empty (no tables exist)
    const result = await client.execute({
      sql: "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'",
      args: []
    });
    
    const isEmptyDatabase = result.rows.length === 0;
    
    // Only run migrations for empty databases (first-time setup)
    if (isEmptyDatabase) {
      console.log('New database detected, running initial schema setup...');
      await migrate(db, { migrationsFolder: './drizzle' });
      console.log('Initial database setup completed');
    } else {
      console.log('Database already contains tables, skipping initialization');
    }
  } catch (error) {
    console.error('Database initialization error:', error);
    // Only exit in production; in development we can continue
    if (process.env.NODE_ENV === 'production') {
      console.error('Fatal database initialization error in production');
      process.exit(1);
    }
  }
}

// Only initialize database on startup
// For schema changes, use: `npx drizzle-kit generate` and then apply migrations manually
initializeDatabaseIfNeeded(); 