import type { RAGStatusResult } from './interface-types.server';

const datasetCache = new Map<string, string[]>();
const ragStatusCache = new Map<string, RAGStatusResult>();

/**
 * Simplified cache for interface-related data.
 * Uses separate caches for different data types to improve type safety and maintainability.
 */
export function getDatasets(id: string): string[] | undefined {
    return datasetCache.get(id);
}

export function setDatasets(id: string, datasets: string[]): void {
    datasetCache.set(id, datasets);
}

export function getRagStatus(id: string): RAGStatusResult | undefined {
    return ragStatusCache.get(id);
}

export function setRagStatus(id: string, status: RAGStatusResult): void {
    ragStatusCache.set(id, status);
}

export function clearCache(id?: string): void {
    if (id) {
        datasetCache.delete(id);
        ragStatusCache.delete(id);
    } else {
        datasetCache.clear();
        ragStatusCache.clear();
    }
}

// Export a single instance - Node.js module system ensures singleton behavior
export const interfaceCache = { getDatasets, setDatasets, getRagStatus, setRagStatus, clearCache }; 