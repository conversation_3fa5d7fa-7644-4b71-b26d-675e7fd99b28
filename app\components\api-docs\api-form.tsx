import React, { useState } from 'react';
import { Form } from '@remix-run/react';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Button } from '~/components/ui/button';
import { Switch } from '~/components/ui/switch';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { CheckCircle, XCircle, Copy, Check } from 'lucide-react';
import type { ApiParameter } from './api-endpoint';

export interface ApiFormProps {
  path: string;
  method: string;
  parameters: ApiParameter[];
  onResponse?: (response: any) => void;
}

export function ApiForm({ path, method, parameters, onResponse }: ApiFormProps) {
  const [formState, setFormState] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    data?: any;
  } | null>(null);
  const [isCopied, setIsCopied] = useState(false);

  const handleInputChange = (name: string, value: any) => {
    setFormState(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setResult(null);

    try {
      // Build form data for submission
      const formData = new FormData();
      
      // Add all parameters to the form data
      Object.entries(formState).forEach(([key, value]) => {
        // Skip empty values for optional parameters
        if (value === '' || value === undefined) return;
        
        // Handle array parameters (like datasetIds[])
        if (key.endsWith('[]') && Array.isArray(value)) {
          value.forEach(item => {
            if (item) formData.append(key, item);
          });
        } else if (typeof value === 'boolean') {
          // Convert boolean to string 'true'/'false'
          formData.append(key, value.toString());
        } else {
          formData.append(key, value);
        }
      });

      // Make the API request
      const response = await fetch(path, {
        method,
        body: method !== 'GET' ? formData : undefined,
      });

      const data = await response.json();
      
      const result = {
        success: response.ok,
        message: response.ok ? 'API call successful' : 'API call failed',
        data,
      };
      
      setResult(result);
      if (onResponse) onResponse(result);
    } catch (error) {
      setResult({
        success: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Special handling for array parameters
  const getArrayItems = (name: string) => {
    const baseKey = name.replace('[]', '');
    const items = formState[name] || [];
    return items.map((item: string, index: number) => (
      <div key={index} className="flex items-center gap-2 mt-1">
        <Input
          value={item}
          onChange={e => {
            const newItems = [...items];
            newItems[index] = e.target.value;
            handleInputChange(name, newItems);
          }}
        />
        <Button
          type="button"
          variant="destructive"
          size="sm"
          onClick={() => {
            const newItems = items.filter((_: any, i: number) => i !== index);
            handleInputChange(name, newItems);
          }}
        >
          Remove
        </Button>
      </div>
    ));
  };

  const addArrayItem = (name: string) => {
    const current = formState[name] || [];
    handleInputChange(name, [...current, '']);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000); // Reset after 2 seconds
      })
      .catch(err => {
        console.error('Failed to copy: ', err);
      });
  };

  return (
    <div className="space-y-6">
      <Form className="space-y-4" onSubmit={handleSubmit}>
        {parameters.map((param) => (
          <div key={param.name} className="space-y-2">
            <div className="flex justify-between items-center">
              <Label htmlFor={param.name}>
                {param.name} {param.required && <span className="text-red-500">*</span>}
              </Label>
              <span className="text-xs text-muted-foreground font-mono">{param.type}</span>
            </div>
            
            <div className="space-y-1">
              <p className="text-xs text-muted-foreground">{param.description}</p>
              
              {param.type === 'boolean' ? (
                <div className="flex items-center space-x-2">
                  <Switch
                    id={param.name}
                    checked={formState[param.name] === true}
                    onCheckedChange={checked => handleInputChange(param.name, checked)}
                  />
                  <Label htmlFor={param.name}>{formState[param.name] ? 'True' : 'False'}</Label>
                </div>
              ) : param.name.endsWith('[]') ? (
                <div className="space-y-2">
                  {getArrayItems(param.name)}
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => addArrayItem(param.name)}
                  >
                    Add Item
                  </Button>
                </div>
              ) : (
                <Input
                  id={param.name}
                  placeholder={param.example || `Enter ${param.name}`}
                  value={formState[param.name] || ''}
                  onChange={e => handleInputChange(param.name, e.target.value)}
                  required={param.required}
                />
              )}
            </div>
          </div>
        ))}

        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? 'Sending...' : 'Send Request'}
        </Button>
      </Form>

      {result && (
        <div className="space-y-2">
          <Alert variant={result.success ? 'default' : 'destructive'}>
            <div className="flex items-center gap-2">
              {result.success ? <CheckCircle size={16} /> : <XCircle size={16} />}
              <AlertDescription>
                {result.message}
              </AlertDescription>
            </div>
          </Alert>
          
          <div className="border rounded-md p-4 bg-muted">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium">Response</h3>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => copyToClipboard(JSON.stringify(result.data, null, 2))}
                className="text-xs h-7 px-2 gap-1"
              >
                {isCopied ? (
                  <>
                    <Check className="h-3.5 w-3.5" />
                    <span>Copied</span>
                  </>
                ) : (
                  <>
                    <Copy className="h-3.5 w-3.5" />
                    <span>Copy</span>
                  </>
                )}
              </Button>
            </div>
            <pre className="whitespace-pre-wrap text-xs overflow-auto">
              {JSON.stringify(result.data, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
} 