import * as React from "react"
import { toast } from "sonner"

import { Input } from "~/components/ui/input"
import { But<PERSON> } from "~/components/ui/button"
import { Label } from "~/components/ui/label"
import { getSettings, updateSettings, resetSettings, getReadableInterval } from "~/utils/settings"
import { useSetPageTitle } from "~/hooks/use-set-page-title"

export default function SettingsPage() {
  // Set the page title
  useSetPageTitle("Settings", "Configure application preferences");

  // Initialize state with current settings
  const [settings, setSettings] = React.useState(() => {
    const currentSettings = getSettings()
    return {
      maxQuickAccessItems: currentSettings.maxQuickAccessItems,
      interfaceLoadLimit: currentSettings.interfaceLoadLimit,
      apiTimeoutMs: currentSettings.apiTimeoutMs / 1000, // Convert ms to seconds
      maxRetries: currentSettings.maxRetries,
      retryDelayMs: currentSettings.retryDelayMs / 1000, // Convert ms to seconds
    }
  })

  const [errors, setErrors] = React.useState<Record<string, string>>({})
  
  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    const numValue = parseInt(value, 10) || 1 // Default to 1 if not a valid number
    
    setSettings(prev => ({
      ...prev,
      [name]: numValue
    }))
  }
  
  // Save settings
  const handleSave = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate inputs
    if (settings.maxQuickAccessItems < 1 || settings.maxQuickAccessItems > 20) {
      toast.error("Maximum Quick Access Items must be between 1 and 20")
      return
    }
    
    // Save settings
    updateSettings({
      maxQuickAccessItems: settings.maxQuickAccessItems,
      interfaceLoadLimit: settings.interfaceLoadLimit,
      apiTimeoutMs: settings.apiTimeoutMs * 1000,
      maxRetries: settings.maxRetries,
      retryDelayMs: settings.retryDelayMs * 1000,
    })
    
    toast.success("Settings updated successfully")
  }
  
  // Reset to defaults
  const handleReset = () => {
    resetSettings()
    const defaultSettings = getSettings()
    
    setSettings({
      maxQuickAccessItems: defaultSettings.maxQuickAccessItems,
      interfaceLoadLimit: defaultSettings.interfaceLoadLimit,
      apiTimeoutMs: defaultSettings.apiTimeoutMs / 1000,
      maxRetries: defaultSettings.maxRetries,
      retryDelayMs: defaultSettings.retryDelayMs / 1000,
    })
    
    toast.success("Settings reset to defaults")
  }
  
  return (
    <div className="px-6 py-6 space-y-6">
      <div className="w-full">
        <div className="mb-6">
          <h3 className="text-lg font-medium">Application Settings</h3>
          <p className="text-sm text-muted-foreground">
            Adjust the application settings according to your preferences.
            These settings will be stored in your browser.
          </p>
        </div>
        
        <form onSubmit={handleSave} className="space-y-8">
          <div className="space-y-2">
            <Label htmlFor="maxQuickAccessItems">Maximum Quick Access Items</Label>
            <Input
              id="maxQuickAccessItems"
              name="maxQuickAccessItems"
              type="number"
              min={1}
              max={20}
              value={settings.maxQuickAccessItems}
              onChange={handleChange}
              className="max-w-md"
            />
            <p className="text-sm text-muted-foreground">
              How many items to show in the quick access menu.
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="interfaceLoadLimit">Interface Load Limit</Label>
            <Input
              id="interfaceLoadLimit"
              name="interfaceLoadLimit"
              type="number"
              min={10}
              max={1000}
              value={settings.interfaceLoadLimit}
              onChange={handleChange}
              className="max-w-md"
            />
            <p className="text-sm text-muted-foreground">
              The maximum number of interfaces to load at once.
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="apiTimeoutMs">API Timeout (seconds)</Label>
            <Input
              id="apiTimeoutMs"
              name="apiTimeoutMs"
              type="number"
              min={1}
              max={60}
              value={settings.apiTimeoutMs}
              onChange={handleChange}
              className="max-w-md"
            />
            <p className="text-sm text-muted-foreground">
              The timeout for API requests.
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="maxRetries">Max Retries</Label>
            <Input
              id="maxRetries"
              name="maxRetries"
              type="number"
              min={0}
              max={10}
              value={settings.maxRetries}
              onChange={handleChange}
              className="max-w-md"
            />
            <p className="text-sm text-muted-foreground">
              The maximum number of retry attempts for API requests.
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="retryDelayMs">Retry Delay (seconds)</Label>
            <Input
              id="retryDelayMs"
              name="retryDelayMs"
              type="number"
              min={0.1}
              max={10}
              step={0.1}
              value={settings.retryDelayMs}
              onChange={handleChange}
              className="max-w-md"
            />
            <p className="text-sm text-muted-foreground">
              The delay between retry attempts when an API request fails.
            </p>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" type="button" onClick={handleReset}>
              Reset to Defaults
            </Button>
            <Button type="submit">Save Changes</Button>
          </div>
        </form>
      </div>
    </div>
  )
} 