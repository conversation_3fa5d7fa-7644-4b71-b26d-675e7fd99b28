import { withRetry } from '~/utils/retry.server';
import type { SyncOptions } from '~/utils/sync-utils.server';

/**
 * Extended sync options with application-specific filters
 */
interface ApplicationSyncOptions extends SyncOptions {
  orgLevel4?: string;
  orgLevel5?: string;
  pladaServiceIds?: string[];
}

interface SyncResponseData {
  success: boolean;
  error?: string;
}

/**
 * Service for handling server-side application operations
 */
export class ApplicationService {
  /**
   * Syncs applications and their interfaces from HEET
   * @param options Sync options including filters
   * @returns Response data from the sync operation
   */
  static async syncApplications(options: ApplicationSyncOptions = {}): Promise<SyncResponseData> {
    return withRetry(async () => {
      const formData = new FormData();
      
      if (options.orgLevel4) {
        formData.append('orgLevel4', options.orgLevel4);
      }
      
      if (options.orgLevel5) {
        formData.append('orgLevel5', options.orgLevel5);
      }
      
      if (options.pladaServiceIds && options.pladaServiceIds.length > 0) {
        options.pladaServiceIds.forEach(id => {
          formData.append('pladaServiceIds[]', id);
        });
      }
      
      const response = await fetch('/oms/api/applications/sync', {
        method: 'POST',
        body: formData,
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        return { 
          success: false, 
          error: data.error || `Error ${response.status}: ${response.statusText}` 
        };
      }
      
      return data;
    });
  }
} 