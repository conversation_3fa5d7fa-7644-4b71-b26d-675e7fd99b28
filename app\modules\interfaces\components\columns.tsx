"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "~/components/ui/badge"
import { DataTableColumnHeader } from "~/components/ui/data-table/data-table-column-header"
import { Interface, InterfaceDirection } from "~/db/types/interfaces"
import type { InterfaceWithRagStatus } from "~/models/interface/interface-rag-status.server"
import { Link, AlertCircle, AlertTriangle, CheckCircle2, CheckCircle, XCircle, Clock, HelpCircle } from "lucide-react"
import { SyncStatus } from "~/db/types/interfaces"
import { Button } from "~/components/ui/button"

// Define interface types
export const transferTypes = [
  { value: "FILE", label: "File" },
  { value: "API", label: "API" },
  { value: "Message", label: "Message" },
] as const

interface GetColumnsOptions {
  onViewDatasets?: (interfaceId: string, interfaceName: string) => void
}

// Helper function to derive RAG status from counts
function deriveRagStatus(counts: { red: number; amber: number; green: number }): "red" | "amber" | "green" {
  if (counts.red > 0) return "red";
  if (counts.amber > 0) return "amber";
  return "green";
}

// Helper function to get a sync status icon
function getSyncStatusIcon(status: string | null | undefined): React.ReactNode {
  switch (status) {
    case SyncStatus.SUCCESS:
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    case SyncStatus.ERROR:
      return <XCircle className="h-4 w-4 text-red-500" />;
    case SyncStatus.PENDING:
      return <Clock className="h-4 w-4 text-amber-500" />;
    default:
      return <HelpCircle className="h-4 w-4 text-gray-400" />;
  }
}

// Helper function to get a sync status tooltip
function getSyncStatusTooltip(status: string | null | undefined): string {
  switch (status) {
    case SyncStatus.SUCCESS:
      return "Last sync was successful";
    case SyncStatus.ERROR:
      return "Last sync failed";
    case SyncStatus.PENDING:
      return "Sync in progress";
    default:
      return "No sync status available";
  }
}

// Helper function to get status tooltip
function getStatusTooltip(status: "red" | "amber" | "green"): string {
  switch (status) {
    case "red":
      return "SLA Breached";
    case "amber":
      return "SLA At Risk";
    case "green":
      return "On Schedule";
  }
}

export function getColumns({ 
  onViewDatasets
}: GetColumnsOptions): ColumnDef<InterfaceWithRagStatus>[] {
  return [
    {
      accessorKey: "interfaceName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Name" />
      ),
      cell: ({ row }) => {
        const transferType = transferTypes.find(
          (type) => type.value === row.original.transferType
        )
        return (
          <div className="flex space-x-2 min-w-[200px]">
            {transferType && <Badge variant="outline">{transferType.label}</Badge>}
            <span className="whitespace-normal break-words font-medium">
              {row.getValue("interfaceName")}
            </span>
          </div>
        )
      },
    },
    {
      accessorKey: "direction",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Direction" />
      ),
      cell: ({ row }) => {
        const direction = row.getValue("direction") as keyof typeof InterfaceDirection
        return (
          <Badge variant="outline">
            {InterfaceDirection[direction]}
          </Badge>
        )
      },
    },
    {
      accessorKey: "sendAppId",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Send App ID" />
      ),
      cell: ({ row }) => {
        return (
          <div className="font-medium">
            {row.getValue("sendAppId") ? (
              <a
                href={`https://itid.service-now.com/nav_to.do?uri=%2Fcmdb_ci_business_app.do%3Fsysparm_query%3Du_app_instance_idSTARTSWITH${row.getValue("sendAppId")}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 underline hover:text-blue-800"
              >
                {row.getValue("sendAppId")}
              </a>
            ) : (
              row.getValue("sendAppId")
            )}
          </div>
        )
      },
    },
    {
      accessorKey: "sendAppName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Send App Name" />
      ),
      cell: ({ row }) => {
        return (
          <div className="min-w-[200px] whitespace-normal break-words">
            {row.getValue("sendAppName")}
          </div>
        )
      },
    },
    {
      accessorKey: "receivedAppId",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Received App ID" />
      ),
      cell: ({ row }) => {
        return (
          <div className="font-medium">
            {row.getValue("receivedAppId") ? (
              <a
                href={`https://itid.service-now.com/nav_to.do?uri=%2Fcmdb_ci_business_app.do%3Fsysparm_query%3Du_app_instance_idSTARTSWITH${row.getValue("receivedAppId")}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 underline hover:text-blue-800"
              >
                {row.getValue("receivedAppId")}
              </a>
            ) : (
              row.getValue("receivedAppId")
            )}
          </div>
        )
      },
    },
    {
      accessorKey: "receivedAppName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Received App Name" />
      ),
      cell: ({ row }) => {
        return (
          <div className="min-w-[200px] whitespace-normal break-words">
            {row.getValue("receivedAppName")}
          </div>
        )
      },
    },
    {
      accessorKey: "transferType",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Transfer Type" />
      ),
      cell: ({ row }) => {
        const transferType = transferTypes.find(
          (type) => type.value === row.getValue("transferType")
        )
        return (
          <Badge variant="secondary">
            {transferType?.label ?? row.getValue("transferType")}
          </Badge>
        )
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id))
      },
    },
    {
      accessorKey: "frequency",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Frequency" />
      ),
      cell: ({ row }) => {
        return (
          <Badge variant="outline">
            {row.getValue("frequency")}
          </Badge>
        )
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id))
      },
    },
    {
      accessorKey: "lastSyncTime",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Last Synced" />
      ),
      cell: ({ row }) => {
        const lastSyncTime = row.getValue("lastSyncTime") as number | null;
        if (!lastSyncTime) return <div className="text-muted-foreground">Never</div>;
        
        return (
          <div className="text-muted-foreground">
            {new Date(lastSyncTime).toLocaleString()}
          </div>
        )
      },
    },
    {
      accessorKey: "lastSyncStatus",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Sync Status" />
      ),
      cell: ({ row }) => {
        const status = row.getValue("lastSyncStatus") as string | null | undefined;
        return (
          <div className="flex justify-center" title={getSyncStatusTooltip(status)}>
            {getSyncStatusIcon(status)}
          </div>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      id: "ragStatus",
      accessorKey: "ragStatus",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="SLA Status" />
      ),
      // Set minimum width for the column to accommodate the status bar
      meta: {
        width: "100px",
      },
      cell: ({ row }) => {
        const ragStatus = row.original.ragStatus;
        const { red, amber, green } = ragStatus.counts;
        const total = red + amber + green;
        
        // Derive status from counts
        const status = deriveRagStatus(ragStatus.counts);
        
        // Calculate relative widths for the RAG bar segments
        // If total is 0, we'll set more visible default values 
        const redWidth = total > 0 ? (red / total) * 100 : 0;
        const amberWidth = total > 0 ? (amber / total) * 100 : 0;
        const greenWidth = total > 0 ? (green / total) * 100 : 100; // Show full green bar when no data
        
        return (
          <div className="flex flex-col w-full gap-1">
            {/* Status indicator */}
            <div className="flex items-center justify-center">
              <div title={getStatusTooltip(status)}>
                {red > 0 ? (
                  <AlertCircle className="h-5 w-5 text-red-500" />
                ) : amber > 0 ? (
                  <AlertTriangle className="h-5 w-5 text-amber-500" />
                ) : (
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                )}
              </div>
            </div>
          </div>
        )
      },
      sortingFn: (rowA, rowB) => {
        // Sorting priority: red first, then amber, then green
        const statusA = deriveRagStatus(rowA.original.ragStatus.counts);
        const statusB = deriveRagStatus(rowB.original.ragStatus.counts);
        
        const statusOrder = { "red": 0, "amber": 1, "green": 2 };
        return statusOrder[statusA] - statusOrder[statusB];
      },
      filterFn: (row, id, value) => {
        // For RAG status filtering, check if any of the selected statuses have counts > 0
        // e.g., if "red" is selected, filter for interfaces that have red count > 0
        const ragStatus = row.original.ragStatus;
        
        if (!value || !value.length) return true;
        
        return value.some((status: string) => {
          if (status === "red" && ragStatus.counts.red > 0) return true;
          if (status === "amber" && ragStatus.counts.amber > 0) return true;
          if (status === "green" && ragStatus.counts.green > 0) return true;
          return false;
        });
      },
    },
    {
      accessorKey: "datasetCount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Datasets" />
      ),
      enableSorting: true,
      cell: ({ row }) => {
        const interface_ = row.original;
        const count = interface_.datasetCount || 0;
        
        return (
          <div className="text-center">
            {count > 0 ? (
              <button
                onClick={() => {
                  if (interface_.omsInterfaceId) {
                    onViewDatasets?.(interface_.omsInterfaceId, interface_.interfaceName || '')
                  }
                }}
                className="flex items-center justify-center gap-1 mx-auto px-2 py-1 text-primary rounded-md hover:bg-primary/10 transition-colors font-medium"
                title="View Datasets"
              >
                <span>{count}</span>
                <Link className="h-3.5 w-3.5" />
              </button>
            ) : (
              <span className="text-muted-foreground">0</span>
            )}
          </div>
        )
      }
    },
  ]
} 