import { json, type LoaderFunction } from "@remix-run/node"
import { useLoaderData, useNavigation, useSubmit } from "@remix-run/react"
import type { Application } from "~/db/types/applications"
import { ApplicationModel, ApplicationWithRagStatus } from "~/models/application.server"
import { HeetApiError } from "~/services/heet.server"
import { RetryError } from "~/utils/retry.server"
import { useEffect } from "react"
import { useSearchParamState } from "~/hooks/use-search-param-state"
import { ApplicationsDataTable } from "~/modules/applications/components/data-table"
import { ApplicationHeader } from "~/modules/applications/components/application-header"
import { ApplicationListInfo } from "~/modules/applications/components/application-list-info"
import { SearchableTreeView } from "~/components/application-tree-view/searchable-tree-view"
import { useRootLoaderData } from "~/hooks/use-root-loader-data"

// Define the data structure for the loader
interface LoaderData {
  applications: ApplicationWithRagStatus[];
  filteredApplications: ApplicationWithRagStatus[];
  error?: string;
  searchParams: {
    query?: string;
    orgLevel4?: string;
    orgLevel5?: string;
  }
}

export const loader: LoaderFunction = async ({ request }) => {
  const url = new URL(request.url);
  const query = url.searchParams.get("query") || "";
  const orgLevel4 = url.searchParams.get("level4") || "all";
  const orgLevel5 = url.searchParams.get("level5") || "all";

  // Prepare common search parameters for response
  const searchParamsForResponse = {
    query,
    orgLevel4: orgLevel4 !== "all" ? orgLevel4 : undefined,
    orgLevel5: orgLevel5 !== "all" ? orgLevel5 : undefined,
  };

  try {
    // Get all applications for the tree view
    const allApplications = await ApplicationModel.findAll();
    
    // Enhance all applications with interface RAG status
    const enhancedApplications = await ApplicationModel.enhanceWithInterfaceRagStatus(allApplications);

    // Filter applications for the table view
    const filteredApplications = enhancedApplications.filter(app => {
      const matchesLevel4 = orgLevel4 === "all" || app.orgLevel4 === orgLevel4;
      const matchesLevel5 = orgLevel5 === "all" || app.orgLevel5 === orgLevel5;
      const matchesQuery = !query || 
        app.name.toLowerCase().includes(query.toLowerCase()) ||
        app.applicationInstanceId === query;
      
      return matchesLevel4 && matchesLevel5 && matchesQuery;
    });

    return json<LoaderData>({ 
      applications: enhancedApplications, // Use full list for tree view
      filteredApplications, // Use filtered list for table view
      searchParams: searchParamsForResponse
    });
  } catch (error) {
    console.error("Error loading applications:", error);
    
    // Handle specific error types
    if (error instanceof HeetApiError) {
      return json<LoaderData>(
        { 
          applications: [], 
          filteredApplications: [],
          error: `HEET API Error: ${error.message}`,
          searchParams: searchParamsForResponse
        },
        { status: error.status }
      );
    }

    if (error instanceof RetryError) {
      const lastError = error.lastError;
      const errorMessage = lastError instanceof Error 
        ? lastError.message 
        : 'Unknown error';
      
      return json<LoaderData>(
        { 
          applications: [], 
          filteredApplications: [],
          error: `Failed after ${error.attempts} attempts: ${errorMessage}`,
          searchParams: searchParamsForResponse
        }, 
        { status: 500 }
      );
    }

    // Generic error handler for all other errors
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return json<LoaderData>(
      { 
        applications: [], 
        filteredApplications: [],
        error: `Failed to load applications: ${errorMessage}`,
        searchParams: searchParamsForResponse
      }, 
      { status: 500 }
    );
  }
}

/**
 * Main applications page component
 */
export default function ApplicationsPage() {
  const { applications, filteredApplications, error, searchParams } = useLoaderData<LoaderData>();
  const { organizations } = useRootLoaderData(); // Get organizations from root loader
  const navigation = useNavigation();
  const isLoading = navigation.state === "loading";
  const submit = useSubmit();
  
  // Use search param state management
  const {
    searchValue,
    searchParams: searchParamsState,
    setSearchParams,
    handleSearchChange,
    hasFilters: getHasFilters,
    clearFilters
  } = useSearchParamState(searchParams.query || "");
  
  // Compute if we have any active filters
  const hasFilters = getHasFilters();
  
  // Reset search when navigating away and back
  useEffect(() => {
    handleSearchChange(searchParams.query || "");
  }, [searchParams.query, handleSearchChange]);
  
  return (
    <div className="flex h-full">
      {/* Searchable Application Tree View */}
      <SearchableTreeView 
        applications={applications}
        organizations={organizations}
        routeKey="applications"
        searchValue={searchValue}
        onSearchChange={handleSearchChange}
        hasFilters={hasFilters}
        onClearFilters={clearFilters}
        searchParams={searchParamsState}
        setSearchParams={setSearchParams}
      />
      
      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="p-4 space-y-4">
          {/* Header with title and error message */}
          <ApplicationHeader error={error} isLoading={isLoading} />
          
          {/* Applications list info */}
          <ApplicationListInfo
            applications={filteredApplications}
            hasSearchQuery={!!searchParamsState.get("query")}
          />
          
          {/* Applications data table */}
          {filteredApplications.length > 0 && (
            <ApplicationsDataTable 
              data={filteredApplications}
              isLoading={isLoading}
              orgLevel4={searchParamsState.get("level4") || "all"}
              orgLevel5={searchParamsState.get("level5") || "all"}
            />
          )}
        </div>
      </div>
    </div>
  );
} 