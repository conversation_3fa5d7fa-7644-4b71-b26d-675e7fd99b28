import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
  DialogDescription,
} from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { Checkbox } from "~/components/ui/checkbox";
import { cn } from "~/utils/cn";

type RecurrencePattern = "daily" | "weekly" | "monthly" | "yearly";
type WeekDay = "MO" | "TU" | "WE" | "TH" | "FR" | "SA" | "SU";
type MonthlyType = "day" | "weekday";
type YearlyType = "date" | "weekday";

interface SLAFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (sla: string) => Promise<void>;
  selectedCount: number;
  isSubmitting?: boolean;
  existingSla?: string;
}

interface SLAFormState {
  time: string;
  pattern: RecurrencePattern;
  interval: number;
  weekDays: WeekDay[];
  monthlyType: MonthlyType;
  monthDay: number;
  monthWeek: number;
  monthWeekDay: WeekDay;
  yearlyType: YearlyType;
  yearMonth: number;
  yearMonthDay: number;
  yearWeek: number;
  yearWeekDay: WeekDay;
  endType: "never" | "after" | "on";
  endAfterCount: number;
  endOnDate: string;
}

const WEEK_DAYS: { label: string; value: WeekDay }[] = [
  { label: "Monday", value: "MO" },
  { label: "Tuesday", value: "TU" },
  { label: "Wednesday", value: "WE" },
  { label: "Thursday", value: "TH" },
  { label: "Friday", value: "FR" },
  { label: "Saturday", value: "SA" },
  { label: "Sunday", value: "SU" },
];

const MONTHS = [
  { label: "January", value: 1 },
  { label: "February", value: 2 },
  { label: "March", value: 3 },
  { label: "April", value: 4 },
  { label: "May", value: 5 },
  { label: "June", value: 6 },
  { label: "July", value: 7 },
  { label: "August", value: 8 },
  { label: "September", value: 9 },
  { label: "October", value: 10 },
  { label: "November", value: 11 },
  { label: "December", value: 12 },
];

const WEEK_NUMBERS = [
  { label: "First", value: 1 },
  { label: "Second", value: 2 },
  { label: "Third", value: 3 },
  { label: "Fourth", value: 4 },
  { label: "Last", value: -1 },
];

// Function to parse RRULE string and return form state
const parseRRule = (rrule: string | undefined): Partial<SLAFormState> => {
  if (!rrule) return {};
  
  const formState: Partial<SLAFormState> = {};
  
  // Extract components
  const parts = rrule.split(';').reduce((acc, part) => {
    const [key, value] = part.split('=');
    acc[key] = value;
    return acc;
  }, {} as Record<string, string>);
  
  // Set pattern based on FREQ
  if (parts.FREQ) {
    switch (parts.FREQ) {
      case 'DAILY':
        formState.pattern = 'daily';
        break;
      case 'WEEKLY':
        formState.pattern = 'weekly';
        break;
      case 'MONTHLY':
        formState.pattern = 'monthly';
        break;
      case 'YEARLY':
        formState.pattern = 'yearly';
        break;
    }
  }
  
  // Set time
  if (parts.BYHOUR && parts.BYMINUTE) {
    const hours = parts.BYHOUR.padStart(2, '0');
    const minutes = parts.BYMINUTE.padStart(2, '0');
    const seconds = parts.BYSECOND ? parts.BYSECOND.padStart(2, '0') : '00';
    formState.time = `${hours}:${minutes}:${seconds}`;
  }
  
  // Set interval
  if (parts.INTERVAL) {
    formState.interval = parseInt(parts.INTERVAL);
  }
  
  // Weekly pattern specifics
  if (formState.pattern === 'weekly' && parts.BYDAY) {
    formState.weekDays = parts.BYDAY.split(',') as WeekDay[];
  }
  
  // Monthly pattern specifics
  if (formState.pattern === 'monthly') {
    if (parts.BYMONTHDAY) {
      formState.monthlyType = 'day';
      formState.monthDay = parseInt(parts.BYMONTHDAY);
    } else if (parts.BYDAY) {
      formState.monthlyType = 'weekday';
      // Extract week number and day from format like "1MO"
      const match = parts.BYDAY.match(/(-?\d+)([A-Z]{2})/);
      if (match) {
        formState.monthWeek = parseInt(match[1]);
        formState.monthWeekDay = match[2] as WeekDay;
      }
    }
  }
  
  // Yearly pattern specifics
  if (formState.pattern === 'yearly') {
    if (parts.BYMONTH) {
      formState.yearMonth = parseInt(parts.BYMONTH);
      
      if (parts.BYMONTHDAY) {
        formState.yearlyType = 'date';
        formState.yearMonthDay = parseInt(parts.BYMONTHDAY);
      } else if (parts.BYDAY) {
        formState.yearlyType = 'weekday';
        // Extract week number and day from format like "1MO"
        const match = parts.BYDAY.match(/(-?\d+)([A-Z]{2})/);
        if (match) {
          formState.yearWeek = parseInt(match[1]);
          formState.yearWeekDay = match[2] as WeekDay;
        }
      }
    }
  }
  
  // End condition
  if (parts.COUNT) {
    formState.endType = 'after';
    formState.endAfterCount = parseInt(parts.COUNT);
  } else if (parts.UNTIL) {
    formState.endType = 'on';
    // Parse UNTIL date from format like "20240531T235959Z"
    const year = parts.UNTIL.substring(0, 4);
    const month = parts.UNTIL.substring(4, 6);
    const day = parts.UNTIL.substring(6, 8);
    formState.endOnDate = `${year}-${month}-${day}`;
  } else {
    formState.endType = 'never';
  }
  
  return formState;
};

export function SLAForm({
  isOpen,
  onClose,
  onSubmit,
  selectedCount,
  isSubmitting = false,
  existingSla,
}: SLAFormProps) {
  const [formState, setFormState] = useState<SLAFormState>({
    time: "",
    pattern: "daily",
    interval: 1,
    weekDays: ["MO"],
    monthlyType: "day",
    monthDay: 1,
    monthWeek: 1,
    monthWeekDay: "MO",
    yearlyType: "date",
    yearMonth: 1,
    yearMonthDay: 1,
    yearWeek: 1,
    yearWeekDay: "MO",
    endType: "never",
    endAfterCount: 1,
    endOnDate: new Date().toISOString().split('T')[0],
  });
  
  // Add a loading state to prevent form flashing
  const [isFormReady, setIsFormReady] = useState(false);

  // Parse and set form state when existing SLA changes
  useEffect(() => {
    if (isOpen) {
      // Set loading state
      setIsFormReady(false);
      
      // Parse the existing SLA if available
      if (existingSla) {
        const parsedState = parseRRule(existingSla);
        setFormState(prev => ({
          ...prev,
          ...parsedState
        }));
      } else {
        // Reset to default state if no existing SLA
        setFormState({
          time: "",
          pattern: "daily",
          interval: 1,
          weekDays: ["MO"],
          monthlyType: "day",
          monthDay: 1,
          monthWeek: 1,
          monthWeekDay: "MO",
          yearlyType: "date",
          yearMonth: 1,
          yearMonthDay: 1,
          yearWeek: 1,
          yearWeekDay: "MO",
          endType: "never",
          endAfterCount: 1,
          endOnDate: new Date().toISOString().split('T')[0],
        });
      }
      
      // Use a small timeout to ensure the state is fully updated before showing the form
      setTimeout(() => {
        setIsFormReady(true);
      }, 50);
    }
  }, [existingSla, isOpen]);

  const generateRRule = (): string => {
    // Ensure we're using the time as UTC
    const [hours, minutes, seconds = 0] = formState.time.split(':').map(Number);
    let ruleStr = `FREQ=`;

    switch (formState.pattern) {
      case "daily":
        ruleStr += `DAILY;TZID=UTC;INTERVAL=${formState.interval};BYHOUR=${hours};BYMINUTE=${minutes};BYSECOND=${seconds}`;
        break;
      case "weekly":
        ruleStr += `WEEKLY;TZID=UTC;INTERVAL=${formState.interval};BYDAY=${formState.weekDays.join(",")};BYHOUR=${hours};BYMINUTE=${minutes};BYSECOND=${seconds}`;
        break;
      case "monthly":
        if (formState.monthlyType === "day") {
          ruleStr += `MONTHLY;TZID=UTC;INTERVAL=${formState.interval};BYMONTHDAY=${formState.monthDay};BYHOUR=${hours};BYMINUTE=${minutes};BYSECOND=${seconds}`;
        } else {
          ruleStr += `MONTHLY;TZID=UTC;INTERVAL=${formState.interval};BYDAY=${formState.monthWeek}${formState.monthWeekDay};BYHOUR=${hours};BYMINUTE=${minutes};BYSECOND=${seconds}`;
        }
        break;
      case "yearly":
        if (formState.yearlyType === "date") {
          ruleStr += `YEARLY;TZID=UTC;INTERVAL=${formState.interval};BYMONTH=${formState.yearMonth};BYMONTHDAY=${formState.yearMonthDay};BYHOUR=${hours};BYMINUTE=${minutes};BYSECOND=${seconds}`;
        } else {
          ruleStr += `YEARLY;TZID=UTC;INTERVAL=${formState.interval};BYMONTH=${formState.yearMonth};BYDAY=${formState.yearWeek}${formState.yearWeekDay};BYHOUR=${hours};BYMINUTE=${minutes};BYSECOND=${seconds}`;
        }
        break;
    }

    // Add end condition
    if (formState.endType === "after") {
      ruleStr += `;COUNT=${formState.endAfterCount}`;
    } else if (formState.endType === "on") {
      const endDate = new Date(formState.endOnDate);
      ruleStr += `;UNTIL=${endDate.getFullYear()}${String(endDate.getMonth() + 1).padStart(2, '0')}${String(endDate.getDate()).padStart(2, '0')}T235959Z`;
    }

    return ruleStr;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formState.time) return;

    try {
      const rrule = generateRRule();
      await onSubmit(rrule);
      setFormState({
        time: "",
        pattern: "daily",
        interval: 1,
        weekDays: ["MO"],
        monthlyType: "day",
        monthDay: 1,
        monthWeek: 1,
        monthWeekDay: "MO",
        yearlyType: "date",
        yearMonth: 1,
        yearMonthDay: 1,
        yearWeek: 1,
        yearWeekDay: "MO",
        endType: "never",
        endAfterCount: 1,
        endOnDate: new Date().toISOString().split('T')[0],
      });
      onClose();
    } catch (error) {
      console.error("Failed to update SLA:", error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            Update SLA for {selectedCount} Dataset{selectedCount !== 1 ? "s" : ""}
          </DialogTitle>
          <DialogDescription>
            Configure the SLA schedule for the selected datasets.
          </DialogDescription>
        </DialogHeader>
        
        {!isFormReady ? (
          <div className="py-8 flex justify-center items-center">
            <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-primary"></div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              {/* Time Input */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Label htmlFor="sla-time">SLA Time (UTC)</Label>
                </div>
                <Input
                  id="sla-time"
                  type="time"
                  step="1"
                  value={formState.time}
                  onChange={(e) => setFormState(prev => ({ ...prev, time: e.target.value }))}
                  required
                  placeholder="00:00:00"
                  disabled={isSubmitting}
                  aria-describedby="sla-time-description"
                />
                <p id="sla-time-description" className="text-sm text-muted-foreground">
                  Enter the time in UTC timezone. This will be used for SLA calculations regardless of your local timezone.
                </p>
              </div>

              {/* Pattern Selection */}
              <div className="space-y-2">
                <Label>Recurrence Pattern</Label>
                <Select
                  value={formState.pattern}
                  onValueChange={(value: RecurrencePattern) => 
                    setFormState(prev => ({ ...prev, pattern: value }))
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="yearly">Yearly</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Interval Input */}
              <div className="space-y-2">
                <Label>Repeat every</Label>
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    min={1}
                    max={99}
                    value={formState.interval}
                    onChange={(e) => 
                      setFormState(prev => ({ ...prev, interval: parseInt(e.target.value) || 1 }))
                    }
                    className="w-20"
                    disabled={isSubmitting}
                  />
                  <span>
                    {formState.pattern === "daily" && "day(s)"}
                    {formState.pattern === "weekly" && "week(s)"}
                    {formState.pattern === "monthly" && "month(s)"}
                    {formState.pattern === "yearly" && "year(s)"}
                  </span>
                </div>
              </div>

              {/* Pattern-specific Options */}
              {formState.pattern === "weekly" && (
                <div className="space-y-2">
                  <Label>Repeat on</Label>
                  <div className="grid grid-cols-7 gap-2">
                    {WEEK_DAYS.map((day) => (
                      <div key={day.value} className="flex flex-col items-center space-y-1">
                        <Checkbox
                          id={`day-${day.value}`}
                          checked={formState.weekDays.includes(day.value)}
                          onCheckedChange={(checked) => {
                            setFormState(prev => ({
                              ...prev,
                              weekDays: checked
                                ? [...prev.weekDays, day.value]
                                : prev.weekDays.filter(d => d !== day.value)
                            }));
                          }}
                          disabled={isSubmitting}
                        />
                        <Label htmlFor={`day-${day.value}`} className="text-sm">
                          {day.label.slice(0, 3)}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {formState.pattern === "monthly" && (
                <div className="space-y-4">
                  <RadioGroup
                    value={formState.monthlyType}
                    onValueChange={(value: MonthlyType) => 
                      setFormState(prev => ({ ...prev, monthlyType: value }))
                    }
                    disabled={isSubmitting}
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="day" id="monthly-day" />
                      <Label htmlFor="monthly-day">Day</Label>
                      <Input
                        type="number"
                        min={1}
                        max={31}
                        value={formState.monthDay}
                        onChange={(e) => 
                          setFormState(prev => ({ ...prev, monthDay: parseInt(e.target.value) || 1 }))
                        }
                        className="w-20 ml-2"
                        disabled={isSubmitting || formState.monthlyType !== "day"}
                      />
                      <span>of the month</span>
                    </div>

                    <div className="flex items-center space-x-2 mt-2">
                      <RadioGroupItem value="weekday" id="monthly-weekday" />
                      <Label htmlFor="monthly-weekday">The</Label>
                      <Select
                        value={formState.monthWeek.toString()}
                        onValueChange={(value) => 
                          setFormState(prev => ({ ...prev, monthWeek: parseInt(value) }))
                        }
                        disabled={isSubmitting || formState.monthlyType !== "weekday"}
                      >
                        <SelectTrigger className="w-24">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {WEEK_NUMBERS.map((week) => (
                            <SelectItem key={week.value} value={week.value.toString()}>
                              {week.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Select
                        value={formState.monthWeekDay}
                        onValueChange={(value: WeekDay) => 
                          setFormState(prev => ({ ...prev, monthWeekDay: value }))
                        }
                        disabled={isSubmitting || formState.monthlyType !== "weekday"}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {WEEK_DAYS.map((day) => (
                            <SelectItem key={day.value} value={day.value}>
                              {day.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <span>of the month</span>
                    </div>
                  </RadioGroup>
                </div>
              )}

              {formState.pattern === "yearly" && (
                <div className="space-y-4">
                  <RadioGroup
                    value={formState.yearlyType}
                    onValueChange={(value: YearlyType) => 
                      setFormState(prev => ({ ...prev, yearlyType: value }))
                    }
                    disabled={isSubmitting}
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="date" id="yearly-date" />
                      <Label htmlFor="yearly-date">Every</Label>
                      <Select
                        value={formState.yearMonth.toString()}
                        onValueChange={(value) => 
                          setFormState(prev => ({ ...prev, yearMonth: parseInt(value) }))
                        }
                        disabled={isSubmitting || formState.yearlyType !== "date"}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {MONTHS.map((month) => (
                            <SelectItem key={month.value} value={month.value.toString()}>
                              {month.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Input
                        type="number"
                        min={1}
                        max={31}
                        value={formState.yearMonthDay}
                        onChange={(e) => 
                          setFormState(prev => ({ ...prev, yearMonthDay: parseInt(e.target.value) || 1 }))
                        }
                        className="w-20"
                        disabled={isSubmitting || formState.yearlyType !== "date"}
                      />
                    </div>

                    <div className="flex items-center space-x-2 mt-2">
                      <RadioGroupItem value="weekday" id="yearly-weekday" />
                      <Label htmlFor="yearly-weekday">The</Label>
                      <Select
                        value={formState.yearWeek.toString()}
                        onValueChange={(value) => 
                          setFormState(prev => ({ ...prev, yearWeek: parseInt(value) }))
                        }
                        disabled={isSubmitting || formState.yearlyType !== "weekday"}
                      >
                        <SelectTrigger className="w-24">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {WEEK_NUMBERS.map((week) => (
                            <SelectItem key={week.value} value={week.value.toString()}>
                              {week.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Select
                        value={formState.yearWeekDay}
                        onValueChange={(value: WeekDay) => 
                          setFormState(prev => ({ ...prev, yearWeekDay: value }))
                        }
                        disabled={isSubmitting || formState.yearlyType !== "weekday"}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {WEEK_DAYS.map((day) => (
                            <SelectItem key={day.value} value={day.value}>
                              {day.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <span>of</span>
                      <Select
                        value={formState.yearMonth.toString()}
                        onValueChange={(value) => 
                          setFormState(prev => ({ ...prev, yearMonth: parseInt(value) }))
                        }
                        disabled={isSubmitting || formState.yearlyType !== "weekday"}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {MONTHS.map((month) => (
                            <SelectItem key={month.value} value={month.value.toString()}>
                              {month.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </RadioGroup>
                </div>
              )}

              {/* End Options */}
              <div className="space-y-4">
                <Label>End</Label>
                <RadioGroup
                  value={formState.endType}
                  onValueChange={(value: "never" | "after" | "on") => 
                    setFormState(prev => ({ ...prev, endType: value }))
                  }
                  disabled={isSubmitting}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="never" id="end-never" />
                    <Label htmlFor="end-never">Never</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="after" id="end-after" />
                    <Label htmlFor="end-after">After</Label>
                    <Input
                      type="number"
                      min={1}
                      value={formState.endAfterCount}
                      onChange={(e) => 
                        setFormState(prev => ({ ...prev, endAfterCount: parseInt(e.target.value) || 1 }))
                      }
                      className="w-20"
                      disabled={isSubmitting || formState.endType !== "after"}
                    />
                    <span>times</span>
                  </div>

                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="on" id="end-on" />
                    <Label htmlFor="end-on">On date</Label>
                    <Input
                      type="date"
                      value={formState.endOnDate}
                      onChange={(e) => 
                        setFormState(prev => ({ ...prev, endOnDate: e.target.value }))
                      }
                      className="w-40"
                      disabled={isSubmitting || formState.endType !== "on"}
                    />
                  </div>
                </RadioGroup>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-current"></div>
                    Updating...
                  </>
                ) : (
                  "Update SLA"
                )}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
} 