import { json, type ActionFunction } from "@remix-run/node";
import { DatasetModel } from "~/models/dataset.server";

export const action: ActionFunction = async ({ request }) => {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  const formData = await request.formData();
  const datasetIds = formData.getAll("datasetIds[]");
  const sla = formData.get("sla");

  if (!datasetIds.length || !sla) {
    return json(
      { error: "Dataset IDs and SLA are required" },
      { status: 400 }
    );
  }

  try {
    await DatasetModel.updateSLA(datasetIds as string[], sla as string);
    return json({ success: true });
  } catch (error) {
    console.error("Error updating SLA:", error);
    return json(
      { error: "Failed to update SLA" },
      { status: 500 }
    );
  }
}; 