import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "~/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "~/components/ui/tabs";
import { ScrollArea } from "~/components/ui/scroll-area";
import { Separator } from "~/components/ui/separator";
import { Button } from "~/components/ui/button";
import { FileText } from "lucide-react";
import { useEffect } from "react";
import { useSetPageTitle } from "~/hooks/use-set-page-title";

interface PRFAQ {
  headline: string;
  subtitle: string;
  date: string;
  intro: string;
  problem: string;
  solution: string;
  leaderQuote: string;
  howItWorks: string;
  customerQuote: string;
  getStarted: string;
}

interface FAQItem {
  question: string;
  answer: string;
}

export function loader() {
  return json({
    title: "OMS v3 User Manual",
    prfaq: {
      headline: "OMS v3: The Next-Generation Operations Monitoring System",
      subtitle: "Simplifying application monitoring with integrated API access and comprehensive interface tracking",
      date: "March 2024",
      intro: "OMS v3 provides a comprehensive solution for monitoring applications, interfaces, and datasets across your organization. With powerful new features including an interactive API documentation and testing interface, OMS v3 enables both technical and non-technical users to monitor, manage, and integrate with critical data flows.",
      problem: "Organizations struggle with monitoring complex data flows between applications. Technical teams need programmatic access to monitoring systems, while operations teams need simple interfaces to check status. Traditional monitoring systems often fail to provide both interfaces in an integrated manner, leading to disconnected workflows and information silos.",
      solution: "OMS v3 bridges this gap with a unified interface that provides both visual monitoring tools and programmatic API access. The system makes it easy to track application status, verify data arrival times against SLAs, and automate workflows through documented APIs. Our interactive API documentation allows developers to test endpoints directly within the system.",
      leaderQuote: "We developed OMS v3 to make monitoring accessible to everyone in the organization. By combining powerful visual tools with well-documented APIs, we've created a system that serves both operations and development teams,\" said Tom Johnson, Head of Operations Technology. \"The interactive API documentation represents our commitment to making system integration straightforward and reliable.",
      howItWorks: "Users access OMS v3 through a modern web interface where they can monitor application status through intuitive dashboards. When they need to automate workflows, they can visit the API documentation section to learn about available endpoints, test them directly in the browser, and implement integrations with confidence. The system tracks data flows against configurable SLAs and provides immediate visual feedback when issues arise.",
      customerQuote: "Before OMS v3, we spent hours manually checking if data had arrived on time. Now we can set SLAs and let the system alert us to problems automatically. The new API documentation has been a game-changer for our development team, allowing us to integrate OMS data into our custom dashboards,\" said Sarah Chen, Operations Manager at Acme Corporation. \"What used to take days of back-and-forth between teams now happens seamlessly.",
      getStarted: "To start using OMS v3, navigate to the dashboard at https://oms.example.com or contact the IT helpdesk for access."
    } as PRFAQ,
    faq: [
      {
        question: "What's new in OMS v3?",
        answer: "OMS v3 introduces several key improvements including interactive API documentation, enhanced SLA tracking, bulk update capabilities for datasets, and a more intuitive user interface. The new version also improves performance and adds the ability to copy API responses directly to your clipboard."
      },
      {
        question: "How do I use the API documentation?",
        answer: "Navigate to the API Docs section in the sidebar. There you'll find a list of all available API endpoints. Click on any endpoint to see details about its parameters and response formats. Use the 'Try It' tab to test the API directly in your browser by filling out the required parameters and clicking 'Send Request'."
      },
      {
        question: "Can I update multiple datasets at once?",
        answer: "Yes, OMS v3 supports bulk operations. In the Datasets view, select multiple datasets using the checkboxes, then use the action buttons that appear to update SLAs or toggle RAG inclusion status for all selected datasets simultaneously."
      },
      {
        question: "How is the status of an interface determined?",
        answer: "Interface status follows a Red/Amber/Green (RAG) system based on the datasets associated with the interface. An interface will display as Red if any included dataset has missed its SLA, Amber if any dataset is at risk of missing its SLA (due within 30 minutes), and Green if all datasets are on schedule or have arrived on time."
      },
      {
        question: "How do I exclude a dataset from status calculations?",
        answer: "In the Datasets view for an interface, you can toggle the inclusion switch for any dataset. When a dataset is excluded, its status won't affect the parent interface's status calculation. This is useful for datasets that aren't critical or are temporarily unreliable."
      },
      {
        question: "Can I programmatically access OMS data?",
        answer: "Yes, OMS v3 provides a comprehensive API for accessing and manipulating system data. Visit the API Documentation section to explore available endpoints, their parameters, and response formats. You can even test APIs directly in the browser before implementing them in your code."
      }
    ] as FAQItem[]
  });
}

export default function Manual() {
  const { title, prfaq, faq } = useLoaderData<typeof loader>();
  
  // Set the page title
  useSetPageTitle("User Manual", "Guidance on using the OMS platform");
  
  return (
    <div className="container py-8">

      <Tabs defaultValue="manual">
        <TabsList className="mb-4">
          <TabsTrigger value="manual">User Manual</TabsTrigger>
          <TabsTrigger value="prfaq">PRFAQ</TabsTrigger>
        </TabsList>

        <TabsContent value="manual" className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>OMS v3 User Manual</CardTitle>
              <CardDescription>
                This guide will help you understand and use the OMS v3 system for monitoring your applications, interfaces, and datasets.
              </CardDescription>
            </CardHeader>
            <CardContent>
                <div className="prose prose-sm max-w-none dark:prose-invert px-4 py-2">
                  <h2 className="text-2xl font-bold mb-4 mt-2 text-primary">Main Views</h2>
                  
                  <h3 className="text-xl font-semibold mb-3 mt-5 text-foreground border-b pb-1">Dashboard View</h3>
                  <p className="my-2">The Dashboard is your central command center where you can see:</p>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1"><strong className="font-semibold">Status Summary Cards:</strong> Shows you the total counts of applications, interfaces, and datasets in your system</li>
                    <li className="mb-1"><strong className="font-semibold">Interface Status Chart:</strong> Visual representation of how many interfaces are on schedule (green), at risk (yellow), or breached (red)</li>
                    <li className="mb-1"><strong className="font-semibold">Status Breakdown Cards:</strong> Detailed cards showing the percentage and count of interfaces in each status category</li>
                  </ul>
                  <p className="my-2">Use the Dashboard to quickly identify problems that need your attention, especially interfaces with red or amber status.</p>

                  <h3 className="text-xl font-semibold mb-3 mt-5 text-foreground border-b pb-1">Applications View</h3>
                  <p className="my-2">The Applications view lists all the applications in your system. Here you can:</p>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1">View application details including name, type, status, and organizational information</li>
                    <li className="mb-1">Filter applications by organization level (Level 4/Level 5)</li>
                    <li className="mb-1">See how many interfaces are connected to each application</li>
                    <li className="mb-1">Access PLADA service information when available</li>
                    <li className="mb-1">Sync application data with the HEET system</li>
                  </ul>

                  <h4 className="text-lg font-semibold mb-2 mt-4 text-foreground">Searching for Applications</h4>
                  <p className="my-2">To find specific applications:</p>
                  <ol className="my-2 pl-6 list-decimal">
                    <li className="mb-1">Enter search terms in the search box at the top of the Applications page</li>
                    <li className="mb-1">You can search by application name, ID, or PLADA service ID</li>
                    <li className="mb-1">Press Enter to execute the search</li>
                    <li className="mb-1">Use the organization filters on the right to narrow results further</li>
                    <li className="mb-1">Click the X button to clear all filters</li>
                  </ol>

                  <h3 className="text-xl font-semibold mb-3 mt-5 text-foreground border-b pb-1">Interfaces View</h3>
                  <p className="my-2">The Interfaces view shows all connections between your applications. Here you can:</p>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1">See interface details including source and target applications</li>
                    <li className="mb-1">Check the status of each interface (green, yellow, or red)</li>
                    <li className="mb-1">View when data was last synchronized</li>
                    <li className="mb-1">Access datasets connected to each interface</li>
                    <li className="mb-1">Manually sync interface data or configure auto-sync</li>
                  </ul>

                  <h4 className="text-lg font-semibold mb-2 mt-4 text-foreground">Searching for Interfaces</h4>
                  <p className="my-2">To find specific interfaces:</p>
                  <ol className="my-2 pl-6 list-decimal">
                    <li className="mb-1">Enter search terms in the search box at the top of the Interfaces page</li>
                    <li className="mb-1">You can search by interface name, ID, or related application name</li>
                    <li className="mb-1">Press Enter to execute the search</li>
                    <li className="mb-1">Use the filter controls to filter by:
                      <ul className="pl-6 mt-1 list-disc">
                        <li className="mb-1">Organization level (Level 4/Level 5)</li>
                        <li className="mb-1">Status (Red/Amber/Green)</li>
                        <li className="mb-1">Related application</li>
                      </ul>
                    </li>
                    <li className="mb-1">Click the X button to clear all filters</li>
                  </ol>

                  <h3 className="text-xl font-semibold mb-3 mt-5 text-foreground border-b pb-1">Datasets View</h3>
                  <p className="my-2">The Datasets view (accessed through an interface) shows you the data collections associated with an interface. Here you can:</p>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1">View dataset status and timing information</li>
                    <li className="mb-1">Configure SLA settings for when data should arrive</li>
                    <li className="mb-1">Include or exclude datasets from status calculations</li>
                    <li className="mb-1">View events associated with each dataset</li>
                    <li className="mb-1">Update SLA settings for multiple datasets at once</li>
                  </ul>

                  <h3 className="text-xl font-semibold mb-3 mt-5 text-foreground border-b pb-1">Events View</h3>
                  <p className="my-2">The Events view shows a chronological record of activities related to interfaces and datasets. Here you can:</p>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1">See when data arrived</li>
                    <li className="mb-1">Check if events were successful or had errors</li>
                    <li className="mb-1">Filter events by date, type, or status</li>
                    <li className="mb-1">Track the history of data transfers</li>
                  </ul>

                  <h3 className="text-xl font-semibold mb-3 mt-5 text-foreground border-b pb-1">API Documentation View</h3>
                  <p className="my-2">The API Documentation view provides interactive documentation for all available API endpoints in the system. Here you can:</p>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1">Browse available API endpoints and understand their purpose</li>
                    <li className="mb-1">See detailed parameter information for each endpoint</li>
                    <li className="mb-1">Test API endpoints directly from the documentation</li>
                    <li className="mb-1">View sample responses and error codes</li>
                    <li className="mb-1">Copy API responses to your clipboard</li>
                  </ul>

                  <h4 className="text-lg font-semibold mb-2 mt-4 text-foreground">Using the API Documentation</h4>
                  <ol className="my-2 pl-6 list-decimal">
                    <li className="mb-1">Navigate to the API Documentation page from the sidebar</li>
                    <li className="mb-1">Browse the list of available endpoints</li>
                    <li className="mb-1">Click on an endpoint to expand its details</li>
                    <li className="mb-1">Switch to the "Try It" tab to test the endpoint</li>
                    <li className="mb-1">Fill in the required parameters</li>
                    <li className="mb-1">Click "Send Request" to test the endpoint</li>
                    <li className="mb-1">View the response and copy it using the "Copy" button</li>
                  </ol>

                  <p className="my-2">The following API endpoints are available:</p>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1"><strong className="font-semibold">Applications Sync API</strong> (<code>POST /api/applications/sync</code>): Synchronize applications from HEET and their interfaces from DLAS</li>
                    <li className="mb-1"><strong className="font-semibold">DLAS Sync GET API</strong> (<code>GET /api/dlas-sync</code>): Get current DLAS synchronization status and configuration</li>
                    <li className="mb-1"><strong className="font-semibold">DLAS Sync POST API</strong> (<code>POST /api/dlas-sync</code>): Control DLAS synchronization operations</li>
                  </ul>

                  <h3 className="text-xl font-semibold mb-3 mt-5 text-foreground border-b pb-1">Settings View</h3>
                  <p className="my-2">The Settings view allows you to configure system behavior:</p>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1">Set how many items show in Quick Access (1-20 items)</li>
                    <li className="mb-1">Configure how often data automatically syncs (1-60 minutes)</li>
                    <li className="mb-1">Reset settings to defaults if needed</li>
                  </ul>

                  <h2 className="text-2xl font-bold mb-4 mt-2 text-primary">Key Features</h2>
                  
                  <h3 className="text-xl font-semibold mb-3 mt-5 text-foreground border-b pb-1">Dataset SLA Configuration</h3>
                  <p className="my-2">SLAs (Service Level Agreements) determine when datasets should arrive. The system supports various recurrence patterns:</p>

                  <h4 className="text-lg font-semibold mb-2 mt-4 text-foreground">Setting Up SLAs</h4>
                  <ol className="my-2 pl-6 list-decimal">
                    <li className="mb-1">Go to an interface's Datasets tab</li>
                    <li className="mb-1">Select one or more datasets</li>
                    <li className="mb-1">Click "Update SLA"</li>
                    <li className="mb-1">Configure the following options:</li>
                  </ol>

                  <p className="my-2"><strong>Time Settings:</strong></p>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1">Enter the expected arrival time in HH:MM:SS format (in UTC timezone)</li>
                  </ul>

                  <p className="my-2"><strong>Recurrence Pattern:</strong></p>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1"><strong>Daily</strong>: Set datasets to arrive every X day(s)</li>
                    <li className="mb-1"><strong>Weekly</strong>: Configure arrivals on specific days of the week (e.g., Monday, Wednesday, Friday)</li>
                    <li className="mb-1"><strong>Monthly</strong>: Set arrivals on specific days of the month or specific weekdays (e.g., first Monday)</li>
                    <li className="mb-1"><strong>Yearly</strong>: Configure arrivals on specific days of specific months</li>
                  </ul>

                  <p className="my-2"><strong>Interval Options:</strong></p>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1">Specify how often the pattern repeats (every 1 day, every 2 weeks, etc.)</li>
                  </ul>

                  <p className="my-2"><strong>End Conditions:</strong></p>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1">Never end</li>
                    <li className="mb-1">End after X occurrences</li>
                    <li className="mb-1">End on a specific date</li>
                  </ul>

                  <p className="my-2">The system stores these settings in RRULE format, which looks like:</p>
                  <pre className="p-2 bg-muted rounded-md"><code>FREQ=DAILY;TZID=UTC;INTERVAL=1;BYHOUR=14;BYMINUTE=30;BYSECOND=0</code></pre>

                  <h3 className="text-xl font-semibold mb-3 mt-5 text-foreground border-b pb-1">Interface and Dataset Synchronization</h3>

                  <h4 className="text-lg font-semibold mb-2 mt-4 text-foreground">Manual Synchronization</h4>
                  <p className="my-2">To manually sync an interface:</p>
                  <ol className="my-2 pl-6 list-decimal">
                    <li className="mb-1">Go to the Interfaces view</li>
                    <li className="mb-1">Find the interface you want to sync</li>
                    <li className="mb-1">Click the sync button (circular arrows icon)</li>
                    <li className="mb-1">Wait for the sync to complete</li>
                  </ol>

                  <p className="my-2">To sync multiple interfaces at once:</p>
                  <ol className="my-2 pl-6 list-decimal">
                    <li className="mb-1">Select the checkboxes next to multiple interfaces</li>
                    <li className="mb-1">Click the "Sync X Interfaces" button that appears</li>
                    <li className="mb-1">The system will update all selected interfaces</li>
                  </ol>

                  <h4 className="text-lg font-semibold mb-2 mt-4 text-foreground">Automatic Synchronization</h4>
                  <p className="my-2">To enable automatic synchronization:</p>
                  <ol className="my-2 pl-6 list-decimal">
                    <li className="mb-1">Toggle the "Auto-refresh ON/OFF" button in the Interfaces view</li>
                    <li className="mb-1">Data will refresh according to the interval set in Settings</li>
                    <li className="mb-1">You can also adjust this interval in the Settings view</li>
                  </ol>

                  <h4 className="text-lg font-semibold mb-2 mt-4 text-foreground">What Happens During Sync</h4>
                  <p className="my-2">When syncing occurs:</p>
                  <ol className="my-2 pl-6 list-decimal">
                    <li className="mb-1">The system connects to external systems (HEET and DLAS)</li>
                    <li className="mb-1">It fetches the latest application and interface information</li>
                    <li className="mb-1">It recalculates when datasets should arrive</li>
                    <li className="mb-1">It retrieves event information for tracking arrival times</li>
                    <li className="mb-1">Status indicators update based on the latest information</li>
                  </ol>

                  <h3 className="text-xl font-semibold mb-3 mt-5 text-foreground border-b pb-1">Status Calculation and Monitoring</h3>
                  <p className="my-2">The system uses a Red/Amber/Green (RAG) status system to help you monitor your interfaces and datasets:</p>

                  <h4 className="text-lg font-semibold mb-2 mt-4 text-foreground">Dataset Status Logic</h4>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1"><strong>Red (SLA Breached)</strong>: The dataset was expected today, but has either:
                      <ul className="pl-6 mt-1 list-disc">
                        <li className="mb-1">Not arrived yet and the expected time has passed, or</li>
                        <li className="mb-1">Arrived later than the expected time</li>
                      </ul>
                    </li>
                    <li className="mb-1"><strong>Amber (SLA at Risk)</strong>: The dataset is expected within the next 30 minutes but hasn't arrived yet</li>
                    <li className="mb-1"><strong>Green (On Schedule)</strong>: The dataset is on schedule (either has arrived on time or isn't yet due)</li>
                  </ul>

                  <h4 className="text-lg font-semibold mb-2 mt-4 text-foreground">Interface Status Logic</h4>
                  <p className="my-2">An interface's status is determined by its datasets:</p>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1"><strong>Red</strong>: If any dataset included in the calculation is in "red" status</li>
                    <li className="mb-1"><strong>Amber</strong>: If any dataset is in "amber" status (and none are red)</li>
                    <li className="mb-1"><strong>Green</strong>: If all included datasets are in "green" status</li>
                  </ul>

                  <h4 className="text-lg font-semibold mb-2 mt-4 text-foreground">Controlling Which Datasets Affect Status</h4>
                  <p className="my-2">You can control which datasets affect an interface's status:</p>
                  <ol className="my-2 pl-6 list-decimal">
                    <li className="mb-1">Go to the Datasets tab for an interface</li>
                    <li className="mb-1">Find the dataset you want to include/exclude</li>
                    <li className="mb-1">Toggle the inclusion switch</li>
                    <li className="mb-1">The interface status will now be calculated based on your selection</li>
                  </ol>

                  <p className="my-2">To update multiple datasets at once:</p>
                  <ol className="my-2 pl-6 list-decimal">
                    <li className="mb-1">Select several datasets using the checkboxes</li>
                    <li className="mb-1">Click "Toggle RAG Inclusion"</li>
                    <li className="mb-1">Choose whether to include or exclude the selected datasets</li>
                  </ol>

                  <h3 className="text-xl font-semibold mb-3 mt-5 text-foreground border-b pb-1">Arrival Time Calculation</h3>
                  <p className="my-2">The system tracks two important times for each dataset:</p>

                  <h4 className="text-lg font-semibold mb-2 mt-4 text-foreground">Expected Arrival Time</h4>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1">Calculated from the dataset's SLA settings</li>
                    <li className="mb-1">For daily patterns, it calculates today's expected time</li>
                    <li className="mb-1">For weekly, monthly, and yearly patterns, it finds the next expected occurrence</li>
                    <li className="mb-1">All times are in UTC format for consistency across time zones</li>
                  </ul>

                  <h4 className="text-lg font-semibold mb-2 mt-4 text-foreground">Last Arrival Time</h4>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1">Records when data actually arrived</li>
                    <li className="mb-1">Updated when a synchronization event is detected</li>
                    <li className="mb-1">Used to determine if data arrived on time</li>
                    <li className="mb-1">Stored in UTC format</li>
                  </ul>

                  <p className="my-2">The system compares these times to determine if SLAs are being met and calculates the appropriate status color.</p>

                  <h2 className="text-2xl font-bold mb-4 mt-2 text-primary">API Integration</h2>

                  <h3 className="text-xl font-semibold mb-3 mt-5 text-foreground border-b pb-1">Using the OMS APIs</h3>
                  <p className="my-2">OMS v3 provides a set of RESTful APIs that allow you to programmatically interact with the system:</p>

                  <h4 className="text-lg font-semibold mb-2 mt-4 text-foreground">Available APIs</h4>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1"><strong>Applications Sync API</strong>: Trigger synchronization of applications and their interfaces</li>
                    <li className="mb-1"><strong>DLAS Sync API</strong>: Control and monitor the DLAS synchronization process</li>
                  </ul>

                  <h4 className="text-lg font-semibold mb-2 mt-4 text-foreground">Authentication and Requests</h4>
                  <p className="my-2">All API requests are made using standard HTTP methods with form data or query parameters. The system uses the same authentication as the web interface.</p>

                  <h4 className="text-lg font-semibold mb-2 mt-4 text-foreground">Testing APIs</h4>
                  <p className="my-2">You can test all available APIs directly from the API Documentation page without writing any code. This allows you to:</p>
                  <ol className="my-2 pl-6 list-decimal">
                    <li className="mb-1">Understand the required parameters</li>
                    <li className="mb-1">See sample request formats</li>
                    <li className="mb-1">View sample responses</li>
                    <li className="mb-1">Copy response data for use in your applications</li>
                  </ol>

                  <h2 className="text-2xl font-bold mb-4 mt-2 text-primary">Data Sources</h2>
                  <p className="my-2">The OMS system integrates with two main external systems:</p>

                  <h3 className="text-xl font-semibold mb-3 mt-5 text-foreground border-b pb-1">HEET Integration</h3>
                  <p className="my-2">HEET provides application information including:</p>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1">Application details and status</li>
                    <li className="mb-1">Organizational structure (Level 2-5 hierarchy)</li>
                    <li className="mb-1">PLADA service IDs and metadata</li>
                  </ul>

                  <h3 className="text-xl font-semibold mb-3 mt-5 text-foreground border-b pb-1">DLAS Integration</h3>
                  <p className="my-2">DLAS provides interface and dataset information:</p>
                  <ul className="my-2 pl-6 list-disc">
                    <li className="mb-1">Interface connections between applications</li>
                    <li className="mb-1">Dataset details and status</li>
                    <li className="mb-1">Event IDs and event logging</li>
                  </ul>

                  <p className="my-2">These integrations ensure that the OMS system has the latest information about your applications and interfaces.</p>
                </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="prfaq" className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>{prfaq.headline}</CardTitle>
              <CardDescription>{prfaq.subtitle}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Release Date</h3>
                  <p>{prfaq.date}</p>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="text-lg font-medium mb-2">Introduction</h3>
                  <p>{prfaq.intro}</p>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-2">The Problem</h3>
                  <p>{prfaq.problem}</p>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-2">Our Solution</h3>
                  <p>{prfaq.solution}</p>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-2">Leadership Perspective</h3>
                  <blockquote className="border-l-4 border-primary pl-4 italic">
                    {prfaq.leaderQuote}
                  </blockquote>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-2">How It Works</h3>
                  <p>{prfaq.howItWorks}</p>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-2">Customer Success</h3>
                  <blockquote className="border-l-4 border-primary pl-4 italic">
                    {prfaq.customerQuote}
                  </blockquote>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium mb-2">Getting Started</h3>
                  <p>{prfaq.getStarted}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Frequently Asked Questions</CardTitle>
              <CardDescription>Common questions about OMS v3</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {faq.map((item, index) => (
                  <div key={index}>
                    <h3 className="text-lg font-medium mb-2">{item.question}</h3>
                    <p className="text-muted-foreground">{item.answer}</p>
                    {index < faq.length - 1 && <Separator className="mt-4" />}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 