import { AlertCircle, Clock, Play, StopCircle } from 'lucide-react';
import { DLASSyncState } from '~/types/dlas-sync';

interface StatusIconProps {
  state: string;
}

/**
 * Component that returns the appropriate icon based on sync state
 */
export function StatusIcon({ state = DLASSyncState.IDLE }: StatusIconProps) {
  switch (state) {
    case DLASSyncState.RUNNING:
      return <Play className="h-5 w-5 text-blue-500" />;
    case DLASSyncState.ERROR:
      return <AlertCircle className="h-5 w-5 text-red-500" />;
    case DLASSyncState.IDLE:
      return <StopCircle className="h-5 w-5 text-green-500" />;
    default:
      return <Clock className="h-5 w-5 text-gray-500" />;
  }
} 