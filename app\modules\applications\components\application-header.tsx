/**
 * Props for the application header component
 */
import { useEffect } from "react";
import { usePageTitle } from "~/contexts/page-title-context";
import { useSetPageTitle } from "~/hooks/use-set-page-title";

interface ApplicationHeaderProps {
  error?: string;
  isLoading: boolean;
}

/**
 * Header component for the applications page
 */
export function ApplicationHeader({ error, isLoading }: ApplicationHeaderProps) {
  // Set the page title
  useSetPageTitle("Applications", "Manage and monitor your application systems");
  
  return (
    <div>
      {error && (
        <p className="text-sm text-red-500 mt-2">
          {error}
        </p>
      )}
    </div>
  );
} 