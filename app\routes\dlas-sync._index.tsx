import { json, type LoaderFunction } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { getDLASSyncService, getNextScheduledRunTime } from "~/services/dlas-sync.server";
import { DLASSyncMonitor } from "~/components/dlas-sync-monitor";
import { handleApiError } from "~/utils/error-handling.server";
import { useEffect } from "react";
import { useSetPageTitle } from "~/hooks/use-set-page-title";

// Loader function to fetch initial data
export const loader: LoaderFunction = async () => {
  try {
    const syncService = getDLASSyncService();
    
    // Get initial data
    const { state, progress } = syncService.getSyncStatus();
    
    // Get next scheduled run time
    const nextScheduledRunTime = getNextScheduledRunTime();
    
    // Prepare data for client
    return json({
      state,
      progress: {
        totalItems: progress.totalItems,
        processedItems: progress.processedItems,
        successCount: progress.successCount,
        errorCount: progress.errorCount,
        errors: progress.errors,
        startTime: progress.startTime ? progress.startTime.toISOString() : null,
        endTime: progress.endTime ? progress.endTime.toISOString() : null,
        elapsedTimeMs: progress.elapsedTimeMs,
      },
      nextScheduledRunTime: nextScheduledRunTime.toISOString(),
      scheduledSyncInfo: {
        hour: 6,
        minute: 0,
        description: 'Daily sync at 6:00 AM'
      }
    });
  } catch (error) {
    return handleApiError(error);
  }
};

export default function DLASSyncPage() {
  const initialStatus = useLoaderData<typeof loader>();
  
  // Set the page title
  useSetPageTitle("DLAS Data Sync", "Monitor and manage DLAS data synchronization status");
  
  return (
    <div className="px-6 py-6 space-y-6">
      <DLASSyncMonitor initialStatus={initialStatus} />
    </div>
  );
} 