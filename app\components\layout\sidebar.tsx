"use client"

import * as React from "react"
import { cn } from "~/utils/cn"
import { But<PERSON> } from "~/components/ui/button"
import { ScrollArea } from "~/components/ui/scroll-area"
import { Sheet, SheetContent, SheetTrigger } from "~/components/ui/sheet"
import {
  Menu,
  LayoutDashboard,
  AppWindow,
  Share2,
  Settings,
  RefreshCw,
  Code,
  FileText,
} from "lucide-react"
import { NavLink, useLocation } from "@remix-run/react"

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {}

const SidebarContext = React.createContext<{
  collapsed: boolean
  setCollapsed: (collapsed: boolean) => void
}>({
  collapsed: true,
  setCollapsed: () => {},
})

export function SidebarProvider({
  children,
}: {
  children: React.ReactNode
}) {
  // Always set collapsed to true and ignore any attempts to change it
  const [collapsed, setCollapsed] = React.useState(true);

  // Create a no-op function for setCollapsed to prevent state changes
  const handleSetCollapsed = React.useCallback(() => {
    // No-op - sidebar will always stay collapsed
  }, []);

  return (
    <SidebarContext.Provider value={{ collapsed, setCollapsed: handleSetCollapsed }}>
      {children}
    </SidebarContext.Provider>
  )
}

export function useSidebar() {
  const context = React.useContext(SidebarContext)
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider")
  }
  return context
}

// Updated navigation paths with base path
const mainNav = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Applications",
    href: "/applications",
    icon: AppWindow,
  },
  {
    title: "Interfaces",
    href: "/interfaces",
    icon: Share2,
  },
  {
    title: "DLAS Sync",
    href: "/dlas-sync",
    icon: RefreshCw,
  },
  {
    title: "API Docs",
    href: "/api-docs",
    icon: Code,
  },
  {
    title: "User Manual",
    href: "/manual",
    icon: FileText,
  },
] as const;

// Memoized NavLink component
const SidebarNavLink = React.memo(function SidebarNavLink({
  href,
  icon: Icon,
  title,
  collapsed,
  onClick,
  searchParams,
}: {
  href: string;
  icon: typeof Share2;
  title: string;
  collapsed?: boolean;
  onClick?: () => void;
  searchParams: URLSearchParams;
}) {
  const level4 = searchParams.get("level4");
  const level5 = searchParams.get("level5");
  
  const to = React.useMemo(() => {
    if (!level4 && !level5) return href;
    const params = new URLSearchParams();
    if (level4) params.set("level4", level4);
    if (level5) params.set("level5", level5);
    return `${href}?${params.toString()}`;
  }, [href, level4, level5]);

  return (
    <NavLink
      to={to}
      onClick={onClick}
      prefetch="none"
      preventScrollReset
      replace={false}
      className={({ isActive }) =>
        cn(
          "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
          isActive ? "bg-accent" : "transparent",
          collapsed && "justify-center px-2"
        )
      }
    >
      <Icon className="h-4 w-4" />
      {!collapsed && title}
    </NavLink>
  );
});

export function SidebarTrigger() {
  // Hide this button as it no longer serves a purpose
  return null;
}

export function Sidebar({ className }: SidebarProps) {
  const { collapsed } = useSidebar()
  const [isOpen, setIsOpen] = React.useState(false)
  const location = useLocation()
  const searchParams = new URLSearchParams(location.search)

  return (
    <>
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden fixed left-4 top-4 z-40"
          >
            <Menu className="h-6 w-6" />
            <span className="sr-only">Toggle sidebar</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-[300px] p-0">
          <div className="flex h-full flex-col">
            <ScrollArea className="flex-1">
              <nav className="grid gap-1 p-4">
                {mainNav.map((item) => (
                  <SidebarNavLink
                    key={item.href}
                    {...item}
                    onClick={() => setIsOpen(false)}
                    searchParams={searchParams}
                  />
                ))}
              </nav>
            </ScrollArea>
            <div className="p-4">
              <SidebarNavLink
                href="/settings"
                icon={Settings}
                title="Settings"
                onClick={() => setIsOpen(false)}
                searchParams={searchParams}
              />
            </div>
          </div>
        </SheetContent>
      </Sheet>
      <aside
        data-collapsed={collapsed}
        className={cn(
          "fixed left-0 top-16 z-30 hidden h-[calc(100vh-4rem)] w-[300px] border-r bg-background transition-all duration-300",
          collapsed && "w-[80px]",
          "lg:block",
          className
        )}
      >
        <div className="flex h-full flex-col">
          <ScrollArea className="flex-1">
            <nav className="grid gap-1 p-4">
              {mainNav.map((item) => (
                <SidebarNavLink
                  key={item.href}
                  {...item}
                  collapsed={collapsed}
                  searchParams={searchParams}
                />
              ))}
            </nav>
          </ScrollArea>
          <div className="p-4">
            <SidebarNavLink
              href="/settings"
              icon={Settings}
              title="Settings"
              collapsed={collapsed}
              searchParams={searchParams}
            />
          </div>
        </div>
      </aside>
    </>
  )
} 