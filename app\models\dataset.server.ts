import { db } from "~/db/db.server";
import { datasets } from "~/db/schema/datasets";
import { eq, inArray, count, and, or, sql } from "drizzle-orm";
import type { DatasetWithSLA } from "~/db/types/datasets";
import pkg from 'rrule';
import { applications } from "~/db/schema/applications";
import { interfaces } from "~/db/schema/interfaces";
import { events } from "~/db/schema/events";
const { RRule } = pkg;

export class DatasetModel {
  /**
   * Calculate expected arrival time based on SLA
   */
  static calculateExpectedArrivalTime(sla: string | null): Date | null {
    if (!sla) return null;

    try {
      // Create consistent UTC dates
      const now = new Date();
      const utcNow = new Date(now.toISOString());
      
      // Create UTC date for today (midnight UTC)
      const utcToday = new Date(Date.UTC(
        utcNow.getUTCFullYear(),
        utcNow.getUTCMonth(),
        utcNow.getUTCDate()
      ));
      
      const rule = RRule.fromString(sla);
      const nextOccurrence = rule.after(utcNow, true);
      const mostRecentOccurrence = rule.before(utcNow, true);

      if (!nextOccurrence) return null;

      let result: Date | null = null;
      
      // Helper function to check if a date is today in UTC
      const isUtcToday = (date: Date): boolean => {
        return date.getUTCFullYear() === utcToday.getUTCFullYear() &&
               date.getUTCMonth() === utcToday.getUTCMonth() &&
               date.getUTCDate() === utcToday.getUTCDate();
      };
      
      // If next occurrence is today, use it
      if (isUtcToday(nextOccurrence)) {
        result = nextOccurrence;
      }
      // If most recent occurrence is today, use it
      else if (mostRecentOccurrence && isUtcToday(mostRecentOccurrence)) {
        result = mostRecentOccurrence;
      }
      // Otherwise use next occurrence
      else {
        result = nextOccurrence;
      }
      
      // Return the result directly - it's already in UTC format
      return result;
    } catch (error) {
      console.error("Error calculating expected arrival time:", error);
      return null;
    }
  }

  /**
   * Find all datasets for a given interface
   */
  static async findByInterfaceId(interfaceId: string): Promise<DatasetWithSLA[]> {
    const results = await db
      .select()
      .from(datasets)
      .where(eq(datasets.omsInterfaceId, interfaceId))
      .orderBy(datasets.datasetName);

    return results.map(dataset => ({
      ...dataset,
      events: [], // TODO: Implement event loading
      // Convert Date object to timestamp number for lastArrivalTime
      lastArrivalTime: dataset.lastArrivalTime ? 
        dataset.lastArrivalTime instanceof Date ? 
          dataset.lastArrivalTime.getTime() : 
          dataset.lastArrivalTime 
        : null
    }));
  }

  /**
   * Update SLA for multiple datasets
   */
  static async updateSLA(datasetIds: string[], sla: string): Promise<void> {
    // Calculate the expected arrival time based on the new SLA
    const expectedArrivalTime = this.calculateExpectedArrivalTime(sla);

    // Update all selected datasets with new SLA and expected arrival time
    await db.update(datasets)
      .set({ 
        sla,
        expectedArrivalTime,
        updatedAt: new Date()
      })
      .where(inArray(datasets.datasetName, datasetIds));
  }

  /**
   * Update last arrival time for multiple datasets
   * @param datasetLatestDates Map of dataset names to their latest arrival dates
   * @returns Array of updated dataset names
   */
  static async updateLastArrivalTime(
    datasetLatestDates: Map<string, Date>
  ): Promise<string[]> {
    const updatedDatasets: string[] = [];
    
    for (const [datasetName, latestDate] of datasetLatestDates.entries()) {
      try {
        await db
          .update(datasets)
          .set({ 
            lastArrivalTime: latestDate,
            updatedAt: new Date()
          })
          .where(eq(datasets.datasetName, datasetName));
        
        updatedDatasets.push(datasetName);
      } catch (error) {
        console.error(`Error updating last arrival time for dataset ${datasetName}:`, error);
      }
    }
    
    return updatedDatasets;
  }

  /**
   * Recalculate and update expected arrival time for a dataset
   */
  static async updateExpectedArrivalTime(datasetName: string): Promise<void> {
    const dataset = await db
      .select({ sla: datasets.sla })
      .from(datasets)
      .where(eq(datasets.datasetName, datasetName))
      .get();

    if (!dataset) return;

    const expectedArrivalTime = this.calculateExpectedArrivalTime(dataset.sla);

    await db.update(datasets)
      .set({ 
        expectedArrivalTime,
        updatedAt: new Date()
      })
      .where(eq(datasets.datasetName, datasetName));
  }

  /**
   * Update RAG status inclusion flag for multiple datasets
   */
  static async updateRagInclusion(datasetIds: string[], include: boolean): Promise<void> {
    await db
      .update(datasets)
      .set({ 
        includeInRagStatus: include 
      })
      .where(inArray(datasets.datasetName, datasetIds));
  }

  /**
   * Toggle RAG status inclusion for a single dataset
   */
  static async toggleRagInclusion(datasetName: string, include: boolean): Promise<void> {
    await db
      .update(datasets)
      .set({ 
        includeInRagStatus: include 
      })
      .where(eq(datasets.datasetName, datasetName));
  }

  /**
   * Update event count for multiple datasets by querying the events table
   * @param datasetNames Array of dataset names
   */
  static async updateEventCount(datasetNames: string[]): Promise<void> {
    if (!datasetNames || datasetNames.length === 0) return;
    
    for (const datasetName of datasetNames) {
      // Count events for this dataset
      const eventCountResult = await db
        .select({ count: count() })
        .from(events)
        .where(eq(events.datasetName, datasetName));
      
      const eventCount = eventCountResult[0]?.count || 0;
      
      // Update the dataset with the count
      await db
        .update(datasets)
        .set({ 
          eventCount,
          updatedAt: new Date()
        })
        .where(eq(datasets.datasetName, datasetName));
    }
  }

  /**
   * Count datasets based on organization filters
   * 
   * This performs a join operation across applications, interfaces, and datasets
   * to accurately count datasets that belong to interfaces connected to applications
   * within the specified organization filters.
   */
  static async countByOrgFilters(options: { 
    orgLevel4?: string;
    orgLevel5?: string;
    applicationIds?: string[];
  } = {}): Promise<number> {
    const { orgLevel4, orgLevel5, applicationIds } = options;
    const conditions = [];

    // Build query conditions
    if (orgLevel4 && orgLevel4 !== "all") {
      conditions.push(eq(applications.orgLevel4, orgLevel4));
      if (orgLevel5 && orgLevel5 !== "all") {
        conditions.push(eq(applications.orgLevel5, orgLevel5));
      }
    }

    // Add application ID filter if provided
    if (applicationIds?.length) {
      conditions.push(
        or(
          inArray(interfaces.sendAppId, applicationIds),
          inArray(interfaces.receivedAppId, applicationIds)
        )
      );
    }

    // Create the query
    const query = db
      .select({ count: count() })
      .from(datasets)
      .leftJoin(
        interfaces,
        eq(datasets.omsInterfaceId, interfaces.omsInterfaceId)
      )
      .leftJoin(
        applications,
        or(
          eq(interfaces.sendAppId, applications.applicationInstanceId),
          eq(interfaces.receivedAppId, applications.applicationInstanceId)
        )
      );
    
    // Add filter conditions if present
    const result = conditions.length > 0
      ? await query.where(and(...conditions))
      : await query;
    
    return result[0]?.count || 0;
  }

  /**
   * Calculate RAG status for multiple datasets
   * 
   * This method is similar to InterfaceRagStatus.calculateBulkRagStatus but works directly with dataset IDs
   * 
   * @param datasetIds Array of dataset IDs to calculate RAG status for
   * @returns Record of dataset IDs to their RAG status results
   */
  static async calculateRagStatus(datasetIds: string[]): Promise<Record<string, { counts: { red: number; amber: number; green: number } }>> {
    const results: Record<string, { counts: { red: number; amber: number; green: number } }> = {};
    
    // Initialize all datasets with 0 counts
    datasetIds.forEach(id => {
      results[id] = { 
        counts: { red: 0, amber: 0, green: 0 } 
      };
    });
    
    if (datasetIds.length === 0) {
      return results;
    }
    
    // Create a date object that's explicitly in UTC to match database dates
    const now = new Date();
    // Convert to UTC by setting to ISO string and parsing back
    const utcNow = new Date(now.toISOString());
    
    // Find all datasets with the provided IDs
    const datasetRecords = await db
      .select()
      .from(datasets)
      .where(inArray(datasets.datasetName, datasetIds));
    
    // Process each dataset and determine its status
    for (const dataset of datasetRecords) {
      const id = dataset.datasetName;
      
      // Only include datasets that are marked for RAG status inclusion
      if (dataset.includeInRagStatus) {
        if (dataset.expectedArrivalTime) {
          const expectedArrival = new Date(dataset.expectedArrivalTime);
          
          // Calculate time difference in minutes
          const diffMinutes = (expectedArrival.getTime() - utcNow.getTime()) / (1000 * 60);
          
          // Check if dataset is in RED status
          // RED: Expected arrival time has passed and dataset hasn't arrived
          if (
            expectedArrival < utcNow && 
            new Date(expectedArrival).toDateString() === new Date().toDateString() && // Expected today
            (!dataset.lastArrivalTime || 
              new Date(dataset.lastArrivalTime) < expectedArrival || 
              new Date(dataset.lastArrivalTime).toDateString() !== new Date().toDateString()) // Not arrived today
          ) {
            results[id].counts.red = 1;
          }
          // Check if dataset is in AMBER status
          // AMBER: Within 30 minutes of expected arrival and hasn't arrived yet
          else if (
            diffMinutes >= 0 && 
            diffMinutes < 30 && 
            (!dataset.lastArrivalTime || 
              new Date(dataset.lastArrivalTime).toDateString() !== new Date().toDateString())
          ) {
            results[id].counts.amber = 1;
          }
          // Otherwise, dataset is GREEN
          else {
            results[id].counts.green = 1;
          }
        } else {
          // If no expected arrival time, default to GREEN
          results[id].counts.green = 1;
        }
      } else {
        // Datasets not included in RAG status are considered GREEN
        results[id].counts.green = 1;
      }
    }
    
    return results;
  }

  /**
   * Get all datasets associated with a specific interface ID
   * @param interfaceId The OMS interface ID
   * @returns Array of datasets with their names and other properties
   */
  static async getDatasetsByInterfaceId(interfaceId: string): Promise<{ name: string }[]> {
    const results = await db
      .select({
        name: datasets.datasetName,
      })
      .from(datasets)
      .where(eq(datasets.omsInterfaceId, interfaceId));
    
    return results;
  }

  /**
   * Update expected arrival times for all datasets of an interface
   */
  static async updateExpectedArrivalTimes(interfaceId: string): Promise<void> {
    // Get all datasets for this interface
    const interfaceDatasets = await db
      .select({ datasetName: datasets.datasetName, sla: datasets.sla })
      .from(datasets)
      .where(eq(datasets.omsInterfaceId, interfaceId));

    // Update each dataset's expected arrival time
    await Promise.all(
      interfaceDatasets.map(async dataset => {
        const expectedArrivalTime = DatasetModel.calculateExpectedArrivalTime(dataset.sla);
        await db.update(datasets)
          .set({ 
            expectedArrivalTime,
            updatedAt: new Date()
          })
          .where(eq(datasets.datasetName, dataset.datasetName));
      })
    );
  }
} 