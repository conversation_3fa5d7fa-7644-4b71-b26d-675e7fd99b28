# DLAS Sync Service Architecture

## Overview

The DLAS Sync Service is responsible for synchronizing data from the DLAS API into the local database. The service has been refactored to follow best practices for maintainability, testability, and separation of concerns.

## Module Structure

The DLAS sync functionality has been split into the following modules:

### 1. `dlas-sync.server.ts`

The main entry point and orchestrator for DLAS synchronization. This service:
- Provides a public API for triggering sync operations
- Orchestrates the other components
- Maintains a clean interface for external consumers

### 2. `dlas-sync-progress.server.ts`

Responsible for tracking and reporting progress of sync operations:
- Maintains the state of the sync operation
- Tracks success, error, and skip counts
- Calculates estimated completion time
- Emits progress events

### 3. `dlas-sync-config.server.ts`

Manages configuration for sync operations:
- Provides default configuration values
- Merges user-provided options with defaults
- Handles scheduled sync calculation

### 4. `dlas-sync-state.server.ts`

Ensures safe state transitions:
- Prevents race conditions during state changes
- Implements debouncing to avoid rapid state transitions
- Provides a clean API for managing state transitions

### 5. `dlas-sync-broadcast.server.ts`

Handles event broadcasting to clients:
- Manages WebSocket/SSE communication
- Safely handles broadcasting errors
- Lazy-loads dependencies to avoid circular references

### 6. `dlas-sync-processor.server.ts`

Contains the core processing logic:
- Fetches and processes application data
- Handles interface and event synchronization
- Manages error reporting and retries

### 7. `dlas-sync-scheduler.server.ts`

Manages scheduled sync operations:
- Sets up daily sync schedules
- Handles the timing of scheduled operations
- Manages cancelation of scheduled operations

## Benefits of the Refactored Architecture

### Improved Maintainability
- Each module has a single responsibility
- Clear separation of concerns
- Smaller files that are easier to understand
- Better naming conventions and documentation

### Enhanced Testability
- Components can be tested in isolation
- Dependencies are more explicit and injectable
- Reduced use of global state
- Clearer state management

### Better Error Handling
- More consistent error reporting
- Centralized error handling
- Better error propagation

### Cleaner Interfaces
- Clear, well-documented public APIs
- Logical grouping of related functionality
- Better encapsulation of implementation details

## Usage Example

```typescript
import { getDLASSyncService, setupDailySync } from '~/services/dlas-sync.server';

// Get the singleton instance
const syncService = getDLASSyncService();

// Start a sync with default options
await syncService.syncAllApplications();

// Start a sync with custom options
await syncService.syncAllApplications({
  batchSize: 10,
  delayBetweenBatches: 5000
});

// Setup daily sync at 2:30 AM
setupDailySync(2, 30);

// Pause, resume, or cancel a sync
await syncService.pauseSync();
await syncService.resumeSync();
await syncService.cancelSync();

// Get current sync status
const status = syncService.getSyncStatus();
``` 