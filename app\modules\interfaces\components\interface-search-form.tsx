import { useRef } from 'react';
import { useSubmit } from '@remix-run/react';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { X } from 'lucide-react';
import type { Application } from '~/db/types/applications';
import { CommonSearchForm } from '~/components/common-search-form';

interface InterfaceSearchFormProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  hasFilters: boolean;
  onClearFilters: () => void;
  searchParams: URLSearchParams;
  setSearchParams: (params: URLSearchParams) => void;
  selectedApplication?: Application;
}

export function InterfaceSearchForm({
  searchValue,
  onSearchChange,
  hasFilters,
  onClearFilters,
  searchParams,
  setSearchParams,
  selectedApplication
}: InterfaceSearchFormProps) {
  const formRef = useRef<HTMLFormElement>(null);
  const submit = useSubmit();

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    
    if (formRef.current) {
      const formData = new FormData(formRef.current);
      
      // Keep existing filters
      const currentLevel4 = searchParams.get("level4");
      if (currentLevel4) formData.set("level4", currentLevel4);
      
      const currentLevel5 = searchParams.get("level5");
      if (currentLevel5) formData.set("level5", currentLevel5);
      
      submit(formData, { method: "get", replace: true });
    }
  };

  const removeQueryFilter = () => {
    const newParams = new URLSearchParams(searchParams);
    newParams.delete("query");
    setSearchParams(newParams);
    onSearchChange('');
  };

  const removeApplicationFilter = () => {
    const newParams = new URLSearchParams(searchParams);
    newParams.delete("applicationId");
    setSearchParams(newParams);
  };
  
  // Create additional filters for application
  const applicationFilter = selectedApplication && (
    <Badge variant="outline" className="flex items-center gap-1">
      Application: {selectedApplication.name}
      <Button 
        variant="ghost" 
        size="icon" 
        className="h-3 w-3 ml-1 p-0" 
        onClick={removeApplicationFilter}
        type="button"
      >
        <X className="h-3 w-3" />
      </Button>
    </Badge>
  );

  return (
    <CommonSearchForm
      searchValue={searchValue}
      onSearchChange={onSearchChange}
      hasFilters={hasFilters}
      onClearFilters={onClearFilters}
      formRef={formRef}
      onSubmit={handleSubmit}
      placeholder="Search by interface ID or name... (press Enter to search)"
      showActiveFilters={true}
      searchParams={searchParams}
      setSearchParams={setSearchParams}
      removeFilterCallback={(key) => {
        if (key === "query") {
          removeQueryFilter();
        }
      }}
      additionalFilters={applicationFilter}
    />
  );
} 