---
description: 
globs: 
alwaysApply: false
---
# File Conventions

## Naming Conventions

### Components
- Use PascalCase for component files: `ComponentName.tsx`
- Place in appropriate subdirectory under `app/components/`
- Group related components in feature-specific directories

### Routes
- Use kebab-case for route files: `route-name.tsx`
- Nested routes follow Remix conventions: `parent/child.tsx`
- Place route-specific components in `app/routes/`

### Utilities and Hooks
- Use camelCase for utility files: `utilityName.ts`
- Custom hooks prefix with 'use': `useHookName.ts`
- Place in appropriate directory: `app/utils/` or `app/hooks/`

### Types and Interfaces
- Use PascalCase for type files: `TypeName.ts`
- Group related types in feature-specific files
- Place in `app/types/` or alongside components

### Services and APIs
- Use camelCase for service files: `serviceName.ts`
- Group by feature or domain
- Place in `app/services/`

### Database
- Schema files in `app/db/schema/`
- Types in `app/db/types/`
- Migrations in `drizzle/`

### Assets
- Use kebab-case for asset files: `asset-name.ext`
- Place in appropriate subdirectory under `public/`
- Group by type (images, icons, etc.)

## Directory Structure
- Feature-first organization
- Co-locate related files
- Keep directory depth reasonable
- Use index files for exports

