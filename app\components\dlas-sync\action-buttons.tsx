import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Label } from '~/components/ui/label';
import { Loader2, Play, RefreshCw, StopCircle } from 'lucide-react';
import { DLASSyncState } from '~/types/dlas-sync';
import { useState } from 'react';

interface ActionButtonsProps {
  state: string;
  isSubmitting: boolean;
  isActionPending: (action?: string) => boolean;
  performAction: (action: string, applicationId?: string, date?: string) => void;
}

export function ActionButtons({ 
  state, 
  isSubmitting, 
  isActionPending, 
  performAction 
}: ActionButtonsProps) {
  const [applicationId, setApplicationId] = useState('');
  const [selectedDate, setSelectedDate] = useState<string>('');
  
  // Calculate min and max dates for date picker (30 days in the past)
  const getDateLimits = () => {
    const today = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);
    
    return {
      min: thirtyDaysAgo.toISOString().split('T')[0], // Format: YYYY-MM-DD
      max: today.toISOString().split('T')[0]          // Format: YYYY-MM-DD
    };
  };
  
  const dateLimits = getDateLimits();
  
  const handleAction = (action: string) => {
    performAction(action, applicationId.trim() || undefined, selectedDate || undefined);
  };
  
  const renderApplicationInput = () => (
    <div className="mb-4">
      <Input
        id="applicationId"
        type="string"
        value={applicationId}
        onChange={(e) => {
          const value = e.target.value;
          // Only allow positive numbers
          if (value === '' || parseInt(value) >= 0) {
            setApplicationId(value);
          }
        }}
        placeholder="Enter application ID (numbers only)"
        className="w-full"
        disabled={state === DLASSyncState.RUNNING}
        min="0"
      />
      <p className="text-xs text-muted-foreground mt-1">
        Leave blank to sync all applications
      </p>
    </div>
  );
  
  const renderDateInput = () => (
    <div className="mb-4">
      <Label htmlFor="sync-date" className="text-sm font-medium text-gray-700">
        Sync Date (Optional, last 30 days)
      </Label>
      <Input
        id="sync-date"
        type="date"
        className="w-full mt-1"
        value={selectedDate}
        onChange={(e) => setSelectedDate(e.target.value)}
        min={dateLimits.min}
        max={dateLimits.max}
        disabled={state === DLASSyncState.RUNNING}
      />
      <p className="text-xs text-muted-foreground mt-1">
        Leave empty to sync with the latest data
      </p>
    </div>
  );
  
  // Idle state - Show Start button
  if (state === DLASSyncState.IDLE) {
    return (
      <div>
        {renderApplicationInput()}
        {renderDateInput()}
        <Button
          onClick={() => handleAction('start')}
          disabled={isActionPending()}
          className="bg-blue-500 hover:bg-blue-600 text-white"
          aria-label="Start synchronization"
        >
          {isSubmitting && state === DLASSyncState.IDLE ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />
          ) : (
            <Play className="mr-2 h-4 w-4" aria-hidden="true" />
          )}
          Start Sync
        </Button>
      </div>
    );
  }
  
  // Running state - Show Stop button
  if (state === DLASSyncState.RUNNING) {
    return (
      <div>
        {renderApplicationInput()}
        {renderDateInput()}
        <Button
          onClick={() => handleAction('stop')}
          disabled={false}
          variant="destructive"
          aria-label="Stop synchronization"
        >
          {isSubmitting && state === DLASSyncState.RUNNING ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />
          ) : (
            <StopCircle className="mr-2 h-4 w-4" aria-hidden="true" />
          )}
          Stop Sync
        </Button>
      </div>
    );
  }
  
  // Error state - Show Restart button
  return (
    <div>
      {renderApplicationInput()}
      {renderDateInput()}
      <Button
        onClick={() => handleAction('start')}
        disabled={isActionPending()}
        className="bg-blue-500 hover:bg-blue-600 text-white"
        aria-label="Restart synchronization"
      >
        {isSubmitting && state === DLASSyncState.ERROR ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />
        ) : (
          <RefreshCw className="mr-2 h-4 w-4" aria-hidden="true" />
        )}
        Restart Sync
      </Button>
    </div>
  );
} 