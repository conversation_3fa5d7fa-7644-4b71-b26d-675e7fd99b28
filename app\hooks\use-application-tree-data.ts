import { useState, useEffect } from 'react';
import type { ApplicationWithRagStatus } from '~/models/application.server';
import type { TreeNodeData } from '~/components/application-tree-view/tree-view';
import { useSearchParams } from '@remix-run/react';
import type { HeetOrganizationApiResponse } from "~/db/types/organization";
import { useApplicationTree } from "~/components/application-tree-view/application-tree-data";
import React from 'react';

export interface ApplicationTreeFilters {
  orgLevel4?: string;
  orgLevel5?: string;
  query?: string;
}

export interface UseApplicationTreeDataOptions {
  applications: ApplicationWithRagStatus[];
  organizations?: HeetOrganizationApiResponse[];
  preserveState?: boolean;
  routeKey: 'applications' | 'interfaces' | 'dashboard';
}

export interface UseApplicationTreeDataReturn {
  treeData: TreeNodeData[];
  expandedIds: string[];
  setExpandedIds: (ids: string[]) => void;
  activeNodeId?: string;
  setActiveNodeId: (id: string | undefined) => void;
  filters: ApplicationTreeFilters;
  setFilters: (filters: ApplicationTreeFilters) => void;
}

/**
 * Hook to manage application tree data and state
 * Provides unified data structure and state management across different routes
 */
export function useApplicationTreeData({
  applications,
  organizations = [],
  preserveState = true,
  routeKey
}: UseApplicationTreeDataOptions): UseApplicationTreeDataReturn {
  const [searchParams, setSearchParams] = useSearchParams();
  
  // Extract current filters from search params
  const filters = useState(() => ({
    orgLevel4: searchParams.get('level4') || undefined,
    orgLevel5: searchParams.get('level5') || undefined,
    query: searchParams.get('query') || undefined
  }));

  // Add refs to track initialization state and previous filters
  const expandedIdsInitialized = React.useRef(false);
  const previousFilters = React.useRef<string>('');

  // Replace the current tree generation with useApplicationTree hook
  const treeData = useApplicationTree(applications, organizations);

  // Client-side only state
  const [expandedIds, setExpandedIds] = useState<string[]>([]);
  const [activeNodeId, setActiveNodeId] = useState<string | undefined>(undefined);

  // Load saved state on client side only
  useEffect(() => {
    if (preserveState && typeof window !== 'undefined') {
      try {
        const savedState = localStorage.getItem(`tree_state_${routeKey}`);
        if (savedState) {
          const { expandedIds: savedExpandedIds, activeNodeId: savedActiveNodeId } = JSON.parse(savedState);
          setExpandedIds(savedExpandedIds || []);
          setActiveNodeId(savedActiveNodeId);
          expandedIdsInitialized.current = true;
        }
      } catch (error) {
        console.error('Error loading saved tree state:', error);
      }
    }
  }, [preserveState, routeKey]);
  
  // Save state changes
  useEffect(() => {
    if (preserveState && typeof window !== 'undefined') {
      try {
        localStorage.setItem(`tree_state_${routeKey}`, JSON.stringify({
          expandedIds,
          activeNodeId
        }));
      } catch (error) {
        console.error('Error saving tree state:', error);
      }
    }
  }, [expandedIds, activeNodeId, preserveState, routeKey]);

  // Calculate expanded node IDs based on current filters
  useEffect(() => {
    const { orgLevel4, orgLevel5, query } = filters[0];
    const currentFiltersString = JSON.stringify(filters[0]);
    
    // Skip automatic expansion for application (leaf) nodes
    const isApplicationQuery = query && !query.startsWith('level4-') && !query.startsWith('level5-');
    
    // Only update expandedIds if this is the initial load or filters changed (and not clicking an application)
    if ((!expandedIdsInitialized.current || previousFilters.current !== currentFiltersString) && !isApplicationQuery) {
      const ids: string[] = [];
      
      if (orgLevel4) {
        ids.push(`level4-${orgLevel4}`);
      }

      if (orgLevel5 && orgLevel4) {
        ids.push(`level5-${orgLevel5}-${orgLevel4}`);
      }

      if (query && !isApplicationQuery) {
        // Find and expand parent nodes of the matching application
        for (const l4 of treeData) {
          let found = false;
          if (l4.children) {
            for (const l5 of l4.children) {
              if (l5.children) {
                const app = l5.children.find(app => 
                  app.id === query || 
                  app.name.toLowerCase().includes(query.toLowerCase())
                );
                if (app) {
                  ids.push(l4.id, l5.id);
                  found = true;
                  break;
                }
              }
            }
          }
          if (found) break;
        }
      }

      console.log('Setting expanded IDs from filters:', ids, '(isApplicationQuery:', isApplicationQuery, ')');
      setExpandedIds(ids);
      expandedIdsInitialized.current = true;
      previousFilters.current = currentFiltersString;
    }
  }, [filters, treeData]); // Removed expandedIds from dependency array

  // Calculate active node ID based on filters
  useEffect(() => {
    const { orgLevel4, orgLevel5, query } = filters[0];
    let newActiveNodeId: string | undefined = undefined;

    if (query) {
      // Try to find exact application match
      for (const l4 of treeData) {
        if (l4.children) {
          for (const l5 of l4.children) {
            if (l5.children) {
              const app = l5.children.find(app => 
                app.id === query || 
                app.name.toLowerCase() === query.toLowerCase()
              );
              if (app) {
                newActiveNodeId = app.id;
                break;
              }
            }
          }
        }
      }
    }

    if (orgLevel5 && orgLevel4) {
      newActiveNodeId = `level5-${orgLevel5}-${orgLevel4}`;
    }

    if (orgLevel4 && !newActiveNodeId) {
      newActiveNodeId = `level4-${orgLevel4}`;
    }

    // Only update if the active node ID actually changed
    if (newActiveNodeId !== activeNodeId) {
      setActiveNodeId(newActiveNodeId);
    }
  }, [filters, treeData, activeNodeId]);

  // Update filters while preserving route-specific state
  const setFilters = (newFilters: ApplicationTreeFilters) => {
    const currentFilters = filters[0];
    
    // Only reset expandedIdsInitialized if the filters are changing in a way
    // that should affect the tree structure (changing level4 or level5)
    const shouldResetExpansion = 
      currentFilters.orgLevel4 !== newFilters.orgLevel4 || 
      currentFilters.orgLevel5 !== newFilters.orgLevel5;
    
    // We'll still reset if switching from one application to another
    const isChangingApplication = 
      (currentFilters.query !== newFilters.query) && 
      (currentFilters.query || newFilters.query);
      
    if (shouldResetExpansion) {
      console.log('Resetting expansion state due to filter change');
      expandedIdsInitialized.current = false;
    } else {
      console.log('Preserving expansion state despite filter change');
    }

    const params = new URLSearchParams(searchParams);
    
    // Update or remove level4
    if (newFilters.orgLevel4) {
      params.set('level4', newFilters.orgLevel4);
    } else {
      params.delete('level4');
    }

    // Update or remove level5
    if (newFilters.orgLevel5) {
      params.set('level5', newFilters.orgLevel5);
    } else {
      params.delete('level5');
    }

    // Update or remove query
    if (newFilters.query) {
      params.set('query', newFilters.query);
    } else {
      params.delete('query');
    }

    // Preserve other route-specific params if needed
    if (preserveState) {
      const routeParams = new Set(['level4', 'level5', 'query']);
      for (const [key, value] of searchParams.entries()) {
        if (!routeParams.has(key)) {
          params.set(key, value);
        }
      }
    }

    setSearchParams(params);
  };

  return {
    treeData,
    expandedIds,
    setExpandedIds,
    activeNodeId,
    setActiveNodeId,
    filters: filters[0],
    setFilters
  };
} 