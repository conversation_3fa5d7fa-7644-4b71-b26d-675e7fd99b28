import { db } from '~/db/db.server';
import { applications, interfaces } from '~/db/schema';
import { eq, and, or, desc, like, ilike, count } from 'drizzle-orm';
import type { Application } from '~/db/types/applications';
import { HEETService } from '~/services/heet.server';
import { Logger, getConfig } from '~/utils/sync-utils.server';
import { InterfaceModel } from './interface/interface-model.server';

interface FindAllOptions {
  orgLevel4?: string;
  orgLevel5?: string;
  pladaServiceIds?: string[];
  query?: string;
  batchSize?: number;
  retryAttempts?: number;
  applicationId?: string;
  date?: string;
}

/**
 * Interface status object that includes red, amber, green counts
 */
export interface InterfaceRagStatusCounts {
  red: number;
  amber: number;
  green: number;
}

/**
 * Application with interface RAG status information
 */
export type ApplicationWithRagStatus = Application & {
  interfaceRagStatus: InterfaceRagStatusCounts;
};

export class ApplicationModel {
  /**
   * Retrieves all applications from the database with optional filters
   */
  static async findAll(options: FindAllOptions = {}): Promise<Application[]> {
    const { orgLevel4, orgLevel5, pladaServiceIds, query } = options;
    const conditions = [];

    if (orgLevel4 && orgLevel4 !== "all") {
      conditions.push(eq(applications.orgLevel4, orgLevel4));
      if (orgLevel5 && orgLevel5 !== "all") {
        conditions.push(eq(applications.orgLevel5, orgLevel5));
      }
    }

    if (pladaServiceIds?.length) {
      conditions.push(
        or(...pladaServiceIds.map(id => 
          eq(applications.pladaServiceId, id)
        ))
      );
    }

    if (query) {
      conditions.push(
        or(
          like(applications.applicationInstanceId, `%${query}%`),
          like(applications.name, `%${query}%`),
          like(applications.description || '', `%${query}%`)
        )
      );
    }

    return conditions.length > 0
      ? db.select().from(applications).where(and(...conditions)).orderBy(desc(applications.updatedAt))
      : db.select().from(applications).orderBy(desc(applications.updatedAt));
  }

  /**
   * Syncs applications from HEET API with optimized performance for handling thousands of applications
   * 
   * Optimizations:
   * 1. Batch processing with configurable batch size
   * 2. Single database transaction per batch to reduce overhead
   * 3. Detailed logging and error handling for monitoring
   */
  static async syncFromHeet(options: FindAllOptions = {}): Promise<void> {
    // Create logger
    const logger = new Logger('[Application Model]');
    // Get configuration with defaults
    const config = getConfig(options);
    const batchSize = config.batchSize ?? 100;
    
    try {
      logger.log('Sync applications from HEET: start');
      // Fetch all applications from HEET
      logger.log('Fetching applications from HEET...');
      const heetResponse = await HEETService.fetchApplications(options);
      logger.log(`Fetched ${heetResponse.length} applications from HEET`);
      // If no applications found, exit early
      if (!heetResponse.length) {
        logger.log('No applications to sync');
        return;
      }
      // Transform all applications
      logger.log('Transforming applications...');
      const transformedApps = heetResponse.map(app => HEETService.transformApplication(app));
      logger.log('Applications transformed');
      // Process applications in batches
      logger.log(`Processing in batches of up to ${batchSize} applications`);
      for (let i = 0; i < transformedApps.length; i += batchSize) {
        const batch = transformedApps.slice(i, i + batchSize);
        await db.transaction(async (tx) => {
          for (const record of batch) {
            await tx.insert(applications)
              .values(record)
              .onConflictDoUpdate({
                target: [applications.applicationInstanceId],
                set: record
              });
          }
        });
        logger.progress(Math.min(i + batch.length, transformedApps.length), transformedApps.length);
      }
      logger.log(`Successfully synchronized ${transformedApps.length} applications`);
    } catch (error) {
      logger.error('Error syncing from HEET:', error);
      throw error;
    }
  }

  /**
   * Find a single application by ID
   */
  static async findById(applicationId: string): Promise<Application | null> {
    const results = await db
      .select()
      .from(applications)
      .where(eq(applications.applicationInstanceId, applicationId))
      .limit(1);

    return results[0] ?? null;
  }

  /**
   * Counts applications based on the provided filters
   */
  static async countAll(options: FindAllOptions = {}): Promise<number> {
    const { orgLevel4, orgLevel5, pladaServiceIds, query } = options;
    const conditions = [];

    if (orgLevel4 && orgLevel4 !== "all") {
      conditions.push(eq(applications.orgLevel4, orgLevel4));
      if (orgLevel5 && orgLevel5 !== "all") {
        conditions.push(eq(applications.orgLevel5, orgLevel5));
      }
    }

    if (pladaServiceIds?.length) {
      conditions.push(
        or(...pladaServiceIds.map(id => 
          eq(applications.pladaServiceId, id)
        ))
      );
    }

    if (query) {
      conditions.push(
        or(
          like(applications.applicationInstanceId, `%${query}%`),
          like(applications.name, `%${query}%`),
          like(applications.description || '', `%${query}%`)
        )
      );
    }

    const result = conditions.length > 0
      ? await db.select({ count: count() }).from(applications).where(and(...conditions))
      : await db.select({ count: count() }).from(applications);
    
    return result[0]?.count || 0;
  }

  
/**
 * Get all interface IDs associated with an application
 * 
 * @param applicationId - The application instance ID to find interfaces for
 * @returns Array of interface IDs
 */
static async getInterfaceIds(applicationId: string): Promise<string[]> {
    // Query interfaces where the application is either sender or receiver
    const interfaceRecords = await db
      .select({ omsInterfaceId: interfaces.omsInterfaceId })
      .from(interfaces)
      .where(
        or(
          eq(interfaces.sendAppId, applicationId),
          eq(interfaces.receivedAppId, applicationId)
        )
      );
    
    // Extract and return just the IDs
    return interfaceRecords.map(iface => iface.omsInterfaceId);
  }
  
  /**
   * Get RAG status for all interfaces of an application
   * 
   * @param applicationId - The application instance ID to find interfaces for
   * @returns Object containing counts of interfaces by RAG status
   */
  static async getInterfaceRagStatus(applicationId: string): Promise<{ red: number, amber: number, green: number }> {
    // Get all interface IDs for this application
    const interfaceIds = await this.getInterfaceIds(applicationId);
    // If no interfaces, return zero counts
    if (interfaceIds.length === 0) {
      return { red: 0, amber: 0, green: 0 };
    }
    // Get RAG status for all interfaces
    const statusResults = await InterfaceModel.calculateBulkRagStatus(interfaceIds);
    // Initialize counters
    let redCount = 0;
    let amberCount = 0;
    let greenCount = 0;
    // Count interfaces by their status
    for (const id of interfaceIds) {
      const status = statusResults[id];
      if (status) {
        // If red > 0, status is "red"
        if (status.counts.red > 0) {
          redCount++;
        }
        // Else if amber > 0, status is "amber"
        else if (status.counts.amber > 0) {
          amberCount++;
        }
        // Otherwise, status is "green"
        else {
          greenCount++;
        }
      }
    }
    return { red: redCount, amber: amberCount, green: greenCount };
  }
  
  /**
   * Get RAG status for multiple applications in batch
   * 
   * @param applicationIds - Array of application instance IDs
   * @returns Object mapping application IDs to their RAG status
   */
  static async getBatchInterfaceRagStatus(applicationIds: string[]): Promise<Record<string, { red: number, amber: number, green: number }>> {
    // Get all interface IDs for all applications in one query
    const allInterfaceIds = await Promise.all(
      applicationIds.map(appId => this.getInterfaceIds(appId))
    );
    // Flatten and get unique interface IDs
    const uniqueInterfaceIds = [...new Set(allInterfaceIds.flat())];
    // If no interfaces found, return empty results
    if (uniqueInterfaceIds.length === 0) {
      return applicationIds.reduce((acc, appId) => ({
        ...acc,
        [appId]: { red: 0, amber: 0, green: 0 }
      }), {});
    }
    // Get RAG status for all interfaces in one batch
    const statusResults = await InterfaceModel.calculateBulkRagStatus(uniqueInterfaceIds);
    // Calculate results for each application
    const results: Record<string, { red: number, amber: number, green: number }> = {};
    applicationIds.forEach((appId, index) => {
      const appInterfaceIds = allInterfaceIds[index];
      let redCount = 0;
      let amberCount = 0;
      let greenCount = 0;
      appInterfaceIds.forEach(interfaceId => {
        const status = statusResults[interfaceId];
        if (status) {
          if (status.counts.red > 0) redCount++;
          else if (status.counts.amber > 0) amberCount++;
          else greenCount++;
        }
      });
      results[appId] = { red: redCount, amber: amberCount, green: greenCount };
    });
    return results;
  }
  
  /**
   * Enhances application data with interface RAG status counts
   * 
   * @param applications - Array of application objects to enhance
   * @returns Enhanced application array with interface RAG status
   */
  static async enhanceWithInterfaceRagStatus(applications: Application[]): Promise<ApplicationWithRagStatus[]> {
    if (applications.length === 0) {
      return [];
    }
    // Get all application IDs
    const applicationIds = applications.map(app => app.applicationInstanceId);
    // Get RAG status for all applications in batch
    const ragStatuses = await this.getBatchInterfaceRagStatus(applicationIds);
    // Enhance each application with its RAG status
    return applications.map(app => ({
      ...app,
      interfaceRagStatus: ragStatuses[app.applicationInstanceId] || {
        red: 0,
        amber: 0,
        green: 0
      }
    }));
  }
}