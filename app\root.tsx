import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ScrollRestoration,
  useLocation,
} from "@remix-run/react";
import type { LinksFunction, LoaderFunctionArgs } from "@remix-run/node";
import { cssBundleHref } from "@remix-run/css-bundle";
import { Sidebar, SidebarProvider, useSidebar } from "~/components/layout/sidebar"
import { json } from "@remix-run/node";
import { HEETService } from "./services/heet.server";
import type { HeetOrganizationApiResponse } from "~/db/types/organization";
import { Toaster } from "sonner";
import { PageTitleProvider, usePageTitle } from "~/contexts/page-title-context";
import { useEffect, useState } from "react";
import * as React from "react";

import styles from "./tailwind.css?url"
import { Header } from "~/components/layout/header"

export const links: LinksFunction = () => [
  { rel: "stylesheet", href: styles },
  ...(cssBundleHref ? [{ rel: "stylesheet", href: cssBundleHref }] : []),
  { rel: "icon", type: "image/svg+xml", href: "/oms/favicon.svg" },
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous",
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap",
  },
];

export type RootLoaderData = {
  organizations: HeetOrganizationApiResponse[];
};

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const organizations = await HEETService.fetchOrganizations();

    return json<RootLoaderData>({
      organizations,
    });
  } catch (error) {
    console.error("Error loading organizations:", error);
    return json<RootLoaderData>({
      organizations: [],
    });
  }
}

function Document({ children }: { children: React.ReactNode }) {
  const { beginTransition, endTransition } = usePageTitle();
  const location = useLocation();
  
  // Monitor location changes to handle route transitions
  useEffect(() => {
    beginTransition();
    
    // Use a brief delay to ensure the route has changed completely
    const timeoutId = setTimeout(() => {
      endTransition();
    }, 50);
    
    return () => clearTimeout(timeoutId);
  }, [location.pathname, beginTransition, endTransition]);
  
  return (
    <html lang="en" className="h-full" suppressHydrationWarning>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body className="h-full" suppressHydrationWarning>
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

// Client-only component wrapper
function ClientOnly({ children }: { children: React.ReactNode }) {
  let [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  return mounted ? children : null;
}

function AppLayout() {
  const { collapsed } = useSidebar();
  
  return (
    <div className="flex flex-col h-screen">
      <Header />
      <div className="flex-1 flex overflow-hidden">
        <Sidebar className="border-r" />
        <main 
          data-collapsed={collapsed} 
          className="flex-1 lg:pl-[300px] transition-all duration-300"
          style={{
            paddingLeft: collapsed ? "calc(80px + 1.5rem)" : "calc(300px + 1.5rem)",
          }}
        >
          <div className="p-6 h-full overflow-auto">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
}

export default function App() {
  return (
    <Document>
      <SidebarProvider>
        <PageTitleProvider>
          <AppLayout />
          <ClientOnly>
            <Toaster 
              position="top-right"
              richColors
              closeButton
              expand
              visibleToasts={3}
              toastOptions={{
                duration: 5000,
                className: "border border-slate-200 shadow-lg",
                classNames: {
                  error: "bg-red-50 text-red-800 border-red-200",
                }
              }}
            />
          </ClientOnly>
        </PageTitleProvider>
      </SidebarProvider>
    </Document>
  );
}
