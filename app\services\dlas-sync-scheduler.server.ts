import { getDLASSyncService } from './dlas-sync.server';
import { getNextScheduledRunTime } from './dlas-sync-config.server';
import { Logger } from '~/utils/sync-utils.server';
import { env } from '~/env.server';

// Create a logger for initialization
const initLogger = new Logger('DLAS Scheduler Init');

/**
 * Manages scheduled sync operations for DLAS data
 */
export class DLASSyncScheduler {
  private logger: Logger;
  private nextScheduledRun: NodeJS.Timeout | null = null;
  private initialized = false;
  
  constructor() {
    this.logger = new Logger('DLAS Scheduler');
  }
  
  /**
   * Set up daily sync at specified hour and minute
   */
  public setupDailySync(hour: number, minute: number): void {
    this.cancelScheduledSync(); // Clear any existing scheduled sync
    
    const getMillisecondsUntilNextRun = () => {
      const now = new Date();
      const next = new Date();
      next.setHours(hour, minute, 0, 0);
      
      // If the scheduled time has already passed today, schedule for tomorrow
      if (now.getTime() > next.getTime()) {
        next.setDate(next.getDate() + 1);
      }
      
      return next.getTime() - now.getTime();
    };
    
    const scheduleNextRun = () => {
      const msUntilNextRun = getMillisecondsUntilNextRun();
      const nextRunDate = new Date(Date.now() + msUntilNextRun);
      
      this.logger.log(`Scheduling next sync for ${nextRunDate.toLocaleString()}`);
      
      this.nextScheduledRun = setTimeout(async () => {
        this.logger.log('Running scheduled sync');
        
        // Get the sync service and start the sync
        const syncService = getDLASSyncService();
        
        try {
          await syncService.startSync();
        } catch (error) {
          this.logger.error('Error during scheduled sync', error);
        } finally {
          // Schedule the next run
          scheduleNextRun();
        }
      }, msUntilNextRun);
    };
    
    // Start the scheduling
    scheduleNextRun();
    
    this.logger.log(`Daily sync scheduled for ${hour}:${minute.toString().padStart(2, '0')}`);
    this.initialized = true;
  }
  
  /**
   * Cancel any scheduled sync
   */
  public cancelScheduledSync(): void {
    if (this.nextScheduledRun) {
      clearTimeout(this.nextScheduledRun);
      this.nextScheduledRun = null;
      this.logger.log('Scheduled sync canceled');
    }
  }
  
  /**
   * Get the next scheduled run time, or null if no sync is scheduled
   */
  public getNextScheduledRunTime(): Date | null {
    if (!this.nextScheduledRun) return null;
    return getNextScheduledRunTime();
  }
  
  /**
   * Check if the scheduler has been initialized
   */
  public isInitialized(): boolean {
    return this.initialized;
  }
}

export const dlasSyncScheduler = new DLASSyncScheduler();

/**
 * Set up daily sync at specified hour and minute
 */
export function setupDailySync(hour: number, minute: number): void {
  dlasSyncScheduler.setupDailySync(hour, minute);
}

/**
 * Initialize the DLAS sync scheduler from environment variables
 * This function should be called during application startup
 */
export function initializeDLASSyncScheduler(): void {
  try {
    // Get scheduler settings from environment
    const scheduledSyncHour = env.SCHEDULED_SYNC_HOUR || 6; // Default to 6 AM
    const scheduledSyncMinute = env.SCHEDULED_SYNC_MINUTE || 0; // Default to 0 minutes
    
    // Setup the scheduler
    setupDailySync(scheduledSyncHour, scheduledSyncMinute);
    
    initLogger.log(`DLAS Sync scheduled to run daily at ${scheduledSyncHour}:${scheduledSyncMinute.toString().padStart(2, '0')}`);
  } catch (error) {
    initLogger.error('Failed to initialize DLAS Sync scheduler:', error);
  }
}

// Automatically initialize if we're in a Node.js environment (not browser)
if (typeof window === 'undefined') {
  // Check if we're not in test mode
  const isTestEnv = process.env.NODE_ENV === 'test';
  
  if (!isTestEnv) {
    initLogger.log('Auto-initializing DLAS Sync scheduler');
    // Use setTimeout to avoid blocking the module loading
    setTimeout(() => {
      try {
        initializeDLASSyncScheduler();
      } catch (error) {
        initLogger.error('Error auto-initializing DLAS Sync scheduler:', error);
      }
    }, 0);
  } else {
    initLogger.log('Skipping auto-initialization in test environment');
  }
} 