import { useMemo } from "react"
import type { ApplicationWithRagStatus } from "~/models/application.server"
import type { TreeNodeData, RagStatus } from "./tree-view"
import type { HeetOrganizationApiResponse } from "~/db/types/organization"

/**
 * Hook to transform applications data into a hierarchical tree structure using HEET org data
 */
export function useApplicationTree(
  applications: ApplicationWithRagStatus[] = [],
  organizations: HeetOrganizationApiResponse[] = []
) {
  return useMemo(() => {
    const tree: TreeNodeData[] = []
    // Maps to track Level 4 and Level 5 nodes
    const level4Map = new Map<string, TreeNodeData>()
    const level5Map = new Map<string, TreeNodeData>()
    
    // First pass: Process HEET organization data to create the hierarchical structure
    organizations.forEach(org => {
      // Only process level 5 organizations as per the specification
      if (org.level !== 5) return
      
      const { name: level5Name, id: level5Id, level4: level4Name, level4Id } = org
      
      // Create Level 4 node if it doesn't exist
      if (!level4Map.has(level4Name)) {
        const level4Node: TreeNodeData = {
          id: `level4-${level4Name}`,
          name: level4Name,
          type: 'level4',
          children: [],
          status: {
            red: 0,
            amber: 0,
            green: 0,
            none: 0,
            total: 0
          },
          count: 0,
        }
        level4Map.set(level4Name, level4Node)
        tree.push(level4Node)
      }
      
      // Create Level 5 node if it doesn't exist
      if (!level5Map.has(level5Name)) {
        const level5Node: TreeNodeData = {
          id: `level5-${level5Name}-${level4Name}`,
          name: level5Name,
          type: 'level5',
          children: [],
          status: {
            red: 0,
            amber: 0,
            green: 0,
            none: 0,
            total: 0
          },
          count: 0,
        }
        
        // Add Level 5 node to its Level 4 parent
        const level4Node = level4Map.get(level4Name)
        if (level4Node) {
          level4Node.children!.push(level5Node)
        }
        
        level5Map.set(level5Name, level5Node)
      }
    })
    
    // Second pass: Add applications to their appropriate organizational nodes
    applications.forEach(app => {
      const { applicationInstanceId, name, orgLevel4, orgLevel5, interfaceRagStatus } = app
      
      // Skip if app is missing organizational info
      if (!orgLevel4) return
      
      // Create application node
      const appNode: TreeNodeData = {
        id: applicationInstanceId,
        name,
        type: 'application',
        status: {
          red: interfaceRagStatus?.red || 0,
          amber: interfaceRagStatus?.amber || 0,
          green: interfaceRagStatus?.green || 0,
          none: 0,
          total: calculateTotal(interfaceRagStatus)
        }
      }
      
      let parentFound = false
      
      // Try to add to Level 5 if available
      if (orgLevel5) {
        const level5Node = level5Map.get(orgLevel5)
        
        if (level5Node) {
          level5Node.children!.push(appNode)
          level5Node.count = (level5Node.count || 0) + 1
          updateNodeStatus(level5Node, appNode.status)
          
          // Also update the parent Level 4 counts
          if (orgLevel4) {
            const level4Node = level4Map.get(orgLevel4)
            if (level4Node) {
              level4Node.count = (level4Node.count || 0) + 1
              updateNodeStatus(level4Node, appNode.status)
            }
          }
          
          parentFound = true
        }
      }
      
      // If no Level 5 match or the app doesn't have Level 5, add directly to Level 4
      if (!parentFound && orgLevel4) {
        const level4Node = level4Map.get(orgLevel4)
        if (level4Node) {
          level4Node.children!.push(appNode)
          level4Node.count = (level4Node.count || 0) + 1
          updateNodeStatus(level4Node, appNode.status)
          parentFound = true
        }
      }
      
      // If no organizational match found, add to an "Unclassified" section
      if (!parentFound) {
        // Get or create the Unclassified level4 node
        let unclassifiedNode = level4Map.get("Unclassified")
        if (!unclassifiedNode) {
          unclassifiedNode = {
            id: "level4-Unclassified",
            name: "Unclassified",
            type: 'level4',
            children: [],
            status: {
              red: 0,
              amber: 0,
              green: 0,
              none: 0,
              total: 0
            },
            count: 0
          }
          level4Map.set("Unclassified", unclassifiedNode)
          tree.push(unclassifiedNode)
        }
        
        unclassifiedNode.children!.push(appNode)
        unclassifiedNode.count = (unclassifiedNode.count || 0) + 1
        updateNodeStatus(unclassifiedNode, appNode.status)
      }
    })
    
    // Sort all nodes alphabetically
    tree.sort((a, b) => a.name.localeCompare(b.name))
    tree.forEach(level4Node => {
      if (level4Node.children) {
        level4Node.children.sort((a, b) => a.name.localeCompare(b.name))
        level4Node.children.forEach(level5Node => {
          if (level5Node.children) {
            level5Node.children.sort((a, b) => a.name.localeCompare(b.name))
          }
        })
      }
    })
    
    return tree
  }, [applications, organizations])
}

/**
 * Helper function to update a node's status counts from a child node's status
 */
function updateNodeStatus(node: TreeNodeData, childStatus?: RagStatus) {
  if (!node.status || !childStatus) return
  
  node.status.red += childStatus.red || 0
  node.status.amber += childStatus.amber || 0
  node.status.green += childStatus.green || 0
  node.status.none += childStatus.none || 0
  node.status.total += childStatus.total || 0
}

/**
 * Calculate total from status counts
 */
function calculateTotal(status?: { red: number, amber: number, green: number }) {
  if (!status) return 0
  return status.red + status.amber + status.green
}

/**
 * Organizes applications into a tree structure by organization levels
 * Alternative implementation for reference - we use the useApplicationTree hook above
 */
export function buildApplicationTree(
  applications: ApplicationWithRagStatus[]
): TreeNodeData[] {
  // First, organize by level4
  const level4Map = new Map<string, {
    applications: ApplicationWithRagStatus[],
    level5Map: Map<string, ApplicationWithRagStatus[]>
  }>();

  // Process all applications
  applications.forEach(app => {
    const level4 = app.orgLevel4 || 'Unknown';
    const level5 = app.orgLevel5 || 'Unknown';
    
    // Create level4 entry if it doesn't exist
    if (!level4Map.has(level4)) {
      level4Map.set(level4, {
        applications: [],
        level5Map: new Map()
      });
    }
    
    const level4Entry = level4Map.get(level4)!;
    level4Entry.applications.push(app);
    
    // Add to level5 grouping
    if (!level4Entry.level5Map.has(level5)) {
      level4Entry.level5Map.set(level5, []);
    }
    
    level4Entry.level5Map.get(level5)!.push(app);
  });

  // Build the tree structure
  const treeData: TreeNodeData[] = [];
  
  // Process level4 entries
  for (const [level4Name, level4Data] of level4Map.entries()) {
    // Calculate level4 RAG status by combining all applications
    const level4Status = calculateCombinedStatus(level4Data.applications);
    
    const level4Node: TreeNodeData = {
      id: `level4-${level4Name}`,
      name: level4Name,
      type: 'level4',
      children: [],
      status: level4Status,
      count: level4Data.applications.length
    };
    
    // Process level5 entries
    for (const [level5Name, level5Apps] of level4Data.level5Map.entries()) {
      // Calculate level5 RAG status
      const level5Status = calculateCombinedStatus(level5Apps);
      
      const level5Node: TreeNodeData = {
        id: `level5-${level5Name}-${level4Name}`,
        name: level5Name,
        type: 'level5',
        children: [],
        status: level5Status,
        count: level5Apps.length
      };
      
      // Add applications to level5
      level5Apps.forEach(app => {
        level5Node.children!.push({
          id: app.applicationInstanceId,
          name: app.name,
          type: 'application',
          status: {
            red: app.interfaceRagStatus?.red || 0,
            amber: app.interfaceRagStatus?.amber || 0,
            green: app.interfaceRagStatus?.green || 0,
            none: 0,
            total: (app.interfaceRagStatus?.red || 0) + 
                   (app.interfaceRagStatus?.amber || 0) + 
                   (app.interfaceRagStatus?.green || 0)
          }
        });
      });
      
      // Sort applications by name
      level5Node.children!.sort((a, b) => a.name.localeCompare(b.name));
      
      // Add level5 to level4
      level4Node.children!.push(level5Node);
    }
    
    // Sort level5 by name
    level4Node.children!.sort((a, b) => a.name.localeCompare(b.name));
    
    // Add level4 to tree
    treeData.push(level4Node);
  }
  
  // Sort level4 by name
  treeData.sort((a, b) => a.name.localeCompare(b.name));
  
  return treeData;
}

/**
 * Calculates combined RAG status for a group of applications
 */
function calculateCombinedStatus(
  applications: ApplicationWithRagStatus[]
): RagStatus {
  let redCount = 0;
  let amberCount = 0;
  let greenCount = 0;
  
  applications.forEach(app => {
    redCount += app.interfaceRagStatus?.red || 0;
    amberCount += app.interfaceRagStatus?.amber || 0;
    greenCount += app.interfaceRagStatus?.green || 0;
  });
  
  const total = redCount + amberCount + greenCount;
  
  return {
    red: redCount,
    amber: amberCount,
    green: greenCount,
    none: 0,
    total: total
  };
} 