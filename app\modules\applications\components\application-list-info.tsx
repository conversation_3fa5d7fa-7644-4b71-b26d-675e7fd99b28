import type { Application } from "~/db/types/applications";

/**
 * Props for the application list info component
 */
interface ApplicationListInfoProps { 
  applications: Application[]; 
  hasSearchQuery: boolean;
}

/**
 * Information about applications being shown
 */
export function ApplicationListInfo({ 
  applications, 
  hasSearchQuery 
}: ApplicationListInfoProps) {
  return (
    <div>
      <p className="text-sm text-muted-foreground mb-2">
        {applications.length === 0 && hasSearchQuery
          ? "No applications found. Try adjusting your search."
          : `Showing ${applications.length} ${applications.length === 1 ? "application" : "applications"}`}
      </p>
    </div>
  );
} 