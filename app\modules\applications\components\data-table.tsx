"use client"

import { DataTable } from "~/components/ui/data-table/data-table"
import { columns } from "./columns"
import type { Application } from "~/db/types/applications"
import React from "react"
import { useNavigate } from "@remix-run/react"
import { ApplicationWithRagStatus } from "~/models/application.server"

/**
 * Props for the ApplicationsDataTable component
 */
interface ApplicationsDataTableProps {
  data: ApplicationWithRagStatus[]
  isLoading?: boolean
  orgLevel4?: string
  orgLevel5?: string
}

export function ApplicationsDataTable({ 
  data, 
  isLoading = false,
  orgLevel4 = "all", 
  orgLevel5 = "all",
}: ApplicationsDataTableProps) {
  const navigate = useNavigate()

  const filteredData = React.useMemo(() => {
    let filtered = [...data]
    
    if (orgLevel4 !== "all") {
      filtered = filtered.filter(item => item.orgLevel4 === orgLevel4)
      if (orgLevel5 !== "all") {
        filtered = filtered.filter(item => item.orgLevel5 === orgLevel5)
      }
    }
    
    return filtered
  }, [data, orgLevel4, orgLevel5])

  /**
   * <PERSON>les navigating to the interfaces view for an application
   */
  const handleViewInterfaces = (applicationId: string) => {
    // Create a new URLSearchParams object to build the query string
    const params = new URLSearchParams();
    
    // Add the application ID as the query parameter
    params.set("query", applicationId);
    
    // Preserve organization level selections if they exist
    if (orgLevel4 && orgLevel4 !== "all") {
      params.set("level4", orgLevel4);
    }
    
    if (orgLevel5 && orgLevel5 !== "all") {
      params.set("level5", orgLevel5);
    }
    
    // Navigate to the interfaces page with the constructed query string
    navigate(`/interfaces?${params.toString()}`);
  };

  return (
    <div className="space-y-4">
      <div className="relative overflow-x-auto">
        {isLoading && (
          <div className="absolute inset-0 bg-background/50 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}
        <DataTable
          columns={columns}
          data={filteredData}
          onViewInterfaces={handleViewInterfaces}
          facetedFilters={[
            {
              column: "status",
              title: "Status",
              options: [
                { label: "Active", value: "active" },
                { label: "Planned", value: "planned" },
                { label: "Demised", value: "demised" },
              ],
            },
            {
              column: "criticality",
              title: "Criticality",
              options: [
                { label: "Tier 1", value: "Tier 1" },
                { label: "Tier 2", value: "Tier 2" },
                { label: "Tier 3", value: "Tier 3" },
              ],
            },
            {
              column: "interfaceRagStatus",
              title: "Interface Status",
              options: [
                { label: "SLA Breached (Red)", value: "red" },
                { label: "SLA At Risk (Amber)", value: "amber" },
                { label: "On Schedule (Green)", value: "green" },
              ],
            },
          ]}
        />
      </div>
    </div>
  );
} 