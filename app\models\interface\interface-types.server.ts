/**
 * Core interface types and options
 */

import type { Interface } from "~/db/types/interfaces";

/**
 * Search options for finding interfaces
 */
export interface InterfaceSearchOptions {
    orgLevel4?: string;
    orgLevel5?: string;
    appId?: string;
    applicationIds?: string[];
    searchQuery?: string;
}

/**
 * Configuration for sync operations
 */
export interface SyncConfig {
    batchSize: number;
    chunkSize: number;
    enableParallel: boolean;
    date?: string; // Optional YYYY-MM-DD format for historical sync
}

/**
 * RAG (Red, Amber, Green) status types and results
 */
export type RAGStatus = "red" | "amber" | "green";

export interface RAGStatusResult {
    counts: {
        red: number;
        amber: number;
        green: number;
    };
}

/**
 * Extended interface types
 */
export type InterfaceWithDatasetNames = Interface & {
    datasetNames: string[];
};

export type InterfaceWithRagStatus = Interface & {
    ragStatus: RAGStatusResult;
}; 