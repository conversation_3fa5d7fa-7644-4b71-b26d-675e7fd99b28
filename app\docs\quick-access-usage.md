# Quick Access Feature

This document explains how to implement the "Quick Access" tracking in detail pages.

## Overview

The Quick Access feature allows users to quickly access their favorite and recently viewed applications, interfaces, and datasets from the dashboard. The feature consists of:

1. A utility for storing quick access items in localStorage
2. A hook for tracking when items are viewed
3. A component for displaying the quick access items
4. Pin/unpin functionality to keep favorite items in the list

## How to Use in Detail Pages

To track when an item is viewed, import and use the `useTrackView` hook in the detail page component:

### Application Detail Page Example

```tsx
import { useTrackView } from '~/hooks/use-track-view';

export default function ApplicationDetail() {
  const { application } = useLoaderData<typeof loader>();
  
  // Track view
  useTrackView({
    id: application.applicationInstanceId,
    name: application.name,
    type: 'Application',
    path: `/applications/${application.applicationInstanceId}`
  });
  
  // Rest of the component...
}
```

### Interface Detail Page Example

```tsx
import { useTrackView } from '~/hooks/use-track-view';

export default function InterfaceDetail() {
  const { interface: interfaceData } = useLoaderData<typeof loader>();
  
  // Track view
  useTrackView({
    id: interfaceData.omsInterfaceId,
    name: interfaceData.name,
    type: 'Interface',
    path: `/interfaces/${interfaceData.omsInterfaceId}`
  });
  
  // Rest of the component...
}
```

### Dataset Detail Page Example

```tsx
import { useTrackView } from '~/hooks/use-track-view';

export default function DatasetDetail() {
  const { dataset } = useLoaderData<typeof loader>();
  
  // Track view
  useTrackView({
    id: dataset.omsDatasetId,
    name: dataset.name,
    type: 'Dataset',
    path: `/datasets/${dataset.omsDatasetId}`
  });
  
  // Rest of the component...
}
```

## For Event Handlers

For event handlers (like click handlers), you can't use hooks directly. Instead, import and use the `addQuickAccessItem` function:

```tsx
import { addQuickAccessItem } from '~/utils/quick-access';

const handleViewDataset = (datasetId: string, datasetName: string) => {
  // Track the dataset access
  addQuickAccessItem({
    id: datasetId,
    name: datasetName,
    type: 'Dataset',
    path: `/datasets/${datasetId}`
  });
  
  // Navigate or perform other actions
  navigate(`/datasets/${datasetId}`);
};
```

## Pin/Unpin Functionality

The Quick Access component automatically provides pin/unpin functionality. Users can:

1. Pin important items to keep them in the list permanently
2. Unpin items when they're no longer needed
3. See pinned items at the top of the list

## Configuration

The default configuration stores up to 5 unpinned items (pinned items are always kept). To change this limit, modify the `MAX_QUICK_ACCESS_ITEMS` constant in `app/utils/quick-access.ts`.

## Implementation Details

- Quick access items are stored in localStorage with the key `oms_quick_access`
- Items are sorted with pinned items first, then by most recently viewed
- If an item is viewed again, it moves to the top of the list (within its pinned/unpinned group)
- Each item stores its ID, name, type, view timestamp, path, and pinned status
- The Quick Access component will not display if there are no items 