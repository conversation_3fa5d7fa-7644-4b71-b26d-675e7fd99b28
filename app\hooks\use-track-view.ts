import { useEffect } from 'react';
// Remove the import for addQuickAccessItem since we're no longer using it
// import { addQuickAccessItem } from '~/utils/quick-access';

/**
 * Hook to track when an item is viewed
 * 
 * Note: This used to automatically add items to quick access,
 * but now items only appear in quick access when explicitly pinned
 * via the PinQuickAccessButton component.
 * 
 * @param item The item being viewed
 */
export function useTrackView(item: {
  id: string;
  name: string;
  type: 'Application' | 'Interface';
  path: string;
}) {
  useEffect(() => {
    // Only track on client-side
    if (typeof window === 'undefined') return;
    
    // We no longer automatically add to quick access
    // Items are only added when explicitly pinned
    // addQuickAccessItem(item);
    
    // This hook could be used for other tracking purposes in the future
    // such as analytics, most recently viewed, etc.
  }, [item.id, item.type]); // Re-run if the id or type changes
} 