"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "~/components/ui/badge"
import { Checkbox } from "~/components/ui/checkbox"
import { DataTableColumnHeader } from "~/components/ui/data-table/data-table-column-header"
import { But<PERSON> } from "~/components/ui/button"
import { AlertCircle, AlertTriangle, CheckCircle2, Clock, CalendarSearch, EyeOff, Link } from "lucide-react"
import type { DatasetWithSLA } from "~/db/types/datasets"
import type { RAGStatus } from "~/routes/interfaces.$interfaceId.datasets"
import { Switch } from "~/components/ui/switch"
import { generateSLAText, parseRRuleString } from '~/utils/sla-utils'

// Function to format dates in UTC
function formatUTC(timestamp: string | Date): string {
  const date = new Date(timestamp);
  // Use the date-fns format with UTC time values
  return `${date.getUTCFullYear()}-${String(date.getUTCMonth() + 1).padStart(2, '0')}-${String(date.getUTCDate()).padStart(2, '0')} ${String(date.getUTCHours()).padStart(2, '0')}:${String(date.getUTCMinutes()).padStart(2, '0')}:${String(date.getUTCSeconds()).padStart(2, '0')}`;
}

export function getColumns(
  options?: {
    onViewEvents?: (datasetName: string) => void;
    onUpdateSLA?: (datasetName: string) => void;
    onToggleRagInclusion?: (datasetName: string, include: boolean) => void;
  }
): ColumnDef<DatasetWithSLA & { ragStatus: RAGStatus }>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-[2px]"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-[2px]"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "datasetName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Dataset Name" />
      ),
      cell: ({ row }) => {
        return (
          <div className="max-w-[500px] break-all font-medium">
            {row.getValue("datasetName") as string}
          </div>
        )
      },
    },
    {
      accessorKey: "legalEntityCode",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Legal Entity" />
      ),
      cell: ({ row }) => {
        return (
          <div className="font-medium">
            {row.getValue("legalEntityCode") as string || "N/A"}
          </div>
        )
      },
    },
    {
      accessorKey: "productType",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Product Type" />
      ),
      cell: ({ row }) => {
        const productType = row.getValue("productType") as string | string[]
        return (
          <div className="truncate">
            {Array.isArray(productType) ? productType.join(", ") : productType}
          </div>
        )
      },
      filterFn: (row, id, value) => {
        const productType = row.getValue(id) as string | string[]
        const types = Array.isArray(productType) ? productType : [productType]
        return value.some((v: string) => types.includes(v))
      },
    },
    {
      accessorKey: "transferType",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Transfer Type" />
      ),
      cell: ({ row }) => {
        return (
          <Badge variant="secondary">
            {row.getValue("transferType") as string}
          </Badge>
        )
      },
    },
    {
      accessorKey: "frequency",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Frequency" />
      ),
      cell: ({ row }) => {
        return (
          <Badge variant="outline">
            {row.getValue("frequency") as string}
          </Badge>
        )
      },
    },
    {
      accessorKey: "sla",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="SLA" />
      ),
      cell: ({ row }) => {
        const slaStr = row.getValue("sla") as string | null;
        const slaConfig = parseRRuleString(slaStr);
        return (
          <div className="font-medium">
            {slaConfig ? generateSLAText(slaConfig) : "Not Set"}
          </div>
        );
      },
    },
    {
      accessorKey: "expectedArrivalTime",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Expected Arrival" />
      ),
      cell: ({ row }) => {
        const timestamp = row.getValue("expectedArrivalTime") as string | null
        return (
          <div className="font-medium">
            {timestamp ? formatUTC(timestamp) : "Not Set"}
          </div>
        )
      },
    },
    {
      accessorKey: "lastArrivalTime",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Last Arrival" />
      ),
      cell: ({ row }) => {
        const timestamp = row.getValue("lastArrivalTime") as string | null
        return (
          <div className="font-medium">
            {timestamp ? formatUTC(timestamp) : "Never"}
          </div>
        )
      },
    },
    {
      id: "ragStatus",
      accessorKey: "ragStatus",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const status = row.original.ragStatus;
        
        return (
          <div className="flex items-center justify-center">
            {status === "red" && (
              <AlertCircle className="h-5 w-5 text-destructive" />
            )}
            {status === "amber" && (
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
            )}
            {status === "green" && (
              <CheckCircle2 className="h-5 w-5 text-green-500" />
            )}
          </div>
        )
      },
    },
    {
      accessorKey: "includeInRagStatus",
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title="RAG Status Inclusion"
        />
      ),
      cell: ({ row }) => {
        const dataset = row.original;
        const isIncluded = dataset.includeInRagStatus;
        
        return (
          <div className="flex justify-center">
            <Switch 
              checked={isIncluded} 
              onCheckedChange={(checked: boolean) => {
                if (options?.onToggleRagInclusion) {
                  options.onToggleRagInclusion(dataset.datasetName, checked);
                }
              }}
              aria-label={`Include ${dataset.datasetName} in RAG status calculation`}
            />
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "eventCount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Events" />
      ),
      enableSorting: true,
      cell: ({ row }) => {
        const dataset = row.original;
        const count = dataset.eventCount || 0;

        return (
          <div className="text-center">
            {count > 0 ? (
              <button
                onClick={() => options?.onViewEvents?.(dataset.datasetName)}
                className="flex items-center justify-center gap-1 mx-auto px-2 py-1 text-primary rounded-md hover:bg-primary/10 transition-colors font-medium"
                title="View Events"
              >
                <span>{count}</span>
                <Link className="h-3.5 w-3.5" />
              </button>
            ) : (
              <span className="text-muted-foreground">0</span>
            )}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row }) => {
        const dataset = row.original
        return (
          <div className="flex justify-end gap-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => options?.onUpdateSLA?.(dataset.datasetName)}
              title="Update SLA"
            >
              <Clock className="h-4 w-4" />
            </Button>
          </div>
        )
      },
    },
  ]
} 