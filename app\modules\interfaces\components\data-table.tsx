"use client"

import { DataTable } from "~/components/ui/data-table/data-table"
import { getColumns } from "./columns"
import type { Interface } from "~/db/types/interfaces"
import type { InterfaceWithRagStatus } from "~/models/interface/interface-rag-status.server"
import React from "react"
import { useRevalidator, useLocation } from "@remix-run/react"
import { addQuickAccessItem } from "~/utils/quick-access"
import { ColumnDef } from "@tanstack/react-table"

interface InterfacesDataTableProps {
  data: InterfaceWithRagStatus[]
  isLoading?: boolean
  orgLevel4?: string
  orgLevel5?: string
  onViewDatasets?: (interfaceId: string, interfaceName: string) => void
}

export function InterfacesDataTable({ 
  data, 
  isLoading = false,
  orgLevel4 = "all", 
  orgLevel5 = "all",
  onViewDatasets,
}: InterfacesDataTableProps) {
  const revalidator = useRevalidator()
  const location = useLocation()

  const filteredData = React.useMemo(() => {
    let filtered = [...data]
    // TODO: Add org level filtering once we add these fields to interfaces
    return filtered
  }, [data, orgLevel4, orgLevel5])

  // Apply the columns configuration
  const columns = React.useMemo(
    () => getColumns({ 
      onViewDatasets
    }) as ColumnDef<InterfaceWithRagStatus, unknown>[],
    [onViewDatasets]
  );

  return (
    <div className="space-y-4">
      <div className="relative overflow-x-auto">
        {isLoading && (
          <div className="absolute inset-0 bg-background/50 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}
        <DataTable
          columns={columns}
          data={filteredData}
          filterColumn="interfaceName"
          defaultSort={[{ id: "ragStatus", desc: false }]}
          facetedFilters={[
            {
              column: "ragStatus",
              title: "Status",
              options: [
                { label: "SLA Breached (Red)", value: "red" },
                { label: "SLA At Risk (Amber)", value: "amber" },
                { label: "On Schedule (Green)", value: "green" },
              ],
            },
            {
              column: "direction",
              title: "Direction",
              options: [
                { label: "Inbound", value: "IN" },
                { label: "Outbound", value: "OUT" },
              ],
            },
            {
              column: "transferType",
              title: "Transfer Type",
              options: [
                { label: "File", value: "FILE" },
                { label: "API", value: "API" },
                { label: "Message", value: "Message" },
              ],
            },
            {
              column: "frequency",
              title: "Frequency",
              options: [
                { label: "Daily", value: "Daily" },
                { label: "Weekly", value: "Weekly" },
                { label: "Monthly", value: "Monthly" },
                { label: "Real Time", value: "Real time" },
              ],
            },
          ]}
        />
      </div>
    </div>
  )
} 