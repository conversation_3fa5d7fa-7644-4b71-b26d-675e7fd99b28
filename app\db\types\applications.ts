import type { applications } from '../schema/applications';
import { z } from "zod";

// Database Types
export type Application = typeof applications.$inferSelect;
export type NewApplication = typeof applications.$inferInsert;

// API Types
export interface HeetApplicationApiResponse {
  applicationInstanceId: string;
  name: string;
  shortName: string;
  description: string;
  criticality: string;
  status: string;
  strategicStatus: string;
  itOwningOrganisation: {
    level2: string;
    level3: string;
    level4: string;
    level5: string;
  };
  pladaServiceId: string;
  pladaServiceName: string;
  pladaOwners: Array<{
    psid: string;
    displayName: string;
    emailAddress: string;
  }>;
  pladaDelegates: Array<{
    psid: string;
    displayName: string;
    emailAddress: string;
  }>;
}

// Zod Schemas for API Response Validation
export const HeetApplicationOwnerSchema = z.object({
  psid: z.string(),
  displayName: z.string(),
  emailAddress: z.string().email(),
});

export const HeetApplicationApiResponseSchema = z.object({
  applicationInstanceId: z.string(),
  name: z.string(),
  shortName: z.string(),
  description: z.string(),
  criticality: z.string(),
  status: z.string(),
  strategicStatus: z.string(),
  itOwningOrganisation: z.object({
    level2: z.string(),
    level3: z.string(),
    level4: z.string(),
    level5: z.string(),
  }),
  pladaServiceId: z.string(),
  pladaServiceName: z.string(),
  pladaOwners: z.array(HeetApplicationOwnerSchema),
  pladaDelegates: z.array(HeetApplicationOwnerSchema),
});

// Form Types
export interface ApplicationFormData {
  name: string;
  shortName: string;
  description?: string;
  criticality: string;
  status: string;
  strategicStatus: string;
  orgLevel2: string;
  orgLevel3: string;
  orgLevel4: string;
  orgLevel5: string;
  pladaServiceId: string;
  pladaServiceName: string;
}

// Form Validation Schema
export const ApplicationFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  shortName: z.string().min(1, "Short name is required"),
  description: z.string().optional(),
  criticality: z.string().min(1, "Criticality is required"),
  status: z.string().min(1, "Status is required"),
  strategicStatus: z.string().min(1, "Strategic status is required"),
  orgLevel2: z.string().min(1, "Level 2 organization is required"),
  orgLevel3: z.string().min(1, "Level 3 organization is required"),
  orgLevel4: z.string().min(1, "Level 4 organization is required"),
  orgLevel5: z.string().min(1, "Level 5 organization is required"),
  pladaServiceId: z.string().min(1, "PLADA service ID is required"),
  pladaServiceName: z.string().min(1, "PLADA service name is required"),
}); 