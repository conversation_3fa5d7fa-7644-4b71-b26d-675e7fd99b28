"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "~/components/ui/badge"
import { DataTableColumnHeader } from "~/components/ui/data-table/data-table-column-header"
import type { InterfaceEvent } from "~/db/types/events"
import { format } from "date-fns"
import { Button } from "~/components/ui/button"
import { FileJson, Copy, Check } from "lucide-react"
import { useState } from "react"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog"
import React from "react"

// Function to format dates in UTC
function formatUTC(timestamp: string | Date): string {
  try {
    const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return "Invalid date";
    }
    
    // Use the date-fns format with UTC time values
    return `${date.getUTCFullYear()}-${String(date.getUTCMonth() + 1).padStart(2, '0')}-${String(date.getUTCDate()).padStart(2, '0')} ${String(date.getUTCHours()).padStart(2, '0')}:${String(date.getUTCMinutes()).padStart(2, '0')}:${String(date.getUTCSeconds()).padStart(2, '0')}`;
  } catch (error) {
    console.error("Error formatting date:", error);
    return "Invalid date";
  }
}

// JSON Viewer component
const JsonViewer = ({ json }: { json: string }) => {
  const [isError, setIsError] = React.useState(false);
  const [formattedJson, setFormattedJson] = React.useState<string | null>(null);
  const [copied, setCopied] = React.useState(false);
  
  React.useEffect(() => {
    try {
      const parsedJson = JSON.parse(json || '{}');
      setFormattedJson(JSON.stringify(parsedJson, null, 2));
      setIsError(false);
    } catch (error) {
      setIsError(true);
      setFormattedJson(json || '');
    }
  }, [json]);
  
  const handleCopy = React.useCallback(() => {
    if (formattedJson) {
      navigator.clipboard.writeText(formattedJson);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  }, [formattedJson]);
  
  if (isError) {
    return (
      <div className="bg-destructive/10 p-4 rounded-md">
        <p className="text-destructive font-medium">Invalid JSON</p>
        <p className="text-muted-foreground text-sm mt-2">{formattedJson}</p>
      </div>
    );
  }
  
  return (
    <div className="relative">
      <Button 
        variant="ghost" 
        size="icon" 
        className="absolute right-2 top-2 h-8 w-8 opacity-80 hover:opacity-100"
        onClick={handleCopy}
        title="Copy JSON"
      >
        {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
        <span className="sr-only">Copy JSON</span>
      </Button>
      <pre className="bg-muted p-4 rounded-md overflow-auto max-h-[60vh] text-sm">
        <code className="text-foreground whitespace-pre-wrap">
          {formattedJson}
        </code>
      </pre>
    </div>
  );
};

export function getColumns(): ColumnDef<InterfaceEvent>[] {
  return [
    {
      accessorKey: "msgId",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Message ID" />
      ),
      cell: ({ row }) => {
        return (
          <div className="max-w-[200px] truncate font-medium">
            {row.getValue("msgId") as string}
          </div>
        )
      },
    },
    {
      accessorKey: "businessDate",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Business Date" />
      ),
      cell: ({ row }) => {
        return (
          <div className="font-medium">
            {row.getValue("businessDate") as string}
          </div>
        )
      },
    },
    {
      accessorKey: "createdDateTime",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created Time" />
      ),
      cell: ({ row }) => {
        const date = row.getValue("createdDateTime") as Date
        return (
          <div className="font-medium">
            {formatUTC(date)}
          </div>
        )
      },
    },
    {
      accessorKey: "startNodeName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="From" />
      ),
      cell: ({ row }) => {
        return (
          <div className="font-medium">
            {row.getValue("startNodeName") as string}
          </div>
        )
      },
    },
    {
      accessorKey: "endNodeName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="To" />
      ),
      cell: ({ row }) => {
        return (
          <div className="font-medium">
            {row.getValue("endNodeName") as string}
          </div>
        )
      },
    },
    {
      accessorKey: "frequency",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Frequency" />
      ),
      cell: ({ row }) => {
        return (
          <Badge variant="outline">
            {row.getValue("frequency") as string}
          </Badge>
        )
      },
    },
    {
      accessorKey: "valid",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const valid = row.getValue("valid") as string
        return (
          <Badge variant={valid === "Y" ? "secondary" : "destructive"}>
            {valid === "Y" ? "Valid" : "Invalid"}
          </Badge>
        )
      },
    },
    {
      accessorKey: "reportedForName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Reported For" />
      ),
      cell: ({ row }) => {
        return (
          <div className="font-medium">
            {row.getValue("reportedForName") as string}
          </div>
        )
      },
    },
    {
      accessorKey: "reportedForId",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Reported For ID" />
      ),
      cell: ({ row }) => {
        return (
          <div className="font-medium">
            {row.getValue("reportedForId") as string}
          </div>
        )
      },
    },
    {
      accessorKey: "milestoneType",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Milestone Type" />
      ),
      cell: ({ row }) => {
        return (
          <Badge variant="outline">
            {row.getValue("milestoneType") as string}
          </Badge>
        )
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const event = row.original;
        return (
          <div className="flex justify-end">
            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  title="View Raw JSON"
                >
                  <FileJson className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[80vw]">
                <DialogHeader>
                  <DialogTitle>Raw JSON Data</DialogTitle>
                </DialogHeader>
                <JsonViewer json={event.rawJson || '{}'} />
              </DialogContent>
            </Dialog>
          </div>
        )
      },
    },
  ]
} 