import { db } from "~/db/db.server"
import { events } from "~/db/schema/events"
import type { InterfaceEvent } from "~/db/types/events"
import { eq, desc, inArray } from "drizzle-orm"

export class EventModel {
  /**
   * Find all events for a given dataset
   */
  static async findByDatasetName(datasetName: string): Promise<InterfaceEvent[]> {
    return db
      .select()
      .from(events)
      .where(eq(events.datasetName, datasetName))
      .orderBy(desc(events.createdDateTime));
  }

  /**
   * Find new event IDs that don't exist in the database
   */
  static async findNewEventIds(allEventIds: string[]): Promise<string[]> {
    if (!allEventIds.length) return [];

    const existingEvents = await db
      .select({ msgId: events.msgId })
      .from(events)
      .where(inArray(events.msgId, allEventIds));

    const existingEventIds = new Set(existingEvents.map(e => e.msgId));
    return allEventIds.filter(id => !existingEventIds.has(id));
  }

  /**
   * Store new events in the database
   */
  static async storeEvents(newEvents: InterfaceEvent[]): Promise<void> {
    if (!newEvents.length) return;
    await db.insert(events).values(newEvents);
  }
} 