import { createContext, useContext, ReactNode, useState, useCallback, useRef } from "react";

// Global state to track ongoing transitions
let isPageTransitioning = false;

type PageTitleContextType = {
  title: string;
  description: string | null;
  setPageTitle: (title: string, description?: string | null) => void;
  beginTransition: () => void;
  endTransition: () => void;
};

const PageTitleContext = createContext<PageTitleContextType>({
  title: "",
  description: null,
  setPageTitle: () => {},
  beginTransition: () => {},
  endTransition: () => {}
});

export function PageTitleProvider({ children }: { children: ReactNode }) {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState<string | null>(null);
  const pendingUpdatesRef = useRef<{title: string, description: string | null} | null>(null);
  
  // Mark beginning of page transition
  const beginTransition = useCallback(() => {
    isPageTransitioning = true;
  }, []);
  
  // Mark end of page transition
  const endTransition = useCallback(() => {
    isPageTransitioning = false;
    
    // Apply any pending updates that were queued during transition
    if (pendingUpdatesRef.current) {
      setTitle(pendingUpdatesRef.current.title);
      setDescription(pendingUpdatesRef.current.description);
      pendingUpdatesRef.current = null;
    }
  }, []);
  
  // Use useCallback to prevent unnecessary re-renders and ensure stable references
  const setPageTitle = useCallback((newTitle: string, newDescription: string | null = null) => {
    // If we're in transition, queue the update but don't apply yet
    if (isPageTransitioning) {
      pendingUpdatesRef.current = { title: newTitle, description: newDescription };
      return;
    }
    
    // Use functional updates to ensure we're working with the latest state
    setTitle(prev => {
      // Only update if there's an actual change
      return prev !== newTitle ? newTitle : prev;
    });
    
    setDescription(prev => {
      // Only update if there's an actual change
      return prev !== newDescription ? newDescription : prev;
    });
  }, []);

  const value = {
    title,
    description,
    setPageTitle,
    beginTransition,
    endTransition
  };

  return (
    <PageTitleContext.Provider value={value}>
      {children}
    </PageTitleContext.Provider>
  );
}

export function usePageTitle() {
  const context = useContext(PageTitleContext);
  if (!context) {
    throw new Error("usePageTitle must be used within a PageTitleProvider");
  }
  return context;
} 