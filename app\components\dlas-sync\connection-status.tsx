import { Alert, AlertDescription } from '~/components/ui/alert';
import { AlertCircle } from 'lucide-react';

interface ConnectionStatusProps {
  connected: boolean;
  actionError: string | null;
}

export function ConnectionStatus({ connected, actionError }: ConnectionStatusProps) {
  return (
    <>
      {/* Connection Status */}
      <div className="mt-4 flex items-center text-sm">
        <div className="flex items-center">
          <span 
            className={`mr-2 block h-2 w-2 rounded-full ${connected ? 'bg-green-500' : 'bg-red-500'}`} 
            aria-hidden="true" 
          />
          <span>{connected ? 'Connected to live updates' : 'Live updates disconnected'}</span>
        </div>
      </div>
      
      {/* Display action error if any */}
      {actionError && (
        <Alert className="bg-red-50 border-red-200 mt-4" role="alert">
          <AlertCircle className="h-4 w-4 text-red-500" aria-hidden="true" />
          <AlertDescription>
            {actionError}
          </AlertDescription>
        </Alert>
      )}
    </>
  );
} 