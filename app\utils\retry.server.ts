interface RetryOptions {
  maxAttempts?: number;
  initialDelay?: number;
  maxDelay?: number;
  shouldRetry?: (error: unknown) => boolean;
}

const defaultRetryOptions: Required<RetryOptions> = {
  maxAttempts: 3,
  initialDelay: 1000, // 1 second
  maxDelay: 10000,    // 10 seconds
  shouldRetry: (error) => true,
};

export class RetryError extends Error {
  constructor(
    message: string,
    public attempts: number,
    public lastError: unknown
  ) {
    super(message);
    this.name = "RetryError";
  }
}

/**
 * Executes a function with retry logic and exponential backoff
 * @param fn The async function to retry
 * @param options Retry configuration options
 * @returns The result of the function
 * @throws RetryError if all retry attempts fail
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const config = { ...defaultRetryOptions, ...options };
  let lastError: unknown;
  let delay = config.initialDelay;

  for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;

      if (attempt === config.maxAttempts || !config.shouldRetry(error)) {
        throw new RetryError(
          `Failed after ${attempt} attempts`,
          attempt,
          lastError
        );
      }

      // Log retry attempt
      console.warn(
        `Attempt ${attempt} failed, retrying in ${delay}ms...`,
        error instanceof Error ? error.message : error
      );

      // Wait before next attempt with exponential backoff
      await new Promise((resolve) => setTimeout(resolve, delay));
      
      // Exponential backoff with max delay
      delay = Math.min(delay * 2, config.maxDelay);
    }
  }

  // This should never be reached due to the throw in the loop
  throw new RetryError(
    `Failed after ${config.maxAttempts} attempts`,
    config.maxAttempts,
    lastError
  );
} 