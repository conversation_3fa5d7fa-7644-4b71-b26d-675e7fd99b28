/**
 * By default, <PERSON> will handle hydrating your app on the client for you.
 * You are free to delete this file if you'd like to, but if you ever want it revealed again, you can run `npx remix reveal` ✨
 * For more information, see https://remix.run/file-conventions/entry.client
 */

import { RemixBrowser } from '@remix-run/react';
import { startTransition, StrictMode } from 'react';
import { hydrateRoot } from 'react-dom/client';

// Patch document.documentElement to suppress hydration warnings
// This is a workaround for hydration mismatches that can't be easily fixed
document.documentElement.setAttribute('suppressHydrationWarning', 'true');

// Configure DOM error handling
const configureDOMErrorHandling = () => {
  let recoveryAttempts = 0;
  const MAX_RECOVERY_ATTEMPTS = 3;
  
  window.addEventListener('error', (event) => {
    const error = event.error;
    
    // Check if this is a DOM-related error we want to handle
    if (error instanceof DOMException) {
      const isNodeInsertionError = error.message.includes('The node before which the new node is to be inserted is not a child of this node');
      const isHierarchyError = error.message.includes('The operation would yield an incorrect node tree');
      const isHydrationError = error.message.includes('Hydration');
      
      if (isNodeInsertionError || isHierarchyError || isHydrationError) {
        // Limit recovery attempts to prevent infinite loops
        if (recoveryAttempts >= MAX_RECOVERY_ATTEMPTS) {
          console.error('Maximum DOM recovery attempts reached:', error.message);
          return;
        }
        
        recoveryAttempts++;
        console.warn(`Intercepted DOM error (attempt ${recoveryAttempts}/${MAX_RECOVERY_ATTEMPTS}):`, error.message);
        
        // Prevent the error from bubbling up and crashing the app
        event.preventDefault();
        event.stopPropagation();
        
        // Attempt to force a DOM refresh using RAF for better timing
        requestAnimationFrame(() => {
          try {
            // Force a repaint by toggling display style
            document.body.style.display = 'none';
            void document.body.offsetHeight; // Force reflow
            document.body.style.display = '';
            
            console.log('DOM recovery attempt complete');
            
            // Reset recovery count after a successful period
            setTimeout(() => {
              recoveryAttempts = 0;
            }, 1000);
          } catch (refreshError) {
            console.error('Error during DOM refresh attempt:', refreshError);
          }
        });
        
        return;
      }
    }
    
    // Log other errors but don't interfere with them
    console.error('Unhandled error:', error);
  });
};

// Initialize error handling
configureDOMErrorHandling();

// Add a small delay before hydration to allow any external scripts to finish
setTimeout(() => {
  // Attempt to hydrate the app
  try {
    startTransition(() => {
      hydrateRoot(
        document,
        <StrictMode>
          <RemixBrowser />
        </StrictMode>
      );
    });
    console.log('App hydration started');
  } catch (error) {
    console.error('Critical hydration failure:', error);
    const rootElement = document.getElementById('root');
    if (rootElement) {
      rootElement.innerHTML = '<div style="padding: 20px; font-family: system-ui, sans-serif;">An error occurred during app initialization. Please refresh the page.</div>';
    }
  }
}, 50); // Small delay to help with timing issues
