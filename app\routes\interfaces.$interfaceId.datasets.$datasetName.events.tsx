import { json, type LoaderFunctionArgs } from "@remix-run/node"
import { useLoaderData, useOutletContext } from "@remix-run/react"
import { EventModel } from "~/models/event.server"
import { InterfaceModel } from "~/models/interface.server"
import { EventsDataTable } from "~/modules/interfaces/components/event-sheet"
import type { InterfaceEvent } from "~/db/types/events"
import { useEffect } from "react"
import { useSetPageTitle } from "~/hooks/use-set-page-title"

interface LoaderData {
  events: InterfaceEvent[];
  datasetName: string;
  interfaceName: string;
}

interface ErrorResponse {
  error: string;
}

interface OutletContextType {
  interfaceName: string;
  navigateToInterface: (preserveParams?: boolean) => void;
  navigateToDataset: (preserveParams?: boolean) => void;
}

export async function loader({ params, request }: LoaderFunctionArgs) {
  const { interfaceId, datasetName } = params

  if (!interfaceId || !datasetName) {
    throw new Error("Interface ID and Dataset Name are required")
  }

  const decodedDatasetName = decodeURIComponent(datasetName)

  const [events, interface_] = await Promise.all([
    EventModel.findByDatasetName(decodedDatasetName),
    InterfaceModel.findById(interfaceId)
  ])

  if (!interface_) {
    return json<ErrorResponse>({ error: "Interface not found" }, { status: 404 })
  }

  return json<LoaderData>({
    events,
    datasetName: decodedDatasetName,
    interfaceName: interface_.interfaceName ?? 'Unknown Interface'
  })
}

export async function action({ request, params }: LoaderFunctionArgs) {
  const { datasetName } = params

  if (!datasetName) {
    throw new Error("Dataset Name is required")
  }

  return json({ success: false })
}

export default function EventsPage() {
  const loaderData = useLoaderData<typeof loader>()
  const { navigateToInterface, navigateToDataset } = useOutletContext<OutletContextType>()
  
  // Handle error case
  if ('error' in loaderData) {
    return <div className="text-red-500">{loaderData.error}</div>
  }

  const { events, interfaceName, datasetName } = loaderData
  
  // Set the page title
  useSetPageTitle("Events", `Events for dataset ${datasetName}`);

  // Transform data back to the correct format
  // We need to deserialize the date which is serialized as JSON
  const transformedEvents = events.map(event => {
    try {
      // Ensure createdDateTime is a proper Date object
      return {
        ...event,
        createdDateTime: new Date(event.createdDateTime as unknown as string)
      };
    } catch (error) {
      console.error("Error processing event:", error);
      // Return event with current date if processing fails
      return {
        ...event,
        createdDateTime: new Date()
      };
    }
  });

  return (
    <div className="space-y-4">
      <EventsDataTable
        data={transformedEvents}
        interfaceName={interfaceName}
        datasetName={datasetName}
        onNavigateToInterface={(preserveParams) => navigateToInterface(preserveParams)}
        onNavigateToDataset={(preserveParams) => navigateToDataset(preserveParams)}
      />
    </div>
  )
} 