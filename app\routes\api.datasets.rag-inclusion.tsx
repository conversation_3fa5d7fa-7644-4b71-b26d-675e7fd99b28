import { json, type ActionFunction } from "@remix-run/node";
import { DatasetModel } from "~/models/dataset.server";

export const action: ActionFunction = async ({ request }) => {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  const formData = await request.formData();
  const datasetIds = formData.getAll("datasetIds[]");
  const include = formData.get("include") === 'true';

  // Single dataset toggle
  const datasetName = formData.get("datasetName");
  if (datasetName && typeof datasetName === 'string') {
    try {
      await DatasetModel.toggleRagInclusion(datasetName, include);
      return json({ success: true });
    } catch (error) {
      console.error("Error toggling RAG inclusion for dataset:", error);
      return json(
        { error: "Failed to toggle RAG inclusion status" },
        { status: 500 }
      );
    }
  }

  // Bulk update
  if (!datasetIds.length) {
    return json(
      { error: "Dataset IDs are required" },
      { status: 400 }
    );
  }

  try {
    await DatasetModel.updateRagInclusion(datasetIds as string[], include);
    return json({ success: true });
  } catch (error) {
    console.error("Error updating RAG inclusion status:", error);
    return json(
      { error: "Failed to update RAG inclusion status" },
      { status: 500 }
    );
  }
}; 