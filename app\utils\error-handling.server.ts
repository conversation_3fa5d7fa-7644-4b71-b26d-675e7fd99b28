import { json } from "@remix-run/node";
import { DLASApiError } from "~/services/dlas.server";
import { RetryError } from "~/utils/retry.server";

export interface ErrorResponse {
  error: string;
  status: number;
}

export function handleApiError(error: unknown): Response {
  console.error("API Error:", error);

  if (error instanceof DLASApiError) {
    return json(
      { error: `DLAS API Error: ${error.message}` },
      { status: error.status }
    );
  }

  if (error instanceof RetryError) {
    const lastError = error.lastError;
    const errorMessage = lastError instanceof Error 
      ? lastError.message 
      : 'Unknown error';
    
    return json(
      { error: `Failed after ${error.attempts} attempts: ${errorMessage}` },
      { status: 500 }
    );
  }

  return json(
    { error: "An unexpected error occurred" },
    { status: 500 }
  );
} 