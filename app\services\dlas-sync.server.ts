import { DLASSyncProcessor } from './dlas-sync-processor.server';
import { DLASSyncProgressTracker, DLASSyncState, type DLASSyncProgress } from './dlas-sync-progress.server';
import { DLASSyncBroadcaster } from './dlas-sync-broadcast.server';
import { getDefaultDLASSyncConfig, mergeDLASSyncConfig, type DLASSyncConfig as BaseConfig } from './dlas-sync-config.server';
import { Logger } from '~/utils/sync-utils.server';

// Extend the base config with our additional parameters
export interface DLASSyncConfig extends BaseConfig {
  applicationId?: string;
  date?: string; // Optional date parameter in YYYY-MM-DD format
}

/**
 * Service for managing DLAS synchronization
 */
export class DLASSyncService {
  private logger: Logger;
  private progressTracker: DLASSyncProgressTracker;
  private broadcaster: DLASSyncBroadcaster;
  private config: DLASSyncConfig;
  private syncLock = false;
  private abortController: AbortController | null = null;

  constructor() {
    this.logger = new Logger('DLAS Sync');
    this.broadcaster = new DLASSyncBroadcaster();
    this.progressTracker = new DLASSyncProgressTracker(this.broadcaster);
    this.config = {
      ...getDefaultDLASSyncConfig(),
      applicationId: undefined,
      date: undefined
    };
    this.logger.log('DLASSyncService initialized');
  }

  private async executeWithLock<T>(operation: () => Promise<T>): Promise<{ success: boolean; result?: T }> {
    if (this.syncLock) {
      this.logger.log('Operation already in progress, cannot acquire lock');
      return { success: false };
    }
    this.logger.log('Acquired lock for sync operation');
    this.syncLock = true;
    try {
      const result = await operation();
      return { success: true, result };
    } catch (error) {
      this.logger.error('Error during sync operation', error);
      throw error;
    } finally {
      this.syncLock = false;
      this.logger.log('Released lock for sync operation');
    }
  }

  public async startSync(options: Partial<DLASSyncConfig> = {}): Promise<{ success: boolean; message?: string }> {
    const result = await this.executeWithLock(async () => {
      if (this.progressTracker.getState() === DLASSyncState.RUNNING) {
        return { success: false, message: 'Sync already in progress' };
      }
      try {
        this.config = {
          ...mergeDLASSyncConfig(options),
          applicationId: options.applicationId,
          date: options.date
        };
        this.abortController = new AbortController();
        const processor = new DLASSyncProcessor(this.progressTracker);
        await processor.processSyncJob(
          this.abortController.signal,
          this.config.applicationId,
          this.config.date
        );
        if (this.progressTracker.getState() === DLASSyncState.RUNNING) {
          this.progressTracker.completeSync();
          this.logger.log('Sync completed successfully');
        }
        return { success: true };
      } catch (error) {
        this.logger.error('Error during sync operation', error);
        if (this.progressTracker.getState() === DLASSyncState.RUNNING) {
          this.progressTracker.errorSync(error);
        }
        return { success: false, message: 'Sync operation failed' };
      } finally {
        this.abortController = null;
        if (this.progressTracker.getState() === DLASSyncState.RUNNING) {
          this.logger.warn('Sync operation finished but state still running - forcing to idle');
          this.progressTracker.resetProgress();
        }
      }
    });
    return result.success 
      ? result.result || { success: true }
      : { success: false, message: 'Could not start sync, another operation is in progress' };
  }

  public async stopSync(): Promise<{ success: boolean; message?: string }> {
    if (!this.abortController) {
      return { success: false, message: 'No sync operation in progress' };
    }
    try {
      this.logger.log('Stopping sync operation');
      this.abortController.abort();
      this.progressTracker.resetProgress();
      this.syncLock = false;
      return { success: true, message: 'Sync operation stopped' };
    } catch (error) {
      this.logger.error('Error stopping sync operation', error);
      this.progressTracker.resetProgress();
      this.syncLock = false;
      return { success: false, message: 'Failed to stop sync operation' };
    } finally {
      this.abortController = null;
    }
  }

  public isLocked(): boolean {
    return this.syncLock;
  }

  public getSyncStatus(): { state: DLASSyncState; progress: DLASSyncProgress } {
    const state = this.progressTracker.getState();
    const progress = this.progressTracker.getProgress();
    return { state, progress };
  }

  public updateConfig(newConfig: Partial<DLASSyncConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  public getConfig(): DLASSyncConfig {
    return { ...this.config };
  }
}

// Create singleton instance
const dlasSyncService = new DLASSyncService();

/**
 * Get the singleton instance of DLASSyncService
 */
const getDLASSyncService = () => dlasSyncService;

// Re-export types and functions
export { DLASSyncState, type DLASSyncProgress } from './dlas-sync-progress.server';
export { getNextScheduledRunTime } from './dlas-sync-config.server';
export { setupDailySync, initializeDLASSyncScheduler } from './dlas-sync-scheduler.server';

// Export the service getter
export { getDLASSyncService }; 