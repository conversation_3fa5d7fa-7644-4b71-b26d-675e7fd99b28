// Maximum number of recently viewed items to store
import { getSettings } from './settings';

// Default value is managed in settings.ts
const STORAGE_KEY = 'oms_quick_access';

// Type definition for quick access items
export interface QuickAccessItem {
  id: string;
  name: string;
  type: 'Application' | 'Interface';
  timestamp: number;
  path: string;
  pinned: boolean;
  // Organization level information
  orgLevel4?: string;
  orgLevel5?: string;
  // RAG status information
  ragStatus?: {
    red: number;
    amber: number;
    green: number;
  };
  // Additional IDs for status tracking
  applicationId?: string;  // For application and interface types
  interfaceId?: string;   // For interface types
}

/**
 * Get all quick access items sorted with pinned items first, then by timestamp
 */
export function getQuickAccessItems(): QuickAccessItem[] {
  // Only run on client-side
  if (typeof window === 'undefined') return [];
  
  try {
    const storedItems = localStorage.getItem(STORAGE_KEY);
    if (!storedItems) return [];
    
    const items = JSON.parse(storedItems) as QuickAccessItem[];
    
    // Sort items: pinned first, then by most recent
    return items.sort((a, b) => {
      // First sort by pinned status (pinned items come first)
      if (a.pinned && !b.pinned) return -1;
      if (!a.pinned && b.pinned) return 1;
      
      // Then sort by timestamp (most recent first)
      return b.timestamp - a.timestamp;
    });
  } catch (error) {
    console.error('Failed to retrieve quick access items:', error);
    return [];
  }
}

/**
 * Ensures filter parameters are preserved in the path
 * This includes both regular search parameters and faceted filter parameters
 * 
 * @param path The base path without parameters
 * @param currentLocation The current location object with search parameters
 * @returns Path with all filter parameters preserved
 */
export function getPathWithFilters(path: string, currentLocation: { search: string }): string {
  if (!currentLocation.search) return path;
  
  const currentParams = new URLSearchParams(currentLocation.search);
  
  // Check if the path already has parameters
  const [basePath, existingParams] = path.split('?');
  const pathParams = new URLSearchParams(existingParams || '');
  
  // Add or update all parameters from the current location
  // This includes both standard search params and filter_ params for faceted filters
  for (const [key, value] of currentParams.entries()) {
    // Keep all parameters, especially those starting with filter_
    pathParams.append(key, value);
  }
  
  const paramString = pathParams.toString();
  return paramString ? `${basePath}?${paramString}` : basePath;
}

/**
 * Add an item to quick access
 */
export function addQuickAccessItem(item: Omit<QuickAccessItem, 'timestamp' | 'pinned'>): void {
  if (typeof window === 'undefined') return;
  
  try {
    const currentItems = getQuickAccessItems();
    const { maxQuickAccessItems } = getSettings();
    
    // Check if item already exists
    const existingItemIndex = currentItems.findIndex(i => 
      i.id === item.id && i.type === item.type
    );
    
    // Create new array to store updated items
    let updatedItems: QuickAccessItem[];
    
    if (existingItemIndex >= 0) {
      // If item exists, preserve its pinned status but update other properties
      const existingItem = currentItems[existingItemIndex];
      updatedItems = [...currentItems];
      updatedItems.splice(existingItemIndex, 1);
      
      // Add the updated item at the beginning with current timestamp
      updatedItems.unshift({
        ...item,
        pinned: existingItem.pinned, // Keep existing pinned status
        timestamp: Date.now()
      });
    } else {
      updatedItems = [...currentItems];
      
      // Add the new item at the beginning with current timestamp
      updatedItems.unshift({
        ...item,
        pinned: false, // New items are not pinned by default
        timestamp: Date.now()
      });
    }
    
    // Limit to maximum number of items, but KEEP all pinned items regardless of count
    const pinnedItems = updatedItems.filter(item => item.pinned);
    const unpinnedItems = updatedItems.filter(item => !item.pinned);
    
    // Only limit unpinned items to the max count
    if (unpinnedItems.length > maxQuickAccessItems) {
      updatedItems = [
        ...pinnedItems,
        ...unpinnedItems.slice(0, maxQuickAccessItems)
      ];
    }
    
    // Save back to localStorage
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedItems));
  } catch (error) {
    console.error('Failed to add quick access item:', error);
  }
}

/**
 * Add an item to quick access and pin it by default
 */
export function addPinnedQuickAccessItem(item: Omit<QuickAccessItem, 'timestamp' | 'pinned'>): void {
  if (typeof window === 'undefined') return;
  
  try {
    const currentItems = getQuickAccessItems();
    const { maxQuickAccessItems } = getSettings();
    
    // Check if item already exists
    const existingItemIndex = currentItems.findIndex(i => 
      i.id === item.id && i.type === item.type
    );
    
    // Create new array to store updated items
    let updatedItems: QuickAccessItem[];
    
    if (existingItemIndex >= 0) {
      // If item exists, update it and set pinned to true
      updatedItems = [...currentItems];
      updatedItems.splice(existingItemIndex, 1);
      
      // Add the updated item at the beginning with current timestamp and pinned
      updatedItems.unshift({
        ...item,
        pinned: true, // Always pin the item when using this function
        timestamp: Date.now()
      });
    } else {
      updatedItems = [...currentItems];
      
      // Add the new item at the beginning with current timestamp and pinned
      updatedItems.unshift({
        ...item,
        pinned: true, // Always pin the item when using this function
        timestamp: Date.now()
      });
    }
    
    // Limit to maximum number of items, but KEEP all pinned items regardless of count
    const pinnedItems = updatedItems.filter(item => item.pinned);
    const unpinnedItems = updatedItems.filter(item => !item.pinned);
    
    // Only limit unpinned items to the max count
    if (unpinnedItems.length > maxQuickAccessItems) {
      updatedItems = [
        ...pinnedItems,
        ...unpinnedItems.slice(0, maxQuickAccessItems)
      ];
    }
    
    // Save back to localStorage
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedItems));
  } catch (error) {
    console.error('Failed to add pinned quick access item:', error);
  }
}

/**
 * Toggle pinned status of an item
 */
export function togglePinnedStatus(itemId: string, itemType: 'Application' | 'Interface'): boolean {
  if (typeof window === 'undefined') return false;
  
  try {
    const currentItems = getQuickAccessItems();
    
    // Find the item
    const itemIndex = currentItems.findIndex(i => 
      i.id === itemId && i.type === itemType
    );
    
    if (itemIndex === -1) return false;
    
    // If we're unpinning, remove the item completely
    if (currentItems[itemIndex].pinned) {
      // Remove the item
      const updatedItems = currentItems.filter((_, index) => index !== itemIndex);
      localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedItems));
      return false;
    } else {
      // If we're pinning, update the pinned status
      const updatedItems = [...currentItems];
      updatedItems[itemIndex] = {
        ...updatedItems[itemIndex],
        pinned: true
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedItems));
      return true;
    }
  } catch (error) {
    console.error('Failed to toggle pinned status:', error);
    return false;
  }
}

/**
 * Clear all quick access items
 */
export function clearQuickAccessItems(): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.error('Failed to clear quick access items:', error);
  }
}

/**
 * Format timestamp to relative time (e.g., "2 days ago", "Yesterday, 3:45 PM", "Today, 10:30 AM")
 */
export function formatTimestamp(timestamp: number): string {
  const now = new Date();
  const date = new Date(timestamp);
  
  const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    // Today
    return `Today, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  } else if (diffDays === 1) {
    // Yesterday
    return `Yesterday, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  } else if (diffDays < 7) {
    // Within a week
    return `${diffDays} days ago`;
  } else {
    // More than a week
    return date.toLocaleDateString();
  }
}

/**
 * Update quick access items with RAG status information
 * 
 * @param ragStatuses Object containing RAG status information for interfaces and applications
 */
export function updateQuickAccessItemsWithRagStatus(
  ragStatuses: {
    interfaces: Record<string, { counts: { red: number; amber: number; green: number } }>;
    applications: Record<string, { counts: { red: number; amber: number; green: number } }>;
  }
): void {
  if (typeof window === 'undefined') return;
  
  try {
    const currentItems = getQuickAccessItems();
    let hasUpdates = false;
    
    const updatedItems = currentItems.map(item => {
      // For interface items, update with interface RAG status
      if (item.type === 'Interface' && item.id) {
        const interfaceStatus = ragStatuses.interfaces[item.id];
        if (interfaceStatus) {
          hasUpdates = true;
          return {
            ...item,
            ragStatus: {
              red: interfaceStatus.counts.red,
              amber: interfaceStatus.counts.amber,
              green: interfaceStatus.counts.green
            }
          };
        }
      }
      
      // For application items, update with application RAG status
      if (item.type === 'Application' && item.id) {
        const applicationStatus = ragStatuses.applications[item.id];
        if (applicationStatus) {
          hasUpdates = true;
          return {
            ...item,
            ragStatus: {
              red: applicationStatus.counts.red,
              amber: applicationStatus.counts.amber,
              green: applicationStatus.counts.green
            }
          };
        }
      }
      
      return item;
    });
    
    // Only update localStorage if we made changes
    if (hasUpdates) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedItems));
    }
  } catch (error) {
    console.error('Failed to update quick access items with RAG status:', error);
  }
} 