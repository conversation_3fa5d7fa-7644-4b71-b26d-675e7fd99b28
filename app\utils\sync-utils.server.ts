import { db } from "~/db/db.server";
import { inArray, eq } from "drizzle-orm";
import { SQLiteColumn, SQLiteTable } from "drizzle-orm/sqlite-core";
import type { AppLoadContext } from '@remix-run/node';
import { 
    getSyncBatchSize, 
    getSyncChunkSize 
} from '~/utils/env.server';

/**
 * Basic configuration for sync operations
 */
export interface SyncConfig {
    batchSize?: number;
    retryAttempts?: number;
    applicationId?: string;
    date?: string;
}

export const DEFAULT_CONFIG = {
    batchSize: 100,
    retryAttempts: 3
} as const;

export function getConfig(options: Partial<SyncConfig> = {}, context?: AppLoadContext): SyncConfig {
    return {
        batchSize: options.batchSize ?? getSyncBatchSize(context),
        retryAttempts: options.retryAttempts ?? 3,
        applicationId: options.applicationId,
        date: options.date
    };
}

/**
 * Split array into chunks of specified size
 */
export function chunk<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
    }
    return chunks;
}

/**
 * Batch upsert records into database
 */
export async function batchUpsert<T extends Record<string, any>>(
    table: SQLiteTable,
    records: T[],
    idField: SQLiteColumn,
    batchSize: number = 100
): Promise<void> {
    const batches = chunk(records, batchSize);
    for (const batch of batches) {
        await db.transaction(async (tx) => {
            for (const record of batch) {
                await tx.insert(table)
                    .values(record)
                    .onConflictDoUpdate({
                        target: [idField],
                        set: record
                    });
            }
        });
    }
}

/**
 * Find existing record IDs in database
 */
export async function findExistingIds(
    table: SQLiteTable,
    idField: SQLiteColumn,
    ids: string[]
): Promise<Set<string>> {
    const results = await db
        .select({ id: idField })
        .from(table)
        .where(inArray(idField, ids));
    return new Set(results.map(r => r.id as string));
}

/**
 * Simplified logger for sync operations
 */
export class Logger {
    private prefix: string;
    private startTime: number;

    constructor(prefix: string) {
        this.prefix = prefix;
        this.startTime = Date.now();
    }

    log(message: string, data?: unknown): void {
        console.log(`[${this.prefix}] ${message}`, data || '');
    }

    error(message: string, error: unknown): void {
        console.error(`[${this.prefix}] ${message}:`, error instanceof Error ? error.message : String(error));
    }

    warn(message: string): void {
        console.warn(`[${this.prefix}] ${message}`);
    }

    progress(current: number, total: number): void {
        const percentage = Math.round((current / total) * 100);
        this.log(`Progress: ${current}/${total} (${percentage}%)`);
    }

    summarize(): void {
        const duration = (Date.now() - this.startTime) / 1000;
        this.log(`Operation completed in ${duration.toFixed(1)}s`);
    }
}

/**
 * Custom error for sync operations
 */
export class SyncError extends Error {
    constructor(
        message: string,
        public readonly code: string,
        public readonly data?: unknown
    ) {
        super(message);
        this.name = 'SyncError';
    }
}

/**
 * Handle sync operation with standardized error handling
 */
export async function handleSyncOperation<T>(
    operation: () => Promise<T>,
    logger: Logger
): Promise<T> {
    try {
        return await operation();
    } catch (error) {
        logger.error('Operation failed', error);
        throw new SyncError(
            error instanceof Error ? error.message : 'Unknown error',
            'SYNC_ERROR',
            { originalError: error }
        );
    }
} 