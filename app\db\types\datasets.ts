import { datasets } from '../schema/datasets';

// Database Types
export type Dataset = typeof datasets.$inferSelect;
export type NewDataset = typeof datasets.$inferInsert;

// API Response Types
export interface DLASDataset {
  Status: string;
  Direction: string;
  EimInterfaceID: string | null;
  DatasetStatus: string;
  DatasetName: string;
  Description: string;
  InterfaceSerial: number;
  TransferType: string;
  Frequency: string;
  PrimaryDataTerm: {
    name: string;
  };
  ProductType: string[];
  RelatedDrilldownList: string[];
  BusinessContexts?: BusinessContext[];
}

// Business Context type
export interface BusinessContext {
  country: string;
  legal_entity: string;
  legal_entity_code: string;
  line_of_business_name: string;
}

export interface DLASDatasetResponse {
  dataset_logged_list: DLASDataset[];
}

// Extended Dataset type with SLA
export interface DatasetWithSLA extends Omit<Dataset, 'lastArrivalTime' | 'eventCount'> {
  sla: string | null;
  lastArrivalTime: number | null;
  events?: DatasetEvent[];
  includeInRagStatus: boolean;
  eventCount: number;
}

// Dataset Event type
export interface DatasetEvent {
  id: string;
  datasetId: string;
  transferTime: string;
  status: string;
  metadata?: Record<string, unknown>;
}

// Define the ProductType type for better type safety
export type ProductType = string[];

// Define the RelatedDrilldownList
export type RelatedDrilldownList = string[];

// Define the PrimaryDataTerm structure
export interface PrimaryDataTerm {
  name: string;
}

// Define the Dataset status enum
export enum DatasetStatus {
  NORMAL = 'NORMAL',
  ERROR = 'ERROR',
  WARNING = 'WARNING',
}

// Define the Dataset direction enum
export enum DatasetDirection {
  IN = 'IN',
  OUT = 'OUT',
}

// Define the Dataset status enum
export enum DatasetDlasStatus {
  COLLIBRA_UPDATED = 'COLLIBRA UPDATED',
  DLAS_ONLY = 'DLAS ONLY',
  DLAS_EIM_MATCHED = 'DLAS&EIM MATCHED (DM1)',
} 