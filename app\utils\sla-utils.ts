import * as rruleLib from 'rrule';
const { RRule } = rruleLib;
import type { 
  SLAConfig, WeekDay, WeekNumber, 
  DailySLA, WeeklySLA, MonthlySLA, YearlySLA 
} from '../types/sla';

// Constants
export const WEEKDAYS: WeekDay[] = ['MO', 'TU', 'WE', 'TH', 'FR', 'SA', 'SU'];
export const MONTHS = Array.from({ length: 12 }, (_, i) => i + 1);
export const WEEK_NUMBERS: WeekNumber[] = [1, 2, 3, 4, -1];

// Helper Functions
export const formatTime = (time: string): string => {
  const [hours, minutes] = time.split(':');
  const hour = parseInt(hours, 10);
  const period = hour >= 12 ? 'PM' : 'AM';
  const formattedHour = hour % 12 || 12;
  return `${formattedHour}:${minutes} ${period}`;
};

export const formatWeekDays = (weekDays: WeekDay[]): string => {
  const days = weekDays.map(day => {
    const fullDay = {
      'MO': 'Monday', 'TU': 'Tuesday', 'WE': 'Wednesday',
      'TH': 'Thursday', 'FR': 'Friday', 'SA': 'Saturday', 'SU': 'Sunday'
    }[day];
    return fullDay;
  });
  
  if (days.length === 1) return days[0];
  if (days.length === 2) return `${days[0]} and ${days[1]}`;
  return `${days.slice(0, -1).join(', ')}, and ${days[days.length - 1]}`;
};

export const formatMonth = (month: number): string => {
  return new Date(2000, month - 1).toLocaleString('default', { month: 'long' });
};

export const formatWeekNumber = (week: WeekNumber): string => {
  if (week === -1) return 'last';
  const suffixes = ['st', 'nd', 'rd', 'th'];
  const suffix = week <= 3 ? suffixes[week - 1] : suffixes[3];
  return `${week}${suffix}`;
};

interface RRuleOptions {
  freq: number;
  interval: number;
  dtstart: Date;
  byhour: number;
  byminute: number;
  count?: number;
  until?: Date;
  byweekday?: rruleLib.Weekday[];
  bymonthday?: number;
  bymonth?: number;
}

const getWeekday = (day: WeekDay): rruleLib.Weekday => {
  const weekdayMap: Record<WeekDay, rruleLib.Weekday> = {
    'MO': RRule.MO,
    'TU': RRule.TU,
    'WE': RRule.WE,
    'TH': RRule.TH,
    'FR': RRule.FR,
    'SA': RRule.SA,
    'SU': RRule.SU
  };
  return weekdayMap[day];
};

export const generateRRule = (config: SLAConfig): rruleLib.RRule => {
  const baseOptions: RRuleOptions = {
    freq: RRule.YEARLY,  // Will be overridden below
    interval: config.interval,
    dtstart: new Date(),
    byhour: parseInt(config.time.split(':')[0], 10),
    byminute: parseInt(config.time.split(':')[1], 10)
  };

  // Set the correct frequency based on pattern
  switch (config.pattern) {
    case 'daily':
      baseOptions.freq = RRule.DAILY;
      break;
    case 'weekly':
      baseOptions.freq = RRule.WEEKLY;
      break;
    case 'monthly':
      baseOptions.freq = RRule.MONTHLY;
      break;
    case 'yearly':
      baseOptions.freq = RRule.YEARLY;
      break;
  }

  if (config.endType === 'after' && config.endAfterCount) {
    baseOptions.count = config.endAfterCount;
  } else if (config.endType === 'on' && config.endOnDate) {
    baseOptions.until = new Date(config.endOnDate);
  }

  switch (config.pattern) {
    case 'weekly':
      const weekConfig = config as WeeklySLA;
      return new RRule({
        ...baseOptions,
        byweekday: weekConfig.weekDays.map(day => getWeekday(day))
      });

    case 'monthly':
      const monthConfig = config as MonthlySLA;
      if (monthConfig.byDay) {
        return new RRule({
          ...baseOptions,
          byweekday: getWeekday(monthConfig.byDay.day).nth(monthConfig.byDay.week)
        });
      }
      return new RRule({
        ...baseOptions,
        bymonthday: monthConfig.byDate
      });

    case 'yearly':
      const yearConfig = config as YearlySLA;
      const options: RRuleOptions = {
        ...baseOptions,
        bymonth: yearConfig.month
      };
      
      if (yearConfig.byDay) {
        return new RRule({
          ...options,
          byweekday: getWeekday(yearConfig.byDay.day).nth(yearConfig.byDay.week)
        });
      }
      return new RRule({
        ...options,
        bymonthday: yearConfig.byDate
      });

    default:
      return new RRule(baseOptions);
  }
};

export const generateSLAText = (config: SLAConfig): string => {
  const timeStr = formatTime(config.time);
  const intervalStr = config.interval === 1 ? '' : `every ${config.interval} `;
  
  let recurrenceStr = '';
  switch (config.pattern) {
    case 'daily':
      recurrenceStr = `${intervalStr}day(s)`;
      break;
      
    case 'weekly':
      const weekConfig = config as WeeklySLA;
      recurrenceStr = `${intervalStr}week(s) on ${formatWeekDays(weekConfig.weekDays)}`;
      break;
      
    case 'monthly':
      const monthConfig = config as MonthlySLA;
      if (monthConfig.byDay) {
        recurrenceStr = `${intervalStr}month(s) on the ${formatWeekNumber(monthConfig.byDay.week)} ${formatWeekDays([monthConfig.byDay.day])}`;
      } else {
        recurrenceStr = `${intervalStr}month(s) on day ${monthConfig.byDate}`;
      }
      break;
      
    case 'yearly':
      const yearConfig = config as YearlySLA;
      const monthStr = formatMonth(yearConfig.month);
      if (yearConfig.byDay) {
        recurrenceStr = `${intervalStr}year(s) on the ${formatWeekNumber(yearConfig.byDay.week)} ${formatWeekDays([yearConfig.byDay.day])} of ${monthStr}`;
      } else {
        recurrenceStr = `${intervalStr}year(s) on ${monthStr} ${yearConfig.byDate}`;
      }
      break;
  }
  
  let endStr = '';
  if (config.endType === 'after' && config.endAfterCount) {
    endStr = ` for ${config.endAfterCount} occurrences`;
  } else if (config.endType === 'on' && config.endOnDate) {
    const endDate = new Date(config.endOnDate);
    endStr = ` until ${endDate.toLocaleDateString()}`;
  }
  
  return `Occurs at ${timeStr} ${recurrenceStr}${endStr}`;
};

export const parseRRuleString = (rruleStr: string | null): SLAConfig | null => {
  if (!rruleStr) return null;
  try {
    const rrule = RRule.fromString(rruleStr);
    const options = rrule.options;
    
    const baseConfig = {
      pattern: options.freq === RRule.DAILY ? 'daily' :
               options.freq === RRule.WEEKLY ? 'weekly' :
               options.freq === RRule.MONTHLY ? 'monthly' : 'yearly',
      interval: options.interval || 1,
      time: `${String(options.byhour || 0).padStart(2, '0')}:${String(options.byminute || 0).padStart(2, '0')}`,
      endType: options.until ? 'on' : options.count ? 'after' : 'never',
      endOnDate: options.until ? options.until.toISOString() : undefined,
      endAfterCount: options.count
    };

    switch (baseConfig.pattern) {
      case 'daily':
        return baseConfig as DailySLA;
      
      case 'weekly':
        return {
          ...baseConfig,
          weekDays: (options.byweekday || [RRule.MO])
            .map(d => WEEKDAYS[d as number])
        } as WeeklySLA;
      
      case 'monthly':
        if (options.byweekday?.[0] && options.bysetpos?.[0]) {
          return {
            ...baseConfig,
            byDay: {
              week: options.bysetpos[0] as WeekNumber,
              day: WEEKDAYS[options.byweekday[0] as number]
            }
          } as MonthlySLA;
        }
        return {
          ...baseConfig,
          byDate: options.bymonthday?.[0] || 1
        } as MonthlySLA;
      
      case 'yearly':
        if (options.byweekday?.[0] && options.bysetpos?.[0]) {
          return {
            ...baseConfig,
            month: options.bymonth?.[0] || 1,
            byDay: {
              week: options.bysetpos[0] as WeekNumber,
              day: WEEKDAYS[options.byweekday[0] as number]
            }
          } as YearlySLA;
        }
        return {
          ...baseConfig,
          month: options.bymonth?.[0] || 1,
          byDate: options.bymonthday?.[0] || 1
        } as YearlySLA;
    }
    return null;
  } catch (e) {
    console.error('Failed to parse RRule string:', e);
    return null;
  }
}; 