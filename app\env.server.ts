/**
 * Environment variable access for server components
 * This is a simplified facade over the more detailed utils/env.server.ts
 */
import { 
  getRequiredEnv, 
  getHeetApiUrl as getHeetApiUrlUtil,
  getDlasApiUrl as getDlasApiUrlUtil,
  getScheduledSyncTime as getScheduledSyncTimeUtil,
  getScheduledSyncHour as getScheduledSyncHourUtil,
  getScheduledSyncMinute as getScheduledSyncMinuteUtil
} from './utils/env.server';

export const env = {
  get HEET_API_URL() {
    return getHeetApiUrlUtil();
  },
  
  get DLAS_API_URL() {
    return getDlasApiUrlUtil();
  },
  
  get DATABASE_URL() {
    return getRequiredEnv('DATABASE_URL');
  },

  get SCHEDULED_SYNC_TIME() {
    return getScheduledSyncTimeUtil();
  },

  get SCHEDULED_SYNC_HOUR() {
    return getScheduledSyncHourUtil();
  },

  get SCHEDULED_SYNC_MINUTE() {
    return getScheduledSyncMinuteUtil();
  }
}; 