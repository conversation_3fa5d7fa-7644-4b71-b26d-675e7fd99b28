import { SyncProgress } from '~/types/dlas-sync';
import { Progress } from '~/components/ui/progress';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { AlertCircle, Clock, ChevronDown, ChevronUp } from 'lucide-react';
import { calculateProgressPercent } from '~/utils/dlas-sync-utils';
import { useState } from 'react';
import { Button } from '~/components/ui/button';

interface ProgressDisplayProps {
  progress?: SyncProgress | null;
}

/**
 * Format elapsed time in milliseconds to a human-readable string
 */
function formatElapsedTime(elapsedTimeMs: number): string {
  if (elapsedTimeMs < 1000) return `${elapsedTimeMs}ms`;
  
  const seconds = Math.floor(elapsedTimeMs / 1000);
  if (seconds < 60) return `${seconds}s`;
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}m ${remainingSeconds}s`;
}

function getSuccessRateBadgeColor(successRate: number): string {
  if (successRate === 100) return 'bg-green-100 text-green-800';
  if (successRate >= 50) return 'bg-orange-100 text-orange-800';
  if (successRate > 0) return 'bg-red-100 text-red-800';
  return 'bg-slate-100 text-slate-800';
}

export function ProgressDisplay({ progress }: ProgressDisplayProps) {
  const [showErrors, setShowErrors] = useState(false);

  // If progress is undefined or null, show empty state
  if (!progress) {
    return (
      <div className="rounded-lg border p-4 space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-lg font-medium">Overall Progress</span>
          <span className="text-sm font-medium">0% completed</span>
        </div>
        <Progress 
          value={0} 
          className="h-2 overflow-hidden rounded-full bg-slate-200"
          aria-label="Sync progress: 0%"
        />
        <div className="text-sm text-muted-foreground text-center">
          No sync progress data available
        </div>
      </div>
    );
  }

  const progressPercent = calculateProgressPercent(progress);
  const elapsedTime = formatElapsedTime(progress.elapsedTimeMs ?? 0);

  // Calculate success rates with null safety
  const calculateSuccessRate = (success: number | undefined | null, total: number | undefined | null): number => {
    const safeSuccess = success ?? 0;
    const safeTotal = total ?? 0;
    if (safeTotal === 0) return 0;
    return Math.round((safeSuccess / safeTotal) * 100);
  };

  const applicationsSuccessRate = calculateSuccessRate(progress.successCount, progress.totalItems);
  const interfacesSuccessRate = calculateSuccessRate(progress.successInterfaces, progress.totalInterfaces);
  const datasetsSuccessRate = calculateSuccessRate(progress.successDatasets, progress.totalDatasets);

  return (
    <>
      <div className="rounded-lg border p-4 space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-lg font-medium">Overall Progress</span>
          <span className="text-sm font-medium">{progressPercent}% completed</span>
        </div>
        
        <Progress 
          value={progressPercent} 
          className="h-2 overflow-hidden rounded-full bg-slate-200"
          aria-label={`Sync progress: ${progressPercent}%`}
        />
        
        <div className="grid grid-cols-3 gap-4">
          {/* Applications Progress */}
          <div className="p-4 rounded-lg border">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-medium text-muted-foreground">Applications</h3>
              <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getSuccessRateBadgeColor(applicationsSuccessRate)}`}>
                {applicationsSuccessRate}%
              </span>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>Total:</span>
                <span className="font-medium">{progress.totalItems ?? 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Processed:</span>
                <span className="font-medium">{progress.processedItems ?? 0}</span>
              </div>
            </div>
          </div>

          {/* Interfaces Progress */}
          <div className="p-4 rounded-lg border">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-medium text-muted-foreground">Interfaces</h3>
              <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getSuccessRateBadgeColor(interfacesSuccessRate)}`}>
                {interfacesSuccessRate}%
              </span>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>Total:</span>
                <span className="font-medium">{progress.totalInterfaces ?? 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Processed:</span>
                <span className="font-medium">{progress.processedInterfaces ?? 0}</span>
              </div>
            </div>
          </div>

          {/* Datasets Progress */}
          <div className="p-4 rounded-lg border">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-medium text-muted-foreground">Datasets</h3>
              <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getSuccessRateBadgeColor(datasetsSuccessRate)}`}>
                {datasetsSuccessRate}%
              </span>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>Total:</span>
                <span className="font-medium">{progress.totalDatasets ?? 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Processed:</span>
                <span className="font-medium">{progress.processedDatasets ?? 0}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          <span>Elapsed time: {elapsedTime}</span>
        </div>
      </div>

      {progress.errors?.length > 0 && (
        <div className="mt-4 space-y-2">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between w-full">
              <span>
                {progress.errors.length} error{progress.errors.length > 1 ? 's' : ''} occurred during sync
              </span>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 hover:bg-destructive/20"
                onClick={() => setShowErrors(!showErrors)}
              >
                {showErrors ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </AlertDescription>
          </Alert>
          
          {showErrors && (
            <div className="rounded-lg border border-destructive/50 p-4 space-y-2">
              {progress.errors.map((error, index) => (
                <div key={index} className="text-sm space-y-1">
                  <div className="font-medium text-destructive">Error {index + 1}:</div>
                  <div className="text-muted-foreground">{error.message}</div>
                  {error.item && (
                    <div className="text-xs text-muted-foreground">
                      Item: {error.item}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </>
  );
} 