import { createServer } from '@remix-run/serve';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const BUILD_DIR = path.join(__dirname, 'build/server/index.js');

// Create and start the Remix server
const server = createServer(BUILD_DIR);
const port = process.env.PORT || 3000;

server.listen(port, () => {
  console.log(`Remix server started at http://localhost:${port}`);
});

// Handle uncaught exceptions to prevent the server from crashing
process.on('uncaughtException', (err) => {
  console.error('Uncaught exception:', err);
}); 