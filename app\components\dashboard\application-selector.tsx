import React, { useState, useMemo, useEffect } from 'react';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Checkbox } from '~/components/ui/checkbox';
import { ChevronRight, ChevronDown, MinusSquare, Square, CheckSquare } from 'lucide-react';
import type { ApplicationWithRagStatus } from '~/models/application.server';

interface ApplicationSelectorProps {
  applications: ApplicationWithRagStatus[];
  selectedApplicationIds: string[];
  onSelectionChange: (applicationIds: string[]) => void;
}

export function ApplicationSelector({
  applications,
  selectedApplicationIds,
  onSelectionChange,
}: ApplicationSelectorProps) {
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});
  
  // Initialize all groups as collapsed
  useEffect(() => {
    // We don't need to set any initial state since the default state is collapsed
    // (empty expandedGroups object means nothing is expanded)
    setExpandedGroups({});
  }, [applications]);

  // Group applications by orgLevel4 and orgLevel5
  const groupedApplications = useMemo(() => {
    return applications.reduce<Record<string, Record<string, ApplicationWithRagStatus[]>>>(
      (acc, app) => {
        const orgLevel4 = app.orgLevel4 || 'Uncategorized';
        const orgLevel5 = app.orgLevel5 || 'Other';
        
        if (!acc[orgLevel4]) {
          acc[orgLevel4] = {};
        }
        
        if (!acc[orgLevel4][orgLevel5]) {
          acc[orgLevel4][orgLevel5] = [];
        }
        
        acc[orgLevel4][orgLevel5].push(app);
        return acc;
      },
      {}
    );
  }, [applications]);

  // Toggle group expansion
  const toggleGroupExpand = (groupId: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  };

  // Handle application selection
  const handleToggleApplication = (appId: string) => {
    const isSelected = selectedApplicationIds.includes(appId);
    
    if (isSelected) {
      onSelectionChange(selectedApplicationIds.filter(id => id !== appId));
    } else {
      onSelectionChange([...selectedApplicationIds, appId]);
    }
  };

  // Handle group selection
  const handleToggleL4Group = (l4Group: string) => {
    const l4Apps: string[] = [];
    
    Object.keys(groupedApplications[l4Group]).forEach(l5Group => {
      groupedApplications[l4Group][l5Group].forEach(app => {
        l4Apps.push(app.applicationInstanceId);
      });
    });
    
    // Check if all apps in this group are selected
    const allSelected = l4Apps.every(id => selectedApplicationIds.includes(id));
    
    if (allSelected) {
      // Remove all apps in this group
      onSelectionChange(selectedApplicationIds.filter(id => !l4Apps.includes(id)));
    } else {
      // Add all apps that aren't already selected
      const newSelected = [...selectedApplicationIds];
      
      l4Apps.forEach(id => {
        if (!newSelected.includes(id)) {
          newSelected.push(id);
        }
      });
      
      onSelectionChange(newSelected);
    }
  };

  // Handle L5 group selection
  const handleToggleL5Group = (l4Group: string, l5Group: string) => {
    const l5Apps = groupedApplications[l4Group][l5Group].map(app => app.applicationInstanceId);
    
    // Check if all apps in this L5 group are selected
    const allSelected = l5Apps.every(id => selectedApplicationIds.includes(id));
    
    if (allSelected) {
      // Remove all apps in this group
      onSelectionChange(selectedApplicationIds.filter(id => !l5Apps.includes(id)));
    } else {
      // Add all apps that aren't already selected
      const newSelected = [...selectedApplicationIds];
      
      l5Apps.forEach(id => {
        if (!newSelected.includes(id)) {
          newSelected.push(id);
        }
      });
      
      onSelectionChange(newSelected);
    }
  };

  // Quick filters
  const handleClearSelection = () => {
    onSelectionChange([]);
  };

  const handleSelectAll = () => {
    onSelectionChange(applications.map(app => app.applicationInstanceId));
  };

  // Check if all apps in an L4 group are selected
  const isL4GroupSelected = (l4Group: string) => {
    const l4Apps: string[] = [];
    
    Object.keys(groupedApplications[l4Group]).forEach(l5Group => {
      groupedApplications[l4Group][l5Group].forEach(app => {
        l4Apps.push(app.applicationInstanceId);
      });
    });
    
    return l4Apps.length > 0 && l4Apps.every(id => selectedApplicationIds.includes(id));
  };

  // Check if all apps in an L5 group are selected
  const isL5GroupSelected = (l4Group: string, l5Group: string) => {
    const l5Apps = groupedApplications[l4Group][l5Group].map(app => app.applicationInstanceId);
    return l5Apps.length > 0 && l5Apps.every(id => selectedApplicationIds.includes(id));
  };

  // Check if some apps in an L4 group are selected
  const isSomeL4GroupSelected = (l4Group: string) => {
    const l4Apps: string[] = [];
    
    Object.keys(groupedApplications[l4Group]).forEach(l5Group => {
      groupedApplications[l4Group][l5Group].forEach(app => {
        l4Apps.push(app.applicationInstanceId);
      });
    });
    
    return l4Apps.some(id => selectedApplicationIds.includes(id)) && 
           !l4Apps.every(id => selectedApplicationIds.includes(id));
  };

  // Check if some apps in an L5 group are selected
  const isSomeL5GroupSelected = (l4Group: string, l5Group: string) => {
    const l5Apps = groupedApplications[l4Group][l5Group].map(app => app.applicationInstanceId);
    return l5Apps.some(id => selectedApplicationIds.includes(id)) && 
           !l5Apps.every(id => selectedApplicationIds.includes(id));
  };

  // Count applications in an L4 group
  const countL4Applications = (l4Group: string) => {
    let count = 0;
    Object.keys(groupedApplications[l4Group]).forEach(l5Group => {
      count += groupedApplications[l4Group][l5Group].length;
    });
    return count;
  };

  // Custom checkbox that displays the correct state (checked, unchecked, or indeterminate)
  const CustomCheckbox = ({ 
    checked = false, 
    indeterminate = false,
    onChange,
    className = "",
    ariaLabel
  }: { 
    checked?: boolean; 
    indeterminate?: boolean; 
    onChange: () => void;
    className?: string;
    ariaLabel?: string;
  }) => {
    return (
      <div className={`relative flex items-center justify-center ${className}`}>
        <Checkbox 
          checked={checked}
          onCheckedChange={onChange}
          className="opacity-0 absolute inset-0 cursor-pointer z-10"
          aria-label={ariaLabel}
        />
        <div className="h-4 w-4 flex items-center justify-center">
          {checked && !indeterminate && <CheckSquare className="h-4 w-4 text-primary" />}
          {!checked && !indeterminate && <Square className="h-4 w-4 text-muted-foreground" />}
          {indeterminate && <MinusSquare className="h-4 w-4 text-primary" />}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Selection Actions */}
      <div className="flex items-center gap-2 text-sm mb-2">
        <span className="text-muted-foreground mr-2">
          {selectedApplicationIds.length} of {applications.length} selected
        </span>
        <Button
          variant="outline"
          size="sm"
          className="h-7 px-2 text-xs"
          onClick={handleSelectAll}
        >
          Select All
        </Button>
        <Button
          variant="outline"
          size="sm"
          className="h-7 px-2 text-xs"
          onClick={handleClearSelection}
        >
          Clear
        </Button>
      </div>

      {/* Applications Tree View */}
      <div className="space-y-1 max-h-[500px] overflow-auto border rounded-md p-2">
        {Object.keys(groupedApplications).length === 0 ? (
          <div className="p-2 text-sm text-muted-foreground">No applications match your search.</div>
        ) : (
          <>
            {Object.keys(groupedApplications).sort().map(l4Group => (
              <div key={l4Group} className="space-y-1">
                {/* L4 Group Header */}
                <div 
                  className="flex items-center gap-2 p-1.5 rounded-sm hover:bg-accent cursor-pointer"
                  onClick={() => toggleGroupExpand(`l4-${l4Group}`)}
                >
                  <button className="h-5 w-5 shrink-0">
                    {expandedGroups[`l4-${l4Group}`] ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </button>
                  
                  <CustomCheckbox 
                    checked={isL4GroupSelected(l4Group)}
                    indeterminate={isSomeL4GroupSelected(l4Group)}
                    onChange={() => handleToggleL4Group(l4Group)}
                    className="mr-1"
                    ariaLabel={`Select all applications in ${l4Group}`}
                  />
                  
                  <span className="font-medium flex-1">{l4Group}</span>
                  
                  <Badge variant="outline" className="ml-auto">
                    {countL4Applications(l4Group)}
                  </Badge>
                </div>
                
                {/* L5 Groups */}
                {expandedGroups[`l4-${l4Group}`] && (
                  <div className="pl-8 space-y-1 mt-1">
                    {Object.keys(groupedApplications[l4Group]).sort().map(l5Group => (
                      <div key={`${l4Group}-${l5Group}`} className="space-y-1">
                        {/* L5 Group Header */}
                        <div 
                          className="flex items-center gap-2 p-1.5 rounded-sm hover:bg-accent cursor-pointer"
                          onClick={() => toggleGroupExpand(`l5-${l4Group}-${l5Group}`)}
                        >
                          <button className="h-5 w-5 shrink-0">
                            {expandedGroups[`l5-${l4Group}-${l5Group}`] ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                          </button>
                          
                          <CustomCheckbox 
                            checked={isL5GroupSelected(l4Group, l5Group)}
                            indeterminate={isSomeL5GroupSelected(l4Group, l5Group)}
                            onChange={() => handleToggleL5Group(l4Group, l5Group)}
                            className="mr-1"
                            ariaLabel={`Select all applications in ${l5Group}`}
                          />
                          
                          <span className="font-medium flex-1">{l5Group}</span>
                          
                          <Badge variant="outline" className="ml-auto">
                            {groupedApplications[l4Group][l5Group].length}
                          </Badge>
                        </div>
                        
                        {/* Applications List */}
                        {expandedGroups[`l5-${l4Group}-${l5Group}`] && (
                          <div className="pl-8 space-y-1 mt-1">
                            {groupedApplications[l4Group][l5Group].map(app => (
                              <div 
                                key={app.applicationInstanceId}
                                className="flex items-center gap-2 p-1.5 rounded-sm hover:bg-accent cursor-pointer"
                              >
                                <CustomCheckbox 
                                  checked={selectedApplicationIds.includes(app.applicationInstanceId)}
                                  onChange={() => handleToggleApplication(app.applicationInstanceId)}
                                  className="mr-1"
                                  ariaLabel={`Select ${app.name}`}
                                />
                                
                                <span className="flex-1 text-sm">{app.name}</span>
                                
                                <span className="text-xs text-muted-foreground">{app.applicationInstanceId}</span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </>
        )}
      </div>
    </div>
  );
} 