import {
  CheckCircledIcon,
  CrossCircledIcon,
  StopwatchIcon,
} from "@radix-ui/react-icons"

// UI Constants for Applications
export const APPLICATION_STATUS_OPTIONS = [
  {
    value: "active",
    label: "Active",
    icon: CheckCircledIcon,
    variant: "default" as const,
  },
  {
    value: "inactive",
    label: "Inactive",
    icon: CrossCircledIcon,
    variant: "secondary" as const,
  },
  {
    value: "pending",
    label: "Pending",
    icon: StopwatchIcon,
    variant: "outline" as const,
  },
] as const;

export const CRITICALITY_OPTIONS = [
  { value: "Tier 1", label: "Tier 1" },
  { value: "Tier 2", label: "Tier 2" },
  { value: "Tier 3", label: "Tier 3" },
] as const;

export const STRATEGIC_STATUS_OPTIONS = [
  { value: "Contain", label: "Contain" },
  { value: "Strategic", label: "Strategic" },
  { value: "Retire", label: "Retire" },
] as const;

export const DEPARTMENT_OPTIONS = [
  { value: "FTR", label: "FTR" },
  { value: "EQD", label: "EQD" },
  { value: "FIC", label: "FIC" },
] as const;

export const TEAM_OPTIONS = [
  { value: "SCP", label: "SCP" },
  { value: "ACC", label: "ACC" },
  { value: "PCP", label: "PCP" },
] as const; 