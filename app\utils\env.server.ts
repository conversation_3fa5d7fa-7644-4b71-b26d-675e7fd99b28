/**
 * Environment variable access utility for server-side code
 * This provides a consistent way to access environment variables
 * from both direct process.env and from the context passed by the server
 */

import { AppLoadContext } from '@remix-run/node';

// Define the expected shape of the env object in context
export interface EnvContext {
  env?: {
    HEET_API_URL?: string;
    DLAS_API_URL?: string;
    NODE_ENV?: string;
    DATABASE_URL?: string;
    API_RETRY_MAX_ATTEMPTS?: string;
    API_RETRY_INITIAL_DELAY?: string;
    API_RETRY_MAX_DELAY?: string;
    SYNC_BATCH_SIZE?: string;
    SYNC_CHUNK_SIZE?: string;
    SYNC_IN_QUERY_LIMIT?: string;
    SYNC_ENABLE_PARALLEL?: string;
    SYNC_ENABLE_MEMORY_MONITORING?: string;
    SCHEDULED_SYNC_TIME?: string;
    [key: string]: string | undefined;
  };
}

// Helper function to get environment variables from either source
export function getEnv(
  key: string, 
  context?: AppLoadContext,
  defaultValue: string = ''
): string {
  // Cast context to the expected type
  const typedContext = context as EnvContext | undefined;
  
  // First try from the load context if available
  if (typedContext?.env && typeof typedContext.env === 'object' && key in typedContext.env) {
    const value = typedContext.env[key];
    if (value) return String(value);
  }
  
  // Then try from process.env
  if (process.env[key]) {
    return process.env[key] as string;
  }
  
  // Finally return default if specified
  return defaultValue;
}

// Get environment with validation
export function getRequiredEnv(key: string, context?: AppLoadContext): string {
  const value = getEnv(key, context);
  if (!value) {
    throw new Error(`Required environment variable ${key} is not configured`);
  }
  return value;
}

// Common environment variables
export function getHeetApiUrl(context?: AppLoadContext): string {
  return getRequiredEnv('HEET_API_URL', context);
}

export function getDlasApiUrl(context?: AppLoadContext): string {
  return getRequiredEnv('DLAS_API_URL', context);
}

export function getDatabaseUrl(context?: AppLoadContext): string {
  return getRequiredEnv('DATABASE_URL', context);
}

export function isProduction(context?: AppLoadContext): boolean {
  return getEnv('NODE_ENV', context) === 'production';
}

export function isDevelopment(context?: AppLoadContext): boolean {
  return getEnv('NODE_ENV', context) === 'development';
}

// API Configuration
export function getApiRetryMaxAttempts(context?: AppLoadContext): number {
  return parseInt(getEnv('API_RETRY_MAX_ATTEMPTS', context, '3'), 10);
}

export function getApiRetryInitialDelay(context?: AppLoadContext): number {
  return parseInt(getEnv('API_RETRY_INITIAL_DELAY', context, '1000'), 10);
}

export function getApiRetryMaxDelay(context?: AppLoadContext): number {
  return parseInt(getEnv('API_RETRY_MAX_DELAY', context, '5000'), 10);
}

// Sync Configuration
export function getSyncBatchSize(context?: AppLoadContext): number {
  return parseInt(getEnv('SYNC_BATCH_SIZE', context, '100'), 10);
}

export function getSyncChunkSize(context?: AppLoadContext): number {
  return parseInt(getEnv('SYNC_CHUNK_SIZE', context, '500'), 10);
}

export function getSyncInQueryLimit(context?: AppLoadContext): number {
  return parseInt(getEnv('SYNC_IN_QUERY_LIMIT', context, '1000'), 10);
}

export function getSyncEnableParallel(context?: AppLoadContext): boolean {
  return getEnv('SYNC_ENABLE_PARALLEL', context, 'false').toLowerCase() === 'true';
}

export function getSyncEnableMemoryMonitoring(context?: AppLoadContext): boolean {
  return getEnv('SYNC_ENABLE_MEMORY_MONITORING', context, 'false').toLowerCase() === 'true';
}

// Sync Time Configuration
export function getScheduledSyncTime(context?: AppLoadContext): string {
  return getEnv('SCHEDULED_SYNC_TIME', context, '06:00');
}

export function getScheduledSyncHour(context?: AppLoadContext): number {
  const timeString = getScheduledSyncTime(context);
  const [hours] = timeString.split(':');
  return parseInt(hours, 10);
}

export function getScheduledSyncMinute(context?: AppLoadContext): number {
  const timeString = getScheduledSyncTime(context);
  const parts = timeString.split(':');
  const minutes = parts.length > 1 ? parts[1] : '0';
  return parseInt(minutes, 10);
} 