---
description: 
globs: 
alwaysApply: false
---
# Development Guidelines

## Code Style

### TypeScript
- Use TypeScript for all new code
- Define explicit types and interfaces in `app/types/`
- Avoid using `any` type
- Use optional chaining (`?.`) and nullish coalescing (`??`)

### Components
- Place reusable components in `app/components/`
- Use functional components with hooks
- Props should be explicitly typed
- Follow component naming pattern: `ComponentName.tsx`

### Data Fetching
- Use Remix loaders for server-side data fetching
- Place API services in `app/services/`
- Handle errors with try/catch blocks
- Use Drizzle ORM for database operations

### Styling
- Use Tailwind CSS for styling
- Follow mobile-first responsive design
- Keep styles modular and reusable
- Use CSS modules for component-specific styles

### State Management
- Use React Context for global state
- Keep state close to where it's used
- Prefer controlled components
- Use `useState` and `useReducer` appropriately

### Testing
- Write unit tests for critical functionality
- Test components using React Testing Library
- Mock external dependencies
- Ensure good test coverage

### Performance
- Optimize bundle size
- Use code splitting where appropriate
- Implement proper caching strategies
- Monitor and optimize API calls

### Security
- Validate all user inputs
- Sanitize data before rendering
- Follow security best practices
- Keep sensitive data server-side

