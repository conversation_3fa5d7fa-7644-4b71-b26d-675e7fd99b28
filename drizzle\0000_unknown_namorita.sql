CREATE TABLE `applications` (
	`application_instance_id` text PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`short_name` text NOT NULL,
	`description` text,
	`criticality` text NOT NULL,
	`status` text NOT NULL,
	`strategic_status` text NOT NULL,
	`org_level2` text NOT NULL,
	`org_level3` text NOT NULL,
	`org_level4` text NOT NULL,
	`org_level5` text NOT NULL,
	`plada_service_id` text NOT NULL,
	`plada_service_name` text NOT NULL,
	`owner_psid` text,
	`owner_display_name` text,
	`owner_email` text,
	`delegate_psid` text,
	`delegate_display_name` text,
	`delegate_email` text,
	`created_at` integer DEFAULT CURRENT_TIMESTAMP,
	`updated_at` integer DEFAULT CURRENT_TIMESTAMP
);
--> statement-breakpoint
CREATE INDEX `status_idx` ON `applications` (`status`);--> statement-breakpoint
CREATE INDEX `org_idx` ON `applications` (`org_level4`,`org_level5`);--> statement-breakpoint
CREATE INDEX `plada_service_idx` ON `applications` (`plada_service_id`);--> statement-breakpoint
CREATE TABLE `interfaces` (
	`oms_interface_id` text PRIMARY KEY NOT NULL,
	`status` text NOT NULL,
	`direction` text NOT NULL,
	`eim_interface_id` text,
	`interface_name` text NOT NULL,
	`send_app_id` text NOT NULL,
	`send_app_name` text NOT NULL,
	`received_app_id` text NOT NULL,
	`received_app_name` text NOT NULL,
	`transfer_type` text NOT NULL,
	`frequency` text NOT NULL,
	`technology` text NOT NULL,
	`pattern` text NOT NULL,
	`related_drilldown_key` integer NOT NULL,
	`related_dataset_list` text NOT NULL,
	`demise_date` text,
	`last_sync_time` integer,
	`created_at` integer DEFAULT CURRENT_TIMESTAMP,
	`updated_at` integer DEFAULT CURRENT_TIMESTAMP
);
--> statement-breakpoint
CREATE INDEX `send_app_idx` ON `interfaces` (`send_app_id`);--> statement-breakpoint
CREATE INDEX `received_app_idx` ON `interfaces` (`received_app_id`);--> statement-breakpoint
CREATE INDEX `interface_name_idx` ON `interfaces` (`interface_name`);--> statement-breakpoint
CREATE TABLE `datasets` (
	`dataset_name` text PRIMARY KEY NOT NULL,
	`status` text NOT NULL,
	`direction` text NOT NULL,
	`eim_interface_id` text,
	`dataset_status` text NOT NULL,
	`description` text,
	`interface_serial` integer NOT NULL,
	`oms_interface_id` text,
	`transfer_type` text NOT NULL,
	`frequency` text NOT NULL,
	`primary_data_term` text,
	`product_type` text NOT NULL,
	`related_drilldown_list` text,
	`sla` text,
	`last_arrival_time` integer,
	`created_at` integer DEFAULT CURRENT_TIMESTAMP,
	`updated_at` integer DEFAULT CURRENT_TIMESTAMP,
	FOREIGN KEY (`oms_interface_id`) REFERENCES `interfaces`(`oms_interface_id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE INDEX `datasets_oms_interface_id_idx` ON `datasets` (`oms_interface_id`);--> statement-breakpoint
CREATE INDEX `datasets_interface_serial_idx` ON `datasets` (`interface_serial`);--> statement-breakpoint
CREATE INDEX `datasets_status_idx` ON `datasets` (`status`);--> statement-breakpoint
CREATE TABLE `events` (
	`msg_id` text PRIMARY KEY NOT NULL,
	`business_date` text NOT NULL,
	`created_date_time` integer NOT NULL,
	`dataset_name` text NOT NULL,
	`end_node_id` text NOT NULL,
	`end_node_name` text NOT NULL,
	`frequency` text NOT NULL,
	`raw_json` text NOT NULL,
	`start_node_id` text NOT NULL,
	`start_node_name` text NOT NULL,
	`valid` text(1) NOT NULL,
	`created_at` integer DEFAULT CURRENT_TIMESTAMP NOT NULL
);
