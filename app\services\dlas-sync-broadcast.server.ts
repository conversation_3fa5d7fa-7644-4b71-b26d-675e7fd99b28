import { Logger } from '~/utils/sync-utils.server';
import { type DLASSyncProgress } from './dlas-sync-progress.server';
import { sendSyncUpdateToClients } from '../routes/api.dlas-sync-events';

/**
 * Simplified broadcaster for DLAS sync state changes
 */
export class DLASSyncBroadcaster {
  private logger: Logger;
  
  constructor() {
    this.logger = new Logger('DLAS Broadcast');
  }
  
  /**
   * Broadcast a sync status update to connected clients
   */
  public broadcastSyncStatus(progress: DLASSyncProgress): void {
    try {
      // Convert Date objects to ISO strings for JSON serialization
      const data = {
        ...progress,
        startTime: progress.startTime ? progress.startTime.toISOString() : null,
        endTime: progress.endTime ? progress.endTime.toISOString() : null
      };
      sendSyncUpdateToClients({
        type: 'syncStatus',
        data
      });
    } catch (error) {
      this.logger.error('Failed to broadcast update', error);
    }
  }
} 