import { useEffect, useRef } from "react";
import { usePageTitle } from "~/contexts/page-title-context";

// Global state to track cleanup operations across all hook instances
type CleanupState = {
  pending: boolean;
  timeoutId: number | null;
};

const globalCleanupState: CleanupState = {
  pending: false,
  timeoutId: null,
};

/**
 * Hook to set the page title in the header
 * 
 * @param title - The page title to display
 * @param description - Optional description to display below the title
 */
export function useSetPageTitle(title: string, description?: string | null) {
  const { setPageTitle } = usePageTitle();
  const isMounted = useRef(true);
  const updateScheduled = useRef(false);
  
  useEffect(() => {
    // Function to safely update title with proper timing
    const safelyUpdateTitle = () => {
      // Only proceed if mounted and no cleanup is in progress
      if (!isMounted.current || globalCleanupState.pending) {
        updateScheduled.current = false;
        return;
      }
      
      try {
        setPageTitle(title, description || null);
      } catch (error) {
        console.warn('Error setting page title:', error);
      }
      
      updateScheduled.current = false;
    };
    
    // Schedule the title update using requestAnimationFrame for better timing
    if (!updateScheduled.current) {
      updateScheduled.current = true;
      requestAnimationFrame(safelyUpdateTitle);
    }
    
    // Cleanup function runs when component unmounts or dependencies change
    return () => {
      isMounted.current = false;
      
      // Set cleanup flag to prevent concurrent updates
      globalCleanupState.pending = true;
      
      // Clear any existing timeout to prevent overlapping timers
      if (globalCleanupState.timeoutId !== null) {
        clearTimeout(globalCleanupState.timeoutId);
      }
      
      // Reset the cleanup flag after a safe delay
      globalCleanupState.timeoutId = window.setTimeout(() => {
        globalCleanupState.pending = false;
        globalCleanupState.timeoutId = null;
      }, 150) as unknown as number;
    };
  }, [title, description, setPageTitle]);
} 