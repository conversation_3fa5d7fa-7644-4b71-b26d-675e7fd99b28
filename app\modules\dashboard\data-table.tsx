"use client"

import { DataTable } from "~/components/ui/data-table/data-table"
import { getColumnsWithCallbacks, type DashboardApplication } from "./columns"
import { useNavigate } from "@remix-run/react"

/**
 * Props for the DashboardApplicationsDataTable component
 */
interface DashboardApplicationsDataTableProps {
  data: DashboardApplication[]
  isLoading?: boolean
  onNavigateToInterfaces?: (applicationId: string) => void
}

export function DashboardApplicationsDataTable({ 
  data, 
  isLoading = false,
  onNavigateToInterfaces
}: DashboardApplicationsDataTableProps) {
  const navigate = useNavigate()

  // Function to handle viewing application interfaces
  const handleViewInterfaces = (applicationId: string) => {
    if (onNavigateToInterfaces) {
      onNavigateToInterfaces(applicationId)
    } else {
      // Default behavior - navigate to interfaces page with the application ID as query
      navigate(`/interfaces?query=${applicationId}`)
    }
  }

  // Get columns with the view interfaces callback
  const columns = getColumnsWithCallbacks(handleViewInterfaces)

  return (
    <div className="space-y-4">
      <div className="relative overflow-x-auto">
        {isLoading && (
          <div className="absolute inset-0 bg-background/50 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}
        <DataTable
          columns={columns}
          data={data}
          facetedFilters={[
            {
              column: "status",
              title: "Status",
              options: [
                { label: "Red (SLA Breached)", value: "red" },
                { label: "Amber (SLA At Risk)", value: "amber" },
                { label: "Green (On Schedule)", value: "green" },
              ],
            },
          ]}
          defaultSort={[{ id: "id", desc: false }]}
        />
      </div>
    </div>
  )
} 