import { json, type ActionFunction } from "@remix-run/node";
import { ApplicationModel } from "~/models/application.server";
import { InterfaceModel } from "~/models/interface.server";
import { handleApiError } from "~/utils/error-handling.server";

export const action: ActionFunction = async ({ request }) => {
  if (request.method !== "POST") {
    return json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    const formData = await request.formData();
    const pladaServiceIds = formData.getAll("pladaServiceIds[]").map(id => String(id));
    const orgLevel4 = formData.get("orgLevel4")?.toString() || undefined;
    const orgLevel5 = formData.get("orgLevel5")?.toString() || undefined;
    
    if (!pladaServiceIds.length) {
      return json(
        { success: false, error: "No applications selected" },
        { status: 400 }
      );
    }

    // Prepare filter options
    const options = {
      orgLevel4: orgLevel4 !== "all" ? orgLevel4 : undefined,
      orgLevel5: orgLevel5 !== "all" ? orgLevel5 : undefined,
      pladaServiceIds: pladaServiceIds.length > 0 ? pladaServiceIds : undefined,
    };

    console.time('Sync applications and interfaces');
      
    // Sync applications from HEET
    await ApplicationModel.syncFromHeet(options);
    // Get the updated list of applications
    const syncedApps = await ApplicationModel.findAll(options);
    
    // Then sync interfaces for each application
    await Promise.all(
      syncedApps.map(app => 
        InterfaceModel.syncFromDLAS(app.applicationInstanceId, {
          orgLevel4: options.orgLevel4,
          orgLevel5: options.orgLevel5,
        })
      )
    );

    console.timeEnd('Sync applications and interfaces');
    return json({ success: true });
  } catch (error) {
    return handleApiError(error);
  }
}; 