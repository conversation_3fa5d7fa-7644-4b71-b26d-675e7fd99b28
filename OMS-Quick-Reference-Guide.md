# OMS v3 Quick Reference Guide

![OMS Dashboard](https://placeholder.for/dashboard-screenshot)

## What is OMS v3?

OMS v3 is a tool that helps you monitor and keep track of your organization's applications, interfaces (connections between applications), and datasets. It lets you see status information, track performance, and filter information based on your organization's structure.

## Main Features

### Dashboard
- See counts of applications, interfaces, and datasets
- View status of interfaces using color indicators (green, yellow, red)
- Filter information by organization level

### Interfaces
- See all connections between your applications
- Filter by name, status, or organization
- Check status (on schedule, at risk, or delayed)
- View datasets connected to each interface
- Sync interface data with connected systems

### Applications
- View all applications in your system
- Filter by name or organization
- See which interfaces connect to each application
- Sync application data with external systems

### Datasets
- See data collections associated with interfaces
- View status and timing information
- Configure status indicators (green = on schedule, yellow = at risk, red = delayed)

### Quick Access
- Find recently viewed items
- Pin important items to keep them easily accessible
- See when you last accessed each item

### Settings
- Change how many items show in Quick Access (1-20)
- Set how often data syncs automatically (1-60 minutes)

## How to Use

### Viewing Interfaces

1. Click "Interfaces" in the sidebar
2. Use the search box to find specific interfaces (try searching by name or ID)
3. Use filters to narrow down the list by organization level
4. Click on any interface to see its details

![Interface List](https://placeholder.for/interface-list-screenshot)

### Checking Interface Details

1. From the interfaces list, click on an interface
2. View basic information on the Overview tab
3. See connected datasets on the Datasets tab
4. Review events on the Events tab

For example, you might check an interface to see if data is flowing properly between two systems or to troubleshoot delays.

![Interface Details](https://placeholder.for/interface-details-screenshot)

### Syncing Interface Data

1. Open the interface details page
2. Click the "Sync" button in the top-right corner
3. Wait for the sync to complete
4. The status will update when finished

Syncing updates information from connected systems to ensure you're seeing the latest status and data.

### Viewing Applications

1. Click "Applications" in the sidebar
2. Use the search box to find specific applications (try application name, ID, or service ID)
3. Filter by organization level if needed
4. Click on any application to see details

![Application List](https://placeholder.for/application-list-screenshot)

### Working with Datasets

1. Navigate to an interface
2. Click the "Datasets" tab
3. View all datasets connected to this interface
4. Click on a dataset to see its details and events

Datasets show you what information is being transferred between applications and when it was last updated.

### Using Quick Access

1. Find the Quick Access panel on the right side
2. Click on any item to go directly to it
3. Hover over an item and click the pin icon to keep it at the top
4. Items you view frequently will appear automatically

Quick Access helps you jump directly to interfaces or applications you use often.

![Quick Access](https://placeholder.for/quick-access-screenshot)

### Changing Settings

1. Click "Settings" in the sidebar
2. Adjust the maximum number of Quick Access items (1-20)
3. Set how often data syncs automatically (1-60 minutes)
4. Click "Save Changes" when done

![Settings](https://placeholder.for/settings-screenshot)

## Common Tasks

### Finding an Interface

1. Go to the Interfaces page
2. Type part of the name in the search box
3. Press Enter to search
4. Or use filters to narrow down by organization

Tip: If you don't know the exact name, try searching for the source or target application name.

### Checking Interface Status

Look for the color-coded status indicators:
- Green: On schedule - Everything is working as expected
- Yellow: At risk - May be running behind schedule
- Red: Delayed - SLA has been breached, needs attention

### Filtering by Organization

1. In any list view (Interfaces or Applications)
2. Look for the organization filters (Level 4/Level 5)
3. Select the organizational units you want to view
4. The list will update to show only matching items

This is helpful when you want to focus on a specific department or team's applications.

## Troubleshooting

### Data Not Updating
- Check your internet connection
- Click the refresh/sync button manually
- Verify in Settings that auto-sync is enabled

### Can't Find an Item
- Try different search terms (ID, name, or partial matches)
- Clear any active filters (look for an X button)
- Check if you're viewing the correct organization level

### Slow Performance
- Try reducing the date range of your view
- Update your auto-sync interval in Settings to be less frequent

---

*This quick reference guide is for OMS v3 (Operations Management System).* 