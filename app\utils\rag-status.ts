interface RagCounts {
  red: number;
  amber: number;
  green: number;
}

export function deriveRagStatus(counts?: RagCounts): "red" | "amber" | "green" {
  if (!counts) return "green";
  if (counts.red > 0) return "red";
  if (counts.amber > 0) return "amber";
  return "green";
}

export function getStatusTooltip(status: string, type: 'Application' | 'Interface'): string {
  const prefix = type === 'Application' ? 'Application' : 'Interface';
  switch (status) {
    case "red": return `${prefix} has SLA breached interfaces`;
    case "amber": return `${prefix} has interfaces at risk of breaching SLA`;
    case "green": return `All ${prefix.toLowerCase()} interfaces are on schedule`;
    default: return "Unknown Status";
  }
}

export function isValidRagCounts(obj: any): obj is RagCounts {
  return (
    obj &&
    typeof obj === 'object' &&
    'red' in obj && typeof obj.red === 'number' &&
    'amber' in obj && typeof obj.amber === 'number' &&
    'green' in obj && typeof obj.green === 'number'
  );
} 