import { json } from "@remix-run/node";
import type { MetaFunction, LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { InterfaceModel } from "~/models/interface.server";
import { ApplicationModel } from "~/models/application.server";
import { DatasetModel } from "~/models/dataset.server";
import { deriveRagStatus } from "~/hooks/use-sync-interfaces";
import { QuickAccess } from "~/components/quick-access";
import { StatsCard } from "~/components/dashboard/stats-card";
import { InterfaceStatus } from "~/components/dashboard/interface-status";
import { InterfaceChart } from "~/components/dashboard/interface-chart";
import { ArrowRight, PlusCircle, Settings, Trash2, Pencil, ChevronDown, Check } from "lucide-react";
import { Link } from "@remix-run/react";
import type { ApplicationWithRagStatus } from "~/models/application.server";
import { useRootLoaderData } from "~/hooks/use-root-loader-data";
import { useSetPageTitle } from "~/hooks/use-set-page-title";
import { useCustomDashboards } from "~/hooks/use-custom-dashboards";
import { Button } from "~/components/ui/button";
import { useState, useRef, useEffect } from "react";
import { Input } from "~/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogFooter,
  DialogTrigger
} from "~/components/ui/dialog";
import { CustomDashboardManager } from "~/components/dashboard/custom-dashboard-manager";
import { DashboardApplicationsDataTable } from "~/modules/dashboard/data-table";
import type { DashboardApplication } from "~/modules/dashboard/columns";

export const meta: MetaFunction = () => {
  return [
    { title: "Dashboard - OMS v3" },
    { 
      name: "description",
      content: "Operations Management System Dashboard",
    },
    {
      name: "viewport",
      content: "width=device-width,initial-scale=1",
    },
  ];
};

export type DashboardLoaderData = {
  applications: ApplicationWithRagStatus[];
  stats: {
    totalApplications: number;
    totalInterfaces: number;
    totalDatasets: number;
  };
  interfaceStats: {
    red: number;
    amber: number;
    green: number;
    total: number;
  };
  success: boolean;
  error?: string;
}

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Get all applications
    const allApplications = await ApplicationModel.findAll();
    const enhancedApplications = await ApplicationModel.enhanceWithInterfaceRagStatus(allApplications);

    // Get all interfaces and datasets
    const interfaces = await InterfaceModel.findAll({});
    const totalDatasets = await DatasetModel.countByOrgFilters({});

    // Calculate interface RAG status
    const interfaceIds = interfaces.map(interface_ => interface_.omsInterfaceId);
    const ragStatuses = await InterfaceModel.calculateBulkRagStatus(interfaceIds);
    
    const interfaceStats = {
      red: 0,
      amber: 0,
      green: 0,
      total: interfaces.length
    };

    interfaces.forEach(interface_ => {
      const ragStatus = ragStatuses[interface_.omsInterfaceId];
      if (ragStatus) {
        const status = deriveRagStatus(ragStatus.counts);
        interfaceStats[status]++;
      } else {
        interfaceStats.green++;
      }
    });

    return json({ 
      applications: enhancedApplications,
      stats: {
        totalApplications: allApplications.length,
        totalInterfaces: interfaces.length,
        totalDatasets
      },
      interfaceStats,
      success: true
    });
  } catch (error) {
    console.error("Error loading dashboard data:", error);
    return json({ 
      applications: [],
      stats: {
        totalApplications: 0,
        totalInterfaces: 0,
        totalDatasets: 0
      },
      interfaceStats: {
        red: 0,
        amber: 0,
        green: 0,
        total: 0
      },
      error: error instanceof Error ? error.message : "Unknown error",
      success: false 
    });
  }
}

export default function Dashboard() {
  const cleanupRef = useRef<() => void>(); 
  
  useEffect(() => {
    // This creates a cleanup function to handle any remaining state
    // when the component is about to unmount
    return () => {
      // Execute any cleanup logic here
      if (cleanupRef.current) {
        cleanupRef.current();
      }
      
      // Force any pending micro-tasks to flush before unmounting
      // This helps prevent React DOM manipulation after unmount
      setTimeout(() => {}, 0);
    };
  }, []);
  
  useSetPageTitle("Overview", "Dashboard monitoring system status");
  const navigate = useNavigate();

  const { applications, stats, interfaceStats, success, error } = useLoaderData<typeof loader>() as DashboardLoaderData;
  const { organizations } = useRootLoaderData();
  
  const {
    dashboards,
    activeDashboardId,
    createDashboard,
    updateDashboard,
    deleteDashboard,
    setActiveDashboard,
    getActiveDashboard
  } = useCustomDashboards();

  // Get the active dashboard
  const activeDashboard = getActiveDashboard();

  // Safely access dashboard and filter applications
  const getFilteredApplications = () => {
    if (!activeDashboard || !activeDashboard.applicationIds) {
      return applications;
    }
    
    try {
      return applications.filter(app => 
        activeDashboard.applicationIds.includes(app.applicationInstanceId));
    } catch (error) {
      console.error("Error filtering applications:", error);
      return applications;
    }
  };

  // Filter applications based on active dashboard
  const filteredApplications = getFilteredApplications();

  // Use a proportion-based approach for filtered stats
  const applicationRatio = applications.length > 0 
    ? filteredApplications.length / applications.length 
    : 0;
    
  // Adjust stats based on the proportion of applications selected
  const filteredStats = {
    totalApplications: filteredApplications.length,
    // Make it clear these are estimates but keep as numbers for type compatibility
    totalInterfaces: Math.round(stats.totalInterfaces * applicationRatio),
    totalDatasets: Math.round(stats.totalDatasets * applicationRatio)
  };

  // Simple proportion-based filtering for interface stats
  const filteredInterfaceStats = {
    red: Math.round(interfaceStats.red * applicationRatio) || 0,
    amber: Math.round(interfaceStats.amber * applicationRatio) || 0,
    green: Math.round(interfaceStats.green * applicationRatio) || 0,
    total: Math.round(interfaceStats.total * applicationRatio) || 0
  };

  // Format applications for the dashboard table
  const dashboardApplications: DashboardApplication[] = filteredApplications.map(app => ({
    id: app.applicationInstanceId,
    name: app.name,
    status: {
      red: app.interfaceRagStatus?.red || 0,
      amber: app.interfaceRagStatus?.amber || 0,
      green: app.interfaceRagStatus?.green || 0
    },
    interfaceCount: app.interfaceRagStatus ? 
      (app.interfaceRagStatus.red + app.interfaceRagStatus.amber + app.interfaceRagStatus.green) : 0
  }));

  // Handle navigating to interfaces
  const handleNavigateToInterfaces = (applicationId: string) => {
    navigate(`/interfaces?query=${applicationId}`);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Dashboard Header */}
      <div className="border-b p-4 bg-background">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl font-bold">
              {activeDashboard ? activeDashboard.name : 'All Applications'}
            </h1>
          </div>
          <div className="flex items-center gap-2">
            <CustomDashboardManager 
              key={`dashboard-manager-${activeDashboardId || 'none'}`}
              dashboards={dashboards}
              activeDashboardId={activeDashboardId}
              onCreateDashboard={createDashboard}
              onUpdateDashboard={updateDashboard}
              onDeleteDashboard={deleteDashboard}
              onSelectDashboard={setActiveDashboard}
              onEditDashboard={(id) => navigate(`/custom-dashboards?edit=${id}`)}
              compact={true}
            />
          </div>
        </div>
      </div>

      {/* Main Dashboard Content */}
      <div className="flex-1 space-y-8 p-6 pt-6 overflow-auto">
        {/* Error message if data fetch failed */}
        {!success && (
          <div className="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
            <span className="font-medium">Error!</span> {error || "Failed to load dashboard data."}
          </div>
        )}
        
        {/* Overview Section */}
        <div>
          <div className="grid gap-4 md:grid-cols-3">
            <StatsCard
              title="Applications"
              value={filteredStats.totalApplications}
              description="Total monitored applications"
              icon="application"
            />
            <StatsCard
              title="Interfaces"
              value={filteredStats.totalInterfaces}
              description="Active system interfaces (estimated)"
              icon="interface"
            />
            <StatsCard
              title="Datasets"
              value={filteredStats.totalDatasets}
              description="Managed data collections (estimated)"
              icon="dataset"
            />
          </div>
        </div>
        
        {/* Applications Section */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold tracking-tight">Applications</h2>
            <Link 
              to="/applications" 
              className="text-primary text-sm flex items-center hover:underline"
            >
              View All Applications <ArrowRight className="h-4 w-4 ml-1" />
            </Link>
          </div>
          
          <DashboardApplicationsDataTable 
            data={dashboardApplications}
            onNavigateToInterfaces={handleNavigateToInterfaces}
          />
        </div>
        
        {/* Interface Status Section */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold tracking-tight">Interface Status</h2>
            <Link 
              to="/interfaces" 
              className="text-primary text-sm flex items-center hover:underline"
            >
              View All Interfaces <ArrowRight className="h-4 w-4 ml-1" />
            </Link>
          </div>
          
          <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            <InterfaceStatus stats={filteredInterfaceStats} />
            <div className="col-span-full md:col-span-1 lg:col-span-2">
              <InterfaceChart stats={filteredInterfaceStats} />
            </div>
          </div>
        </div>
        
        {/* Quick Access Section 
        <QuickAccess />*/}
      </div>
    </div>
  );
} 