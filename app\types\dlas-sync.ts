// Define DLAS Sync State constants
export const DLASSyncState = {
  IDLE: 'idle',
  RUNNING: 'running',
  ERROR: 'error',
} as const;

export type DLASSyncStateType = typeof DLASSyncState[keyof typeof DLASSyncState];

// Type for sync errors
export interface SyncError {
  message: string;
  item?: string;
}

// Type for sync progress information
export interface SyncProgress {
  // Application level tracking
  totalItems: number;
  processedItems: number;
  successCount: number;
  errorCount: number;
  errors: SyncError[];
  
  // Interface level tracking
  totalInterfaces: number;
  processedInterfaces: number;
  successInterfaces: number;
  
  // Dataset level tracking
  totalDatasets: number;
  processedDatasets: number;
  successDatasets: number;
  
  // Timing information
  startTime: string | null;
  endTime: string | null;
  elapsedTimeMs: number;
}

// Type for the overall sync status
export interface SyncStatus {
  state: DLASSyncStateType | string;
  progress: SyncProgress;
}

// Scheduled sync information
export interface ScheduledSyncInfo {
  hour: number;
  minute: number;
  description: string;
}

// Props for the DLAS Sync Monitor component
export interface DLASSyncMonitorProps {
  initialStatus?: SyncStatus;
  refreshInterval?: number;
} 