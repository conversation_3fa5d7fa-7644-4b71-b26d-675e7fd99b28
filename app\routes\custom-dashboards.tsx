import { useState, useEffect, useRef } from 'react';
import { Link, useSearchParams, Form } from '@remix-run/react';
import { Button } from '~/components/ui/button';
import { Card } from '~/components/ui/card';
import { useCustomDashboards } from '~/hooks/use-custom-dashboards';
import { useLoaderData } from '@remix-run/react';
import { json } from '@remix-run/node';
import type { LoaderFunction } from '@remix-run/node';
import { ApplicationModel } from '~/models/application.server';
import type { ApplicationWithRagStatus } from '~/models/application.server';
import { useSetPageTitle } from '~/hooks/use-set-page-title';
import { CustomDashboardManager } from '~/components/dashboard/custom-dashboard-manager';
import { ApplicationSelector } from '~/components/dashboard/application-selector';
import { CommonSearchForm } from '~/components/common-search-form';

export const loader: LoaderFunction = async ({ request }) => {
  const url = new URL(request.url);
  const query = url.searchParams.get('query') || '';
  
  // Get all applications (with optional query filter)
  const allApplications = await ApplicationModel.findAll({ query });
  const enhancedApplications = await ApplicationModel.enhanceWithInterfaceRagStatus(allApplications);

  return json({ 
    applications: enhancedApplications,
    query,
  });
};

export default function CustomDashboards() {
  const cleanupRef = useRef<() => void>();
  
  useEffect(() => {
    // This creates a cleanup function to handle any remaining state
    // when the component is about to unmount
    return () => {
      // Execute any cleanup logic here
      if (cleanupRef.current) {
        cleanupRef.current();
      }
      
      // Force any pending micro-tasks to flush before unmounting
      // This helps prevent React DOM manipulation after unmount
      setTimeout(() => {}, 0);
    };
  }, []);
  
  useSetPageTitle("Custom Dashboards", "Create and manage custom dashboards");
  
  const { applications, query } = useLoaderData<{ 
    applications: ApplicationWithRagStatus[],
    query: string
  }>();
  const {
    dashboards,
    activeDashboardId,
    createDashboard,
    updateDashboard,
    deleteDashboard,
    setActiveDashboard,
  } = useCustomDashboards();

  const [selectedDashboardId, setSelectedDashboardId] = useState<string | null>(null);
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchValue, setSearchValue] = useState(query || '');
  const formRef = useRef<HTMLFormElement>(null);
  
  // Check for edit parameter in URL
  useEffect(() => {
    const editParam = searchParams.get('edit');
    if (editParam) {
      const dashboard = dashboards.find(d => d.id === editParam);
      if (dashboard) {
        setSelectedDashboardId(editParam);
      }
    }
  }, [searchParams, dashboards]);
  
  // Find the selected dashboard
  const selectedDashboard = selectedDashboardId 
    ? dashboards.find(d => d.id === selectedDashboardId) 
    : null;

  // Handle editing a dashboard (update URL with edit parameter)
  const handleEditDashboard = (id: string) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set('edit', id);
    setSearchParams(newParams);
    setSelectedDashboardId(id);
  };

  // Handle updating a dashboard's applications
  const handleUpdateDashboardApplications = (applicationIds: string[]) => {
    if (selectedDashboard) {
      updateDashboard(selectedDashboard.id, { applicationIds });
    }
  };

  // Handle search form submission
  const handleSearchSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    const query = formData.get('query')?.toString() || '';
    
    const newParams = new URLSearchParams(searchParams);
    if (query) {
      newParams.set('query', query);
    } else {
      newParams.delete('query');
    }
    
    setSearchParams(newParams);
  };

  // Handle search clear
  const handleClearSearch = () => {
    const newParams = new URLSearchParams(searchParams);
    newParams.delete('query');
    setSearchParams(newParams);
    setSearchValue('');
  };

  // Check if any filters are active
  const hasFilters = searchParams.has('query');

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Custom Dashboards</h1>
        <Button asChild variant="outline">
          <Link to="/dashboard">Back to Main Dashboard</Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Dashboard Management */}
        <Card className="p-6 h-fit col-span-1">
          <CustomDashboardManager
            dashboards={dashboards}
            applications={applications}
            activeDashboardId={activeDashboardId}
            onCreateDashboard={createDashboard}
            onUpdateDashboard={updateDashboard}
            onDeleteDashboard={deleteDashboard}
            onSelectDashboard={setSelectedDashboardId}
            onEditDashboard={handleEditDashboard}
          />
        </Card>

        {/* Right Column - Application Selector */}
        <Card className="col-span-1 lg:col-span-2 p-6">
          {selectedDashboard ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Select Applications for "{selectedDashboard.name}"</h2>
                <div className="text-sm text-muted-foreground">
                  {selectedDashboard.applicationIds.length} applications selected
                </div>
              </div>

                {/* Search Form */}
              <CommonSearchForm
                searchValue={searchValue}
                onSearchChange={setSearchValue}
                hasFilters={hasFilters}
                onClearFilters={handleClearSearch}
                formRef={formRef}
                onSubmit={handleSearchSubmit}
                placeholder="Search applications by ID or name..."
                showActiveFilters={true}
                searchParams={searchParams}
                setSearchParams={setSearchParams}
              />

              <ApplicationSelector
                applications={applications}
                selectedApplicationIds={selectedDashboard.applicationIds}
                onSelectionChange={handleUpdateDashboardApplications}
              />
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-[400px] text-center">
              <h2 className="text-xl font-semibold mb-2">Select or create a dashboard to get started</h2>
              <p className="text-muted-foreground max-w-md">
                Create a custom dashboard to monitor specific applications. You can create multiple dashboards for different monitoring needs.
              </p>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
} 