import { interfaces } from '../schema/interfaces';
import type { DLASDatasetResponse } from './datasets';

// Database Types
export type Interface = typeof interfaces.$inferSelect;
export type NewInterface = typeof interfaces.$inferInsert;

// Sync Status Definition
export const SyncStatus = {
  SUCCESS: 'success',
  ERROR: 'error',
  PENDING: 'pending',
} as const;

export type SyncStatus = typeof SyncStatus[keyof typeof SyncStatus];

// API Response Types
export interface DLASInterfaceResponse {
  appid: string;
  dataDate: string;
  interface: {
    interface_dlas_logged: DLASInterface[];
  };
  dataset: DLASDatasetResponse;
}

export interface DLASInterface {
  Status: string;
  Direction: string;
  EIMInterfaceID: string | null;
  InterfaceName: string;
  SendAppID: string;
  SendAppName: string;
  ReceivedAppID: string;
  ReceivedAppName: string;
  TransferType: string;
  Frequency: string;
  Technology: string;
  Pattern: string;
  RelatedDrilldownKey: number;
  RelatedDatasetList: {
    logged_dataset: string[];
  };
  DemiseDate: string | null;
}

// Helper type for interface status
export const InterfaceStatus = {
  EIM_UPDATED: 'EIM UPDATED',
  DLAS_ONLY: 'DLAS ONLY',
  DLAS_EIM_MATCHED: 'DLAS&EIM MATCHED (DM1)',
} as const;

export type InterfaceStatus = typeof InterfaceStatus[keyof typeof InterfaceStatus];

// Helper type for interface direction
export const InterfaceDirection = {
  IN: 'IN',
  OUT: 'OUT',
} as const;

export type InterfaceDirection = typeof InterfaceDirection[keyof typeof InterfaceDirection]; 