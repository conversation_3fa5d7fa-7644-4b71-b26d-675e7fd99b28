import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { ApiEndpoint } from "~/components/api-docs/api-endpoint";
import { ApiForm } from "~/components/api-docs/api-form";
import { apiSchemas } from "~/lib/api-schemas";
import { useSetPageTitle } from "~/hooks/use-set-page-title";

export function loader() {
  return json({
    title: "API Documentation",
    apiSchemas
  });
}

export default function ApiDocs() {
  const { title, apiSchemas } = useLoaderData<typeof loader>();
  
  // Set the page title
  useSetPageTitle("API Documentation", "Reference for OMS APIs and integration points");
  
  return (
    <div className="container py-8">
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Available Endpoints</h2>
        <nav className="flex flex-col gap-2">
          {apiSchemas.map(schema => (
            <a 
              key={schema.id}
              href={`#${schema.id}`}
              className="text-blue-600 hover:underline flex items-center gap-2"
            >
              <span className="inline-block w-16 font-mono text-xs px-2 py-1 rounded bg-blue-100 text-blue-800">
                {schema.method}
              </span>
              <span className="font-mono">{schema.path}</span>
            </a>
          ))}
        </nav>
      </div>
      
      <div className="space-y-8">
        {apiSchemas.map(schema => (
          <ApiEndpoint 
            key={schema.id}
            {...schema}
          >
            <ApiForm 
              path={schema.path}
              method={schema.method}
              parameters={schema.parameters}
            />
          </ApiEndpoint>
        ))}
      </div>
    </div>
  );
}