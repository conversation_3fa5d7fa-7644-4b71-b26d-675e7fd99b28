# Test Data for DLAS/HEET APIs

## Organizations API Response
```json
[
  {
    "level1": "DBS",
    "level1Id": "RTB00001",
    "level2": "Tech",
    "level2Id": "RTB07001",
    "level3": "MBG",
    "level3Id": "RTB00002",
    "level4": "ET",
    "level4Id": "RTB00003",
    "name": "EQC",
    "id": "RTB00004",
    "level": 5
  },
  {
    "level1": "DBS",
    "level1Id": "RTB00001",
    "level2": "Tech",
    "level2Id": "RTB07001",
    "level3": "MBG",
    "level3Id": "RTB00002",
    "level4": "ET",
    "level4Id": "RTB00003",
    "name": "EQD",
    "id": "RTB00005",
    "level": 5
  },
  {
    "level1": "DBS",
    "level1Id": "RTB00001",
    "level2": "Tech",
    "level2Id": "RTB07001",
    "level3": "MBG",
    "level3Id": "RTB00002",
    "level4": "FTR",
    "level4Id": "RTB00006",
    "name": "PC",
    "id": "RTB00007",
    "level": 5
  },
  {
    "level1": "DBS",
    "level1Id": "RTB00001",
    "level2": "Tech",
    "level2Id": "RTB07001",
    "level3": "MBG",
    "level3Id": "RTB00002",
    "level4": "FTR",
    "level4Id": "RTB00006",
    "name": "ACC",
    "id": "RTB00008",
    "level": 5
  }
]
```

## Applications API Response
```json
[
  {
    "applicationInstanceId": "239906",
    "name": "CoB3",
    "shortName": "CorB3",
    "description": "Core Banking System 3",
    "criticality": "Tier 1",
    "status": "Active",
    "strategicStatus": "Strategic",
    "itOwningOrganisation": {
      "level2": "Tech",
      "level3": "MBG",
      "level4": "ET",
      "level5": "EQC"
    },
    "pladaServiceId": "2969",
    "pladaServiceName": "TAS-HK",
    "pladaOwners": [
      {
        "psid": "********",
        "displayName": "John Smith",
        "emailAddress": "<EMAIL>"
      }
    ],
    "pladaDelegates": [
      {
        "psid": "********",
        "displayName": "Jane Doe",
        "emailAddress": "<EMAIL>"
      }
    ]
  },
  {
    "applicationInstanceId": "239907",
    "name": "TAS",
    "shortName": "TAS",
    "description": "Trading Analytics System",
    "criticality": "Tier 2",
    "status": "Active",
    "strategicStatus": "Strategic",
    "itOwningOrganisation": {
      "level2": "Tech",
      "level3": "MBG",
      "level4": "FTR",
      "level5": "PC"
    },
    "pladaServiceId": "2970",
    "pladaServiceName": "TAS-SG",
    "pladaOwners": [
      {
        "psid": "44489347",
        "displayName": "Bob Wilson",
        "emailAddress": "<EMAIL>"
      }
    ],
    "pladaDelegates": [
      {
        "psid": "44489348",
        "displayName": "Alice Brown",
        "emailAddress": "<EMAIL>"
      }
    ]
  }
]
```

## Interface and Dataset Details API Response
```json
{
  "appid": "239906",
  "dataDate": "2024-03-19",
  "interface": {
    "interface_dlas_logged": [
      {
        "Status": "EIM UPDATED",
        "Direction": "IN",
        "EIMInterfaceID": "12314758",
        "InterfaceName": "Trade Data Interface",
        "SendAppID": "239907",
        "SendAppName": "TAS",
        "ReceivedAppID": "239906",
        "ReceivedAppName": "CoB3",
        "TransferType": "File",
        "Frequency": "Daily",
        "Technology": "SFTP_UNIX",
        "Pattern": "SFTP-0001",
        "RelatedDrilldownKey": 3411961,
        "RelatedDatasetList": {
          "logged_dataset": [
            "C_239906_120454303_000_FO_Notional_20240319.csv",
            "C_239906_2060_CD_HK_TREAHK_20240319.csv"
          ]
        },
        "DemiseDate": null
      },
      {
        "Status": "DLAS ONLY",
        "Direction": "OUT",
        "EIMInterfaceID": null,
        "InterfaceName": "Risk Report Interface",
        "SendAppID": "239906",
        "SendAppName": "CoB3",
        "ReceivedAppID": "239907",
        "ReceivedAppName": "TAS",
        "TransferType": "File",
        "Frequency": "Daily",
        "Technology": "SFTP_UNIX",
        "Pattern": "SFTP-0002",
        "RelatedDrilldownKey": 3411962,
        "RelatedDatasetList": {
          "logged_dataset": [
            "C_239906_2061_CD_HK_TREAHK_20240319.csv"
          ]
        },
        "DemiseDate": null
      }
    ]
  },
  "dataset": {
    "dataset_logged_list": [
      {
        "Status": "COLLIBRA UPDATED",
        "Direction": "IN",
        "EimInterfaceID": "12314758",
        "DatasetStatus": "NORMAL",
        "DatasetName": "C_239906_120454303_000_FO_Notional_20240319.csv",
        "Description": "Front Office Notional Data",
        "InterfaceSerial": 3411961,
        "TransferType": "File",
        "Frequency": "Daily",
        "PrimaryDataTerm": {
          "name": "Trade Data"
        },
        "ProductType": ["FX", "IR"],
        "RelatedDrilldownList": ["3f7b884c-cc6e-4295-a610-0b730bd001a0"]
      },
      {
        "Status": "COLLIBRA UPDATED",
        "Direction": "IN",
        "EimInterfaceID": "12314758",
        "DatasetStatus": "NORMAL",
        "DatasetName": "C_239906_2060_CD_HK_TREAHK_20240319.csv",
        "Description": "Treasury Hong Kong Data",
        "InterfaceSerial": 3411961,
        "TransferType": "File",
        "Frequency": "Daily",
        "PrimaryDataTerm": {
          "name": "Trade Data"
        },
        "ProductType": ["Treasury"],
        "RelatedDrilldownList": ["3f7b884c-cc6e-4295-a610-0b730bd001a0"]
      },
      {
        "Status": "DLAS ONLY",
        "Direction": "OUT",
        "EimInterfaceID": null,
        "DatasetStatus": "NORMAL",
        "DatasetName": "C_239906_2061_CD_HK_TREAHK_20240319.csv",
        "Description": "Risk Report Data",
        "InterfaceSerial": 3411962,
        "TransferType": "File",
        "Frequency": "Daily",
        "PrimaryDataTerm": {
          "name": "Risk Report"
        },
        "ProductType": ["Risk"],
        "RelatedDrilldownList": ["36b88edf-06a6-470f-8db7-d710e9f636a8"]
      }
    ]
  }
}
```

## Event IDs API Response
```json
[
  {
    "events": [
      "3f7b884c-cc6e-4295-a610-0b730bd001a0",
      "36b88edf-06a6-470f-8db7-d710e9f636a8"
    ],
    "interface_serial": 3411961,
    "last_update_date": "2024-03-19",
    "log_date": "2024-03-19",
    "receiverapplicationid": "239906",
    "senderapplicationid": "239907"
  }
]
```

## Events API Response
```json
[
  {
    "msgId": "3f7b884c-cc6e-4295-a610-0b730bd001a0",
    "businessDate": "2024-03-19",
    "createdDateTime": "2024-03-19T08:00:00.000UTC",
    "datasetName": "C_239906_120454303_000_FO_Notional_20240319.csv",
    "endNodeId": "239906",
    "endNodeName": "CoB3",
    "frequency": "Daily",
    "rawJson": {
      "businessDate": "2024-03-19",
      "createdDateTime": "2024-03-19T08:00:00.000UTC",
      "interface": {
        "fromApp": {
          "id": "239907",
          "name": "TAS"
        },
        "toApp": {
          "id": "239906",
          "name": "CoB3"
        }
      }
    },
    "startNodeId": "239907",
    "startNodeName": "TAS",
    "valid": "Y"
  },
  {
    "msgId": "36b88edf-06a6-470f-8db7-d710e9f636a8",
    "businessDate": "2024-03-19",
    "createdDateTime": "2024-03-19T16:00:00.000UTC",
    "datasetName": "C_239906_2061_CD_HK_TREAHK_20240319.csv",
    "endNodeId": "239907",
    "endNodeName": "TAS",
    "frequency": "Daily",
    "rawJson": {
      "businessDate": "2024-03-19",
      "createdDateTime": "2024-03-19T16:00:00.000UTC",
      "interface": {
        "fromApp": {
          "id": "239906",
          "name": "CoB3"
        },
        "toApp": {
          "id": "239907",
          "name": "TAS"
        }
      }
    },
    "startNodeId": "239906",
    "startNodeName": "CoB3",
    "valid": "Y"
  }
]
``` 