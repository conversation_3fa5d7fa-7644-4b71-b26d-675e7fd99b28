"use client"

import * as React from "react"
import { Link, useLoaderData, useNavigate, useLocation } from "@remix-run/react"
import type { RootLoaderData } from "~/root"
import { SidebarTrigger } from "./sidebar"
import { usePageTitle } from "~/contexts/page-title-context"

export function Header() {
  const { organizations } = useLoaderData<RootLoaderData>();
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const { title, description } = usePageTitle();
  
  // Initialize state from URL parameters
  const [selectedOrgLevel4, setSelectedOrgLevel4] = React.useState(
    searchParams.get("level4") || ""
  );
  const [selectedOrgLevel5, setSelectedOrgLevel5] = React.useState(
    searchParams.get("level5") || ""
  );

  // Store title in state to avoid direct DOM updates during transitions
  const [displayTitle, setDisplayTitle] = React.useState("");
  
  // Update display title when title changes, with debounce
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDisplayTitle(title || "");
    }, 20);
    
    return () => clearTimeout(timer);
  }, [title]);

  // Sync state with URL parameters when they change
  React.useEffect(() => {
    const params = new URLSearchParams(location.search);
    const level4 = params.get("level4") || "";
    const level5 = params.get("level5") || "";
    
    setSelectedOrgLevel4(level4);
    // Only reset level5 if level4 changes AND level5 is not specified in the URL
    if (level4 !== selectedOrgLevel4 && !level5) {
      setSelectedOrgLevel5(""); // Reset level5 if level4 changes and no level5 is specified
    } else {
      setSelectedOrgLevel5(level5);
    }
  }, [location.search, selectedOrgLevel4]);

  return (
    <div className="sticky top-0 z-50 w-full border-b bg-background">
      <div className="flex h-16 items-center px-4">
        <div className="flex items-center gap-4">
          <SidebarTrigger />
          <div className="hidden md:flex">
            <Link to="/" className="mr-6 flex items-center space-x-2">
              <div className="text-left">
                <h1 className="text-lg font-semibold">
                  OMS {displayTitle && <span>{displayTitle}</span>}
                </h1>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}