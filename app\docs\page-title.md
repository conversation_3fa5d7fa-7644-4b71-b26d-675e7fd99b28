# Page Title System

The page title system allows for displaying page titles and descriptions in the main header, freeing up space in the main content area.

## Components

### 1. PageTitleContext

Located in `app/contexts/page-title-context.tsx`, this context provides a centralized way to manage page titles across the application.

### 2. usePageTitle Hook

A hook that provides access to the page title context.

```tsx
const { title, description, setPageTitle } = usePageTitle();
```

### 3. useSetPageTitle Hook

A utility hook that automatically sets and cleans up page titles in route components.

```tsx
useSetPageTitle("Page Title", "Optional page description");
```

## Usage in Route Components

To add a page title to a route component, import and use the `useSetPageTitle` hook:

```tsx
import { useSetPageTitle } from "~/hooks/use-set-page-title";

export default function MyPage() {
  useSetPageTitle("My Page", "This is the description for my page");
  
  // Rest of your component...
}
```

## Usage in UI Components

For UI components that need to dynamically update the page title:

```tsx
import { usePageTitle } from "~/contexts/page-title-context";

export function MyComponent({ title, description }) {
  const { setPageTitle } = usePageTitle();
  
  useEffect(() => {
    setPageTitle(title, description);
    
    // Clean up when component unmounts
    return () => setPageTitle("", null);
  }, [title, description, setPageTitle]);
  
  // Rest of your component...
}
```

## Benefits

1. **Consistent UI:** Standardizes the location of page titles across the application
2. **More Content Space:** Frees up valuable vertical space in the main content area
3. **Centralized Control:** Makes it easy to update the page title from any component
4. **Better Navigation Context:** Clearly shows the user where they are in the application 