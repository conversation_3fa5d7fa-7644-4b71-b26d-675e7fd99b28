import { Calendar, Rocket } from 'lucide-react';
import { SyncStatus, ScheduledSyncInfo } from '~/types/dlas-sync';
import { formatDateTimeDisplay } from '~/utils/dlas-sync-utils';
import { StatusIcon } from './status-icon';
import { DLASSyncState } from '~/types/dlas-sync';

interface StatusCardProps {
  status: SyncStatus;
  lastSync: string | null;
  nextSync: string | null;
  scheduledSyncInfo: ScheduledSyncInfo | null;
}

export function StatusCard({ status, lastSync, nextSync, scheduledSyncInfo }: StatusCardProps) {
  return (
    <div className="grid gap-4 md:grid-cols-3">
      {/* Status */}
      <div className="rounded-lg border p-4 space-y-2">
        <div className="text-sm font-medium text-muted-foreground">Status</div>
        <div className="flex items-center gap-2">
          <StatusIcon state={status.state} />
          <span className="text-xl font-semibold">
            {status.state === DLASSyncState.RUNNING ? <span className="text-blue-500">Running</span> : 
             status.state === DLASSyncState.IDLE ? <span className="text-green-500">Idle</span> : <span className="text-red-500">Error</span>}
          </span>
        </div>
      </div>
      
      {/* Last Run Information */}
      <div className="rounded-lg border p-4 space-y-2">
        <div className="text-sm font-medium text-muted-foreground">Last Run</div>
        <div className="flex items-center gap-2">
          <Rocket className="h-4 w-4 text-muted-foreground" aria-hidden="true" />
          <span className="text-xl font-semibold">
            {formatDateTimeDisplay(lastSync)}
          </span>
        </div>
      </div>
      
      {/* Next Run */}
      <div className="rounded-lg border p-4 space-y-2">
        <div className="text-sm font-medium text-muted-foreground">Next Run</div>
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" aria-hidden="true" />
          <span className="text-xl font-semibold">
            {nextSync 
              ? formatDateTimeDisplay(nextSync)
              : "06:00 AM"}
          </span>
        </div>
        {scheduledSyncInfo && (
          <div className="mt-1 text-xs text-muted-foreground">
            {scheduledSyncInfo.description}
          </div>
        )}
      </div>
    </div>
  );
} 