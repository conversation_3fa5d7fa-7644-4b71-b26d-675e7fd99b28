import { useState } from 'react';

/**
 * Custom hook for managing Server-Sent Events connection for sync updates
 * @returns Functions and states for SSE connection management
 */
export function useSyncEventSource() {
  const [connected, setConnected] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  const connect = (onMessage: (data: any) => void) => {
    let source: EventSource | null = null;
    
    try {
      // Connect to the SSE endpoint
      source = new EventSource('/oms/api/dlas-sync-events');
      
      // Set up event handlers
      source.onopen = () => {
        console.log('SSE connection established');
        setConnected(true);
        setError(null);
      };
      
      source.onerror = (e) => {
        console.error('SSE connection error:', e);
        setConnected(false);
        setError(new Error('SSE connection failed'));
        
        // Close the connection if there's an error
        if (source) {
          source.close();
        }
      };
      
      // Listen for sync update events
      source.addEventListener('sync-update', (event) => {
        try {
          const data = JSON.parse(event.data);
          onMessage(data);
        } catch (err) {
          console.error('Error parsing SSE message:', err);
        }
      });
    } catch (err) {
      console.error('Error setting up SSE connection:', err);
      setError(err instanceof Error ? err : new Error('Unknown error setting up SSE'));
      setConnected(false);
    }
    
    // Return cleanup function
    return () => {
      if (source) {
        console.log('Closing SSE connection');
        source.close();
        setConnected(false);
      }
    };
  };
  
  return { connect, connected, error };
} 