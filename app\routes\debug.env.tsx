import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { isDevelopment } from "~/utils/env.server";

// Define the expected shape of context.env
interface EnvContext {
  env?: {
    HEET_API_URL?: string;
    DLAS_API_URL?: string;
    NODE_ENV?: string;
    DATABASE_URL?: string;
    [key: string]: string | undefined;
  };
}

export async function loader({ request, context }: LoaderFunctionArgs) {
  // Only allow this in development mode
  if (!isDevelopment(context)) {
    throw new Response("Not Found", { status: 404 });
  }

  // Safe list of environment variables to display
  // DO NOT include sensitive variables like API keys, tokens, etc.
  const safeEnvVars = {
    NODE_ENV: process.env.NODE_ENV,
    HEET_API_URL: process.env.HEET_API_URL ? "✓ Defined" : "✗ Not defined",
    DLAS_API_URL: process.env.DLAS_API_URL ? "✓ Defined" : "✗ Not defined",
    DATABASE_URL: process.env.DATABASE_URL ? "✓ Defined" : "✗ Not defined",
  };

  // Cast context to the expected type
  const typedContext = context as EnvContext;

  // Check if context.env is available
  const contextEnvAvailable = Boolean(
    typedContext && typedContext.env && typeof typedContext.env === "object"
  );

  // Check specific variables in context if available
  const contextVars = contextEnvAvailable
    ? {
        HEET_API_URL: typedContext.env?.HEET_API_URL ? "✓ Defined" : "✗ Not defined",
        DLAS_API_URL: typedContext.env?.DLAS_API_URL ? "✓ Defined" : "✗ Not defined",
        NODE_ENV: typedContext.env?.NODE_ENV,
      }
    : {};

  return json({
    process_env: safeEnvVars,
    context_env_available: contextEnvAvailable,
    context_env: contextVars,
  });
}

export default function DebugEnv() {
  const data = useLoaderData<typeof loader>();

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Environment Debug</h1>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">process.env Variables</h2>
        <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-60">
          {JSON.stringify(data.process_env, null, 2)}
        </pre>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Context Environment</h2>
        <p className="mb-2">
          Context env available: <strong>{data.context_env_available ? "Yes" : "No"}</strong>
        </p>
        
        {data.context_env_available && (
          <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-60">
            {JSON.stringify(data.context_env, null, 2)}
          </pre>
        )}
      </div>
      
      <div className="text-sm text-gray-500 mt-4">
        Note: This page is only available in development mode and only shows safe environment variables.
      </div>
    </div>
  );
} 