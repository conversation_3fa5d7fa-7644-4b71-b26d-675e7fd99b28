---
description: 
globs: 
alwaysApply: false
---
# Project Structure Guide

## Core Directories

### App Directory
The `app/` directory contains the main application code:
- [app/components/](mdc:app/components) - Reusable UI components
- [app/routes/](mdc:app/routes) - Application routes and pages
- [app/models/](mdc:app/models) - Data models and interfaces
- [app/services/](mdc:app/services) - Business logic and services
- [app/utils/](mdc:app/utils) - Utility functions
- [app/hooks/](mdc:app/hooks) - Custom React hooks
- [app/contexts/](mdc:app/contexts) - React context providers
- [app/db/](mdc:app/db) - Database schemas and types

### Configuration
Key configuration files:
- [package.json](mdc:package.json) - Project dependencies and scripts
- [tsconfig.json](mdc:tsconfig.json) - TypeScript configuration
- [remix.config.js](mdc:remix.config.js) - Remix framework configuration
- [tailwind.config.ts](mdc:tailwind.config.ts) - Tailwind CSS configuration
- [drizzle.config.ts](mdc:drizzle.config.ts) - Drizzle ORM configuration

### Documentation
- [OMS-Technical-Reference.md](mdc:OMS-Technical-Reference.md) - Technical documentation
- [OMS-User-Manual.md](mdc:OMS-User-Manual.md) - User guide
- [OMS-Quick-Reference-Guide.md](mdc:OMS-Quick-Reference-Guide.md) - Quick reference
- [README.md](mdc:README.md) - Project overview

### Build and Assets
- `build/` - Compiled application assets
- `public/` - Static assets and icons
- `drizzle/` - Database migrations and metadata

