/**
 * This file re-exports the InterfaceModel from its new location
 * to maintain backward compatibility with existing code.
 * 
 * Note: Some sync-related methods have been simplified in the refactored version:
 * - Only updateSyncStatus remains for updating sync status
 * - Other sync methods were consolidated to avoid redundancy
 */

export { InterfaceModel } from './interface/interface-model.server';
export type { RAGStatus, RAGStatusResult } from './interface/interface-types.server'; 