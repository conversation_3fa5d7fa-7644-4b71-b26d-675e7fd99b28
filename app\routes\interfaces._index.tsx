import { json, type LoaderFunction } from "@remix-run/node"
import { useLoaderD<PERSON>, use<PERSON><PERSON><PERSON>, useF<PERSON>cher, useLocation } from "@remix-run/react"
import { InterfacesDataTable } from "~/modules/interfaces/components/data-table"
import type { Interface } from "~/db/types/interfaces"
import { InterfaceModel } from "~/models/interface.server"
import type { InterfaceWithRagStatus } from "~/models/interface/interface-rag-status.server"
import { ApplicationModel, type ApplicationWithRagStatus } from "~/models/application.server"
import { DLASApiError } from "~/services/dlas.server"
import { RetryError } from "~/utils/retry.server"
import React from "react"
import { getPathWithFilters } from "~/utils/quick-access"

// Custom hooks
import { useSearchParamState } from "~/hooks/use-search-param-state"
import { useRootLoaderData } from "~/hooks/use-root-loader-data"
import { useHydrated } from "~/hooks/use-hydrated"

// Components
import { InterfaceHeader } from "~/modules/interfaces/components/interface-header"
import { InterfaceListInfo } from "~/modules/interfaces/components/interface-list-info"
import { SearchableTreeView } from "~/components/application-tree-view/searchable-tree-view"

interface LoaderData {
  interfaces: InterfaceWithRagStatus[];
  applications: ApplicationWithRagStatus[];
  selectedApplication?: ApplicationWithRagStatus;
  error?: string;
  lastUpdated?: string;
  searchParams: {
    query?: string;
    orgLevel4?: string;
    orgLevel5?: string;
    sendAppId?: string;
    receivedAppId?: string;
  }
}

export const loader: LoaderFunction = async ({ request }) => {
  const url = new URL(request.url);
  const query = url.searchParams.get("query") || "";
  const orgLevel4 = url.searchParams.get("level4") || "all";
  const orgLevel5 = url.searchParams.get("level5") || "all";

  try {
    // Prepare filter options for interfaces
    const interfaceOptions = {
      orgLevel4: orgLevel4 !== "all" ? orgLevel4 : undefined,
      orgLevel5: orgLevel5 !== "all" ? orgLevel5 : undefined,
    };

    // Get all applications for the tree view regardless of filters
    const applications = await ApplicationModel.findAll();
    
    // Enhance applications with interface RAG status
    const enhancedApplications = await ApplicationModel.enhanceWithInterfaceRagStatus(applications);
    
    // Only fetch interfaces if there's a search query
    let interfaces: Interface[] = [];
    let selectedApplication: ApplicationWithRagStatus | undefined;
    
    if (query) {
      // Interface filters can still apply
      const isAppIdPattern = /^\d+$/.test(query); // Checks if query is numeric, adjust this regex as needed
      
      if (isAppIdPattern) {
        // First try to find as an exact application ID match
        const appResult = await ApplicationModel.findById(query);
        
        // If we found an application, get its interfaces
        interfaces = await InterfaceModel.findAll({
          ...interfaceOptions,
          appId: query
        });
        
        // Only set selectedApplication if interfaces were found
        if (interfaces.length > 0 && appResult) {
          // Enhance the selected application with RAG status
          const [enhancedApp] = await ApplicationModel.enhanceWithInterfaceRagStatus([appResult]);
          selectedApplication = enhancedApp;
        }
      } else {
        // Text search across interface name and application names
        interfaces = await InterfaceModel.findAll({
          ...interfaceOptions,
          searchQuery: query
        });
      }
    }  else {
      // Search via organization
      interfaces = await InterfaceModel.findAll(interfaceOptions);
    }
    
    // Enhance interfaces with RAG status
    const enhancedInterfaces = await InterfaceModel.enhanceWithDatasetRagStatus(interfaces);
    
    const lastUpdated = new Date().toISOString();
    return json<LoaderData>({ 
      interfaces: enhancedInterfaces,
      applications: enhancedApplications,
      selectedApplication,
      lastUpdated,
      searchParams: { query, orgLevel4, orgLevel5 }
    });
  } catch (error) {
    console.error("Error loading interfaces:", error);
    
    if (error instanceof DLASApiError) {
      return json(
        { 
          interfaces: [], 
          applications: [],
          error: `DLAS API Error: ${error.message}`,
          searchParams: {}
        },
        { status: error.status }
      );
    }

    if (error instanceof RetryError) {
      const lastError = error.lastError;
      const errorMessage = lastError instanceof Error 
        ? lastError.message 
        : 'Unknown error';
      
      return json(
        { 
          interfaces: [], 
          applications: [],
          error: `Failed after ${error.attempts} attempts: ${errorMessage}`,
          searchParams: {}
        }, 
        { status: 500 }
      );
    }

    return json(
      { 
        interfaces: [], 
        applications: [],
        error: "Failed to load interfaces",
        searchParams: {}
      }, 
      { status: 500 }
    );
  }
}

export default function InterfacesPage() {
  const { 
    interfaces, 
    applications, 
    selectedApplication, 
    lastUpdated: initialLastUpdated, 
    searchParams, 
    error 
  } = useLoaderData<LoaderData>();
  
  const { organizations } = useRootLoaderData();
  const navigate = useNavigate();
  const fetcher = useFetcher<LoaderData>();
  const location = useLocation();
  
  // Use custom search state management
  const { 
    searchValue, 
    searchParams: searchParamsState, 
    setSearchParams, 
    handleSearchChange, 
    hasFilters: computeHasFilters, 
    clearFilters
  } = useSearchParamState(searchParams.query);
  
  // Get the current data (from fetcher or from initial load)
  const currentData = {
    interfaces: fetcher.data?.interfaces || interfaces,
    applications: fetcher.data?.applications || applications,
    selectedApplication: fetcher.data?.selectedApplication || selectedApplication, 
    lastUpdated: initialLastUpdated,
    error,
    searchParams
  };
  
  const lastUpdated = currentData.lastUpdated || initialLastUpdated;
  const currentInterfaces = currentData.interfaces || interfaces;

  // Computed values
  const hasFilters = computeHasFilters({ hasSelectedApplication: !!selectedApplication });

  // User-facing action handlers
  const handleViewDatasets = (interfaceId: string, interfaceName: string) => {
    const navigationPath = `/interfaces/${interfaceId}/datasets`;
    
    // Use the getPathWithFilters utility to preserve all filters
    const pathWithFilters = getPathWithFilters(navigationPath, location);
    
    // Navigate to the datasets page with all filters preserved
    navigate(pathWithFilters);
  };
  
  const isHydrated = useHydrated();

  return (
    <div className="flex h-full">
      {/* Searchable Application Tree View */}
      {isHydrated ? (
        <SearchableTreeView 
          applications={applications}
          organizations={organizations}
          routeKey="interfaces"
          searchValue={searchValue}
          onSearchChange={handleSearchChange}
          hasFilters={hasFilters}
          onClearFilters={clearFilters}
          searchParams={searchParamsState}
          setSearchParams={setSearchParams}
        />
      ) : (
        <div className="w-72 border-r bg-muted/40 p-4 h-full overflow-auto">
          <div className="h-10 w-full bg-muted animate-pulse rounded"></div>
        </div>
      )}
      
      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="p-4 space-y-4">
          {/* Header with title and controls */}
          <InterfaceHeader
            lastUpdated={lastUpdated}
            isLoading={fetcher.state === 'loading'}
            error={currentData.error}
            selectedApplication={currentData.selectedApplication}
            searchQuery={searchParamsState.get("query") || undefined}
            interfaces={currentInterfaces}
          />
        
          {/* Interface list information */}
          <InterfaceListInfo
            interfaces={currentInterfaces}
            hasSearchQuery={!!searchParamsState.get("query")}
            lastUpdated={lastUpdated}
          />
        
          {/* Interface data table */}
          {currentInterfaces.length > 0 && (
            <InterfacesDataTable 
              data={currentInterfaces}
              orgLevel4={currentData.searchParams.orgLevel4 || "all"}
              orgLevel5={currentData.searchParams.orgLevel5 || "all"}
              onViewDatasets={handleViewDatasets}
            />
          )}
        </div>
      </div>
    </div>
  );
} 