import type { RAGStatusResult } from "~/models/interface/interface-types.server";

interface CacheEntry {
  data: RAGStatusResult;
  timestamp: number;
}

export class RAGCache {
  private static instance: RAGCache;
  private cache: Map<string, CacheEntry>;
  private readonly TTL: number = 5 * 60 * 1000; // 5 minutes in milliseconds

  private constructor() {
    this.cache = new Map();
  }

  public static getInstance(): RAGCache {
    if (!RAGCache.instance) {
      RAGCache.instance = new RAGCache();
    }
    return RAGCache.instance;
  }

  public get(key: string): RAGStatusResult | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // Check if entry has expired
    if (Date.now() - entry.timestamp > this.TTL) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  public set(key: string, data: RAGStatusResult): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  public getMultiple(keys: string[]): Record<string, RAGStatusResult> {
    const result: Record<string, RAGStatusResult> = {};
    
    for (const key of keys) {
      const value = this.get(key);
      if (value) {
        result[key] = value;
      }
    }
    
    return result;
  }

  public setMultiple(entries: Record<string, RAGStatusResult>): void {
    Object.entries(entries).forEach(([key, value]) => {
      this.set(key, value);
    });
  }

  public invalidate(key: string): void {
    this.cache.delete(key);
  }

  public invalidateMultiple(keys: string[]): void {
    keys.forEach(key => this.cache.delete(key));
  }

  public clear(): void {
    this.cache.clear();
  }
}

export const ragCache = RAGCache.getInstance(); 