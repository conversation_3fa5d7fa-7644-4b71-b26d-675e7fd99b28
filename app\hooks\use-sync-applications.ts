import { useState } from 'react';
import { toast } from 'sonner';
import { ApplicationClient, type SyncOptions } from '~/services/applications.client';

/**
 * Custom hook for managing application sync operations
 */
export function useSyncApplications(onRefresh: () => void) {
  const [isSyncing, setIsSyncing] = useState(false);
  
  /**
   * Syncs applications with the given filters
   */
  const syncApplications = async (options: SyncOptions = {}): Promise<void> => {
    if (isSyncing) {
      toast.error("Application sync already in progress");
      return;
    }
    
    setIsSyncing(true);
    
    try {
      toast.info("Syncing applications...");
      
      const result = await ApplicationClient.syncApplications(options);
      
      if (!result.success) {
        toast.error(result.error || "Failed to sync applications");
        throw new Error(result.error || "Failed to sync applications");
      }
      
      toast.success("Applications synced successfully");
      
      // Refresh data to reflect changes
      onRefresh();
    } catch (error) {
      console.error("Error syncing applications:", error);
      
      if (error instanceof Error && !error.message.includes("Failed to sync")) {
        toast.error(error.message);
      }
      
      // Force a refresh of the data to ensure we're displaying the latest state
      onRefresh();
      
      throw error;
    } finally {
      setIsSyncing(false);
    }
  };
  
  return {
    isSyncing,
    syncApplications
  };
} 