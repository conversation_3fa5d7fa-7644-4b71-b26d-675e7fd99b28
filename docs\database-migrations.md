# Database Migration Guide

This guide explains how to properly handle database schema changes in this application.

## Overview

Our application uses [Drizzle ORM](https://orm.drizzle.team) with SQLite for database management. We follow a migration-based approach to handle schema changes while preserving existing data.

## Key Concepts

- **Schema Definitions**: Located in `app/db/schema.ts` - define your database tables and relationships
- **Migration Files**: Located in the `./drizzle` folder - contain SQL to transform your database
- **Database Initialization**: Happens in `app/db/db.server.ts` - only initializes new/empty databases

## Migration Workflow

### 1. Making Schema Changes

When you need to modify the database schema (add/remove/modify tables or fields):

1. Edit the schema definitions in `app/db/schema.ts`
2. Generate migration files
3. Apply the migrations
4. Restart your application

### 2. Generating Migration Files

After updating your schema, generate migration files:

```bash
npx drizzle-kit generate
```

This command:
- Compares your schema with the current database state
- Creates migration files in the `./drizzle` folder
- Does NOT modify your database yet

### 3. Applying Migrations

To apply the generated migrations:

```bash
npx drizzle-kit migrate
```

This command runs the SQL in your migration files against your database.

### 4. Common Schema Change Scenarios

#### Adding a New Field

```typescript
// Before
export const applications = sqliteTable('applications', {
  id: integer('id').primaryKey(),
  name: text('name').notNull()
});

// After
export const applications = sqliteTable('applications', {
  id: integer('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description') // New field
});
```

When adding a new field, Drizzle will generate a migration that preserves existing data.

#### Removing a Field

```typescript
// Before
export const applications = sqliteTable('applications', {
  id: integer('id').primaryKey(),
  name: text('name').notNull(),
  obsoleteField: text('obsolete_field')
});

// After
export const applications = sqliteTable('applications', {
  id: integer('id').primaryKey(),
  name: text('name').notNull()
  // obsoleteField removed
});
```

When removing a field, Drizzle will generate a migration that preserves the remaining data.

#### Changing Field Type

Be careful when changing field types as this might result in data loss. Drizzle will attempt to convert values where possible.

#### Adding Relationships

When adding foreign keys or relationships, ensure the referenced tables and fields exist first.

## Troubleshooting

### "Table Already Exists" Errors

If you get this error when running migrations, it usually means:

1. You're trying to create a table that already exists
2. You might be mixing automatic and manual migrations

Solution: Make sure you're following the proper workflow and only running migrations once.

### Data Loss Prevention

Always back up your database before applying migrations in production. For development:

```bash
cp dev.db dev.db.backup
```

This allows you to restore if something goes wrong.

## Best Practices

1. **Small, Focused Migrations**: Make small, incremental changes rather than large schema rewrites
2. **Test Migrations**: Always test migrations on development data before applying to production
3. **Backup First**: Always backup your database before migrating production data
4. **Version Control**: Keep migration files in version control with your code
5. **Remember Data**: Consider existing data when making schema changes

## Development vs. Production

- **Development**: Migrations are manual to give you control over schema changes
- **Production**: Should follow the same migration workflow, but with proper backups 