import { withRetry, RetryError } from "~/utils/retry.server";
import type { DLASInterfaceResponse, DLASInterface } from "~/db/types/interfaces";
import type { DLASDataset } from "~/db/types/datasets";
import type { DLASInterfaceEvent, DLASEventIDsResponse } from "~/db/types/events";
import { createHash } from "crypto";
import { AppLoadContext } from '@remix-run/node';
import { getDlasApiUrl } from '~/utils/env.server';
import { ApiError, makeRequest, DEFAULT_API_CONFIG } from '~/utils/api-utils.server';

// API Error class specific to DLAS
export class DLASApiError extends ApiError {
  constructor(
    message: string,
    status: number,
    statusText: string
  ) {
    super(message, status, statusText, "DLAS");
  }
}

// API Configuration for DLAS
const DLAS_API_CONFIG = {
  ...DEFAULT_API_CONFIG,
  headers: {
    ...DEFAULT_API_CONFIG.headers,
  },
} as const;

// Helper function to generate interface ID
export function generateInterfaceId(dlasInterface: DLASInterface): string {
  const keyFields = [
    dlasInterface.SendAppID,
    dlasInterface.ReceivedAppID,
    dlasInterface.TransferType,
    dlasInterface.Frequency
  ].join('|');

  return createHash('sha256').update(keyFields).digest('hex');
}

// Helper function to make DLAS-specific API requests
async function makeDlasRequest<T>(url: string, options: RequestInit = {}): Promise<T> {
  return makeRequest<T>(url, options, DLAS_API_CONFIG, "DLAS");
}

/**
 * Service class for handling DLAS API interactions
 */
export class DLASService {
  /**
   * Fetches interfaces from DLAS for a specific application
   * @param appId The application ID to fetch interfaces for
   * @param context Optional context for environment variables
   * @param date Optional date parameter in YYYY-MM-DD format
   * @returns Promise resolving to interface data
   */
  static async fetchInterfaces(appId: string, context?: AppLoadContext, date?: string) {
    console.time(`DLAS Interface API Call for ${appId}`);
    
    try {
      const apiUrl = getDlasApiUrl(context);
      let url = `${apiUrl}/getAppDetails?appid=${appId}`;
      
      // Add date parameter if provided
      if (date) {
        url += `&date=${date}`;
      }
      
      const response = await makeDlasRequest<DLASInterfaceResponse>(url, {
        method: "GET",
      });

      console.timeEnd(`DLAS Interface API Call for ${appId}`);
      return response;
    } catch (error) {
      console.error(`[DLAS Service] Error fetching interfaces for ${appId}:`, error);
      throw error;
    }
  }

  /**
   * Fetches events from DLAS for specific event IDs
   * @param eventIds Array of event IDs to fetch details for
   * @param context Optional context for environment variables
   * @returns Promise resolving to event data
   */
  static async fetchEvents(eventIds: string[], context?: AppLoadContext): Promise<DLASInterfaceEvent[]> {
    console.time(`DLAS Events API Call for ${eventIds.length} events`);
    
    try {
      const apiUrl = getDlasApiUrl(context);
      const url = `${apiUrl}/getMilestoneMsgs`;
      const response = await makeDlasRequest<DLASInterfaceEvent[]>(url, {
        method: "POST",
        body: JSON.stringify({ ids: eventIds }),
        headers: {
          "Content-Type": "application/json",
        },
      });

      console.timeEnd(`DLAS Events API Call for ${eventIds.length} events`);
      return response;
    } catch (error) {
      console.error(`[DLAS Service] Error fetching events:`, error);
      throw error;
    }
  }

  /**
   * Fetches event IDs from DLAS for a specific interface serial
   * @param interfaceSerial The interface serial (relatedDrilldownKey) to fetch event IDs for
   * @param context Optional context for environment variables
   * @returns Promise resolving to array of event IDs
   */
  static async fetchEventIds(interfaceSerial: number, context?: AppLoadContext): Promise<string[]> {
    console.time(`DLAS Event IDs API Call for interface serial ${interfaceSerial}`);
    
    try {
      const apiUrl = getDlasApiUrl(context);
      const url = `${apiUrl}/getTransformInterfacesBySerialids?serialids=${interfaceSerial}`;
      const response = await makeDlasRequest<DLASEventIDsResponse[]>(url, {
        method: "GET",
      });

      console.timeEnd(`DLAS Event IDs API Call for interface serial ${interfaceSerial}`);
      
      // Return the events array from the first response item
      // Note: We expect only one item in the response array for a single interface serial
      return response[0]?.events ?? [];
    } catch (error) {
      console.error(`[DLAS Service] Error fetching event IDs for interface serial ${interfaceSerial}:`, error);
      throw error;
    }
  }

  /**
   * Transforms DLAS interface data for database storage
   * @param dlasInterface The interface data from DLAS API
   * @returns Transformed interface data ready for database insertion
   */
  static transformInterface(dlasInterface: DLASInterface) {
    return {
      omsInterfaceId: generateInterfaceId(dlasInterface),
      status: dlasInterface.Status,
      direction: dlasInterface.Direction,
      eimInterfaceId: dlasInterface.EIMInterfaceID,
      interfaceName: dlasInterface.InterfaceName,
      sendAppId: dlasInterface.SendAppID,
      sendAppName: dlasInterface.SendAppName,
      receivedAppId: dlasInterface.ReceivedAppID,
      receivedAppName: dlasInterface.ReceivedAppName,
      transferType: dlasInterface.TransferType,
      frequency: dlasInterface.Frequency,
      technology: dlasInterface.Technology,
      pattern: dlasInterface.Pattern,
      relatedDrilldownKey: dlasInterface.RelatedDrilldownKey,
      relatedDatasetList: JSON.stringify(dlasInterface.RelatedDatasetList.logged_dataset),
      demiseDate: dlasInterface.DemiseDate,
    };
  }

  /**
   * Transforms DLAS dataset data for database storage
   * @param dlasDataset The dataset data from DLAS API
   * @param omsInterfaceId The OMS Interface ID to link the dataset to
   * @returns Transformed dataset data ready for database insertion
   */
  static transformDataset(dlasDataset: DLASDataset, omsInterfaceId: string) {
    // Extract business context from the first element of the array if it exists
    const businessContext = dlasDataset.BusinessContexts && dlasDataset.BusinessContexts.length > 0
      ? dlasDataset.BusinessContexts[0]
      : null;

    return {
      datasetName: dlasDataset.DatasetName,
      status: dlasDataset.Status,
      direction: dlasDataset.Direction,
      eimInterfaceId: dlasDataset.EimInterfaceID,
      datasetStatus: dlasDataset.DatasetStatus,
      description: dlasDataset.Description,
      interfaceSerial: dlasDataset.InterfaceSerial,
      omsInterfaceId,
      transferType: dlasDataset.TransferType,
      frequency: dlasDataset.Frequency,
      primaryDataTerm: JSON.stringify(dlasDataset.PrimaryDataTerm),
      productType: JSON.stringify(dlasDataset.ProductType),
      relatedDrilldownList: JSON.stringify(dlasDataset.RelatedDrilldownList),
      // Business context fields - using optional chaining for consistency
      country: businessContext?.country,
      legalEntity: businessContext?.legal_entity,
      legalEntityCode: businessContext?.legal_entity_code,
      lineOfBusinessName: businessContext?.line_of_business_name,
      sla: null, // These fields will be populated by the application
      lastArrivalTime: null,
    };
  }

  /**
   * Transforms DLAS event data for database storage
   * @param dlasEvent The event data from DLAS API
   * @returns Transformed event data ready for database insertion
   */
  static transformEvent(dlasEvent: DLASInterfaceEvent) {
    return {
      msgId: dlasEvent.msgId,
      businessDate: dlasEvent.businessDate,
      createdDateTime: new Date(dlasEvent.createdDateTime.replace("UTC", "Z")),
      datasetName: dlasEvent.datasetName,
      endNodeId: dlasEvent.endNodeId,
      endNodeName: dlasEvent.endNodeName,
      frequency: dlasEvent.frequency,
      rawJson: JSON.stringify(dlasEvent.rawJson),
      startNodeId: dlasEvent.startNodeId,
      startNodeName: dlasEvent.startNodeName,
      valid: dlasEvent.valid,
      reportedForId: dlasEvent.reportedForId,
      reportedForName: dlasEvent.reportedForName,
      milestoneType: dlasEvent.milestoneType,
    };
  }

  /**
   * Instance method to get interfaces for an application
   * This wraps the static fetchInterfaces method
   */
  async getInterfacesForApplication(applicationId: string, date?: string): Promise<DLASInterface[]> {
    try {
      const response = await DLASService.fetchInterfaces(applicationId, undefined, date);
      return response.interface?.interface_dlas_logged || [];
    } catch (error) {
      console.error(`[DLAS Service] Error getting interfaces for application ${applicationId}:`, error);
      throw error;
    }
  }
} 