import React, { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, Card<PERSON>eader, CardTitle } from '~/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '~/components/ui/tabs';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '~/components/ui/collapsible';
import { ChevronDown, ChevronUp } from 'lucide-react';

export interface ApiParameter {
  name: string;
  type: string;
  description: string;
  required: boolean;
  example?: string;
}

export interface ApiResponse {
  status: number;
  description: string;
  example?: any;
}

export interface ApiEndpointProps {
  id: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  description: string;
  parameters: ApiParameter[];
  responses: ApiResponse[];
  children?: React.ReactNode;
}

export function ApiEndpoint({ 
  id,
  path, 
  method, 
  description, 
  parameters, 
  responses,
  children
}: ApiEndpointProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  const methodColors = {
    GET: 'bg-blue-100 text-blue-800 border-blue-200',
    POST: 'bg-green-100 text-green-800 border-green-200',
    PUT: 'bg-amber-100 text-amber-800 border-amber-200',
    DELETE: 'bg-red-100 text-red-800 border-red-200',
    PATCH: 'bg-purple-100 text-purple-800 border-purple-200'
  };

  return (
    <Card className="mb-8" id={id}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge className={`font-mono ${methodColors[method]}`}>
              {method}
            </Badge>
            <CardTitle className="text-lg font-mono">{path}</CardTitle>
          </div>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          </Button>
        </div>
        <CardDescription>{description}</CardDescription>
      </CardHeader>

      <Collapsible open={isOpen}>
        <CollapsibleContent>
          <Tabs defaultValue="docs">
            <TabsList className="mx-4">
              <TabsTrigger value="docs">Documentation</TabsTrigger>
              <TabsTrigger value="try">Try It</TabsTrigger>
            </TabsList>

            <TabsContent value="docs" className="p-4">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-2">Parameters</h3>
                  {parameters.length === 0 ? (
                    <p className="text-sm text-muted-foreground">No parameters required</p>
                  ) : (
                    <div className="border rounded-md overflow-hidden">
                      <table className="w-full text-sm">
                        <thead className="bg-muted text-muted-foreground">
                          <tr>
                            <th className="px-4 py-2 text-left font-medium">Name</th>
                            <th className="px-4 py-2 text-left font-medium">Type</th>
                            <th className="px-4 py-2 text-left font-medium">Description</th>
                            <th className="px-4 py-2 text-left font-medium">Required</th>
                          </tr>
                        </thead>
                        <tbody>
                          {parameters.map((param, i) => (
                            <tr key={i} className="border-t">
                              <td className="px-4 py-2 font-mono">{param.name}</td>
                              <td className="px-4 py-2 font-mono text-xs">{param.type}</td>
                              <td className="px-4 py-2">{param.description}</td>
                              <td className="px-4 py-2">
                                {param.required ? (
                                  <Badge variant="default">Required</Badge>
                                ) : (
                                  <Badge variant="outline">Optional</Badge>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-2">Responses</h3>
                  <div className="space-y-2">
                    {responses.map((response, i) => (
                      <div key={i} className="border rounded-md p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge
                            className={
                              response.status >= 200 && response.status < 300
                                ? 'bg-green-100 text-green-800 border-green-200'
                                : response.status >= 400
                                ? 'bg-red-100 text-red-800 border-red-200'
                                : 'bg-amber-100 text-amber-800 border-amber-200'
                            }
                          >
                            {response.status}
                          </Badge>
                          <span className="text-sm font-medium">{response.description}</span>
                        </div>
                        {response.example && (
                          <div className="mt-2">
                            <details>
                              <summary className="text-xs font-medium cursor-pointer">Example response</summary>
                              <pre className="mt-2 p-4 bg-muted rounded-md overflow-auto text-xs">
                                {JSON.stringify(response.example, null, 2)}
                              </pre>
                            </details>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="try" className="p-4">
              {children}
            </TabsContent>
          </Tabs>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
} 