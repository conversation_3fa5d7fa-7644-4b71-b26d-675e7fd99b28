"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table"

import { DataTablePagination } from "./data-table-pagination"
import { DataTableToolbar } from "./data-table-toolbar"

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  filterColumn?: string
  facetedFilters?: {
    column: string
    title: string
    options: {
      label: string
      value: string
      icon?: React.ComponentType<{ className?: string }>
    }[]
  }[]
  enableRowSelection?: boolean
  onRowSelectionChange?: (selectedRows: TData[]) => void
  state?: Record<string, any>
  onSync?: (id: string, pladaId: string) => void
  onViewInterfaces?: (id: string) => void
  defaultSort?: { id: string; desc: boolean }[]
}

export function DataTable<TData, TValue>({
  columns,
  data,
  filterColumn = "name",
  facetedFilters = [],
  enableRowSelection = false,
  onRowSelectionChange,
  state,
  onSync,
  onViewInterfaces,
  defaultSort,
}: DataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = React.useState({})
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [sorting, setSorting] = React.useState<SortingState>(defaultSort || [])

  // Filter out faceted filters for columns that don't exist
  const validFacetedFilters = React.useMemo(() => {
    return facetedFilters.filter(filter => 
      // Check if the column exists in the columns array
      columns.some(col => {
        const columnDef = col as any; // Type assertion to access any property
        return (
          (columnDef.accessorKey && columnDef.accessorKey === filter.column) ||
          (columnDef.id === filter.column)
        );
      })
    );
  }, [columns, facetedFilters]);

  // Handle selected rows changes
  React.useEffect(() => {
    if (onRowSelectionChange) {
      const selectedRowsData = Object.keys(rowSelection).map(
        (index) => data[parseInt(index)]
      );
      onRowSelectionChange(selectedRowsData);
    }
  }, [data, onRowSelectionChange, rowSelection]);

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      ...state,
    },
    enableRowSelection,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    meta: {
      onSync,
      onViewInterfaces,
    },
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  })

  return (
    <div className="space-y-4">
      <DataTableToolbar 
        table={table} 
        filterColumn={filterColumn} 
        facetedFilters={validFacetedFilters} 
      />
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  const columnMeta = header.column.columnDef.meta as any;
                  const width = columnMeta?.width;
                  
                  return (
                    <TableHead 
                      key={header.id} 
                      colSpan={header.colSpan}
                      style={width ? { width, minWidth: width } : undefined}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => {
                    const columnMeta = cell.column.columnDef.meta as any;
                    const width = columnMeta?.width;
                    
                    return (
                      <TableCell 
                        key={cell.id}
                        style={width ? { width, minWidth: width } : undefined}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    )
                  })}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <DataTablePagination table={table} />
    </div>
  )
} 