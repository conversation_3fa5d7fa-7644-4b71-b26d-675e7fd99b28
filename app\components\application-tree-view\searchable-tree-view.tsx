import * as React from "react"
import { useNavigate, useLocation } from "@remix-run/react"
import { TreeView, type TreeNodeData } from "./tree-view"
import type { ApplicationWithRagStatus } from "~/models/application.server"
import { cn } from "~/utils/cn"
import { Menu } from "lucide-react"
import { But<PERSON> } from "~/components/ui/button"
import { useApplicationTreeData } from "~/hooks/use-application-tree-data"
import { CommonSearchForm } from "~/components/common-search-form"
import type { HeetOrganizationApiResponse } from "~/db/types/organization"
import { useHydrated } from "~/hooks/use-hydrated"

interface SearchableTreeViewProps {
  applications: ApplicationWithRagStatus[]
  organizations?: HeetOrganizationApiResponse[]
  className?: string
  isCollapsed?: boolean
  routeKey: 'applications' | 'interfaces' | 'dashboard'
  preserveState?: boolean
  searchValue: string
  onSearchChange: (value: string) => void
  hasFilters: boolean
  onClearFilters: () => void
  searchParams: URLSearchParams
  setSearchParams: (params: URLSearchParams) => void
}

/**
 * Searchable Application Tree View Component 
 * Shows a hierarchical view of applications with integrated search functionality
 */
export function SearchableTreeView({ 
  applications, 
  organizations = [],
  className,
  isCollapsed = false,
  routeKey,
  preserveState = true,
  searchValue,
  onSearchChange,
  hasFilters,
  onClearFilters,
  searchParams,
  setSearchParams
}: SearchableTreeViewProps) {
  const [collapsed, setCollapsed] = React.useState(isCollapsed)
  const navigate = useNavigate()
  const location = useLocation()
  const isHydrated = useHydrated()
  
  // Use our hook for tree data and state management
  const {
    treeData,
    expandedIds,
    setExpandedIds,
    activeNodeId,
    filters,
    setFilters
  } = useApplicationTreeData({
    applications,
    organizations,
    preserveState,
    routeKey
  })

  // Handle form submission
  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    if (searchValue) {
      const newParams = new URLSearchParams(searchParams)
      newParams.set("query", searchValue)
      setSearchParams(newParams)
    }
  }
  
  // Handle expanding/collapsing nodes
  const handleToggleNode = (nodeId: string) => {
    // Explicitly check if the node is in expandedIds to determine the toggle action
    const isCurrentlyExpanded = expandedIds.includes(nodeId);
    const action = isCurrentlyExpanded ? 'Collapsing' : 'Expanding';
    
    const newExpandedIds = isCurrentlyExpanded
      ? expandedIds.filter(id => id !== nodeId)
      : [...expandedIds, nodeId];
    
    console.log(`${action} node:`, nodeId, 'Current:', expandedIds, '→ New:', newExpandedIds);
    
    // Call the setter from the hook
    setExpandedIds(newExpandedIds);
  }
  
  // Handle node selection
  const handleNodeSelect = (node: TreeNodeData) => {
    const newFilters = { ...filters };
    // Store the current expanded state so we can preserve it
    const currentExpandedIds = [...expandedIds];
    
    if (node.type === 'level4') {
      newFilters.orgLevel4 = node.name;
      newFilters.orgLevel5 = undefined;
      newFilters.query = undefined;
    } 
    else if (node.type === 'level5') {
      // Find parent level4
      for (const l4 of treeData) {
        const l5 = l4.children?.find(l5 => l5.id === node.id);
        if (l5) {
          newFilters.orgLevel4 = l4.name;
          newFilters.orgLevel5 = node.name;
          newFilters.query = undefined;
          break;
        }
      }
    } 
    else if (node.type === 'application') {
      newFilters.orgLevel4 = undefined;
      newFilters.orgLevel5 = undefined;
      newFilters.query = node.id;
    }
    
    // Update filters, which will trigger the filter effect
    setFilters(newFilters);
    
    // After a short delay, restore the expanded state
    // This needs to happen after the filter effect runs
    setTimeout(() => {
      console.log('Restoring expanded state:', currentExpandedIds);
      setExpandedIds(currentExpandedIds);
    }, 50);
  }
  
  return (
    <div className={cn(
      "h-full flex flex-col border-r transition-all duration-300 relative",
      collapsed ? "w-12" : "w-72", 
      className
    )}>
      {/* Search form */}
      {!collapsed && (
        <div className="flex-none p-2 border-b">
          <CommonSearchForm
            searchValue={searchValue}
            onSearchChange={onSearchChange}
            hasFilters={hasFilters}
            onClearFilters={onClearFilters}
            onSubmit={handleSubmit}
            placeholder="Search applications..."
            showActiveFilters={true}
            searchParams={searchParams}
            setSearchParams={setSearchParams}
          />
        </div>
      )}
      
      {/* Tree view content */}
      <div className="flex-1 min-h-0 overflow-hidden">
        {!collapsed ? (
          <div className="h-full overflow-auto">
            {isHydrated ? (
              <div className="p-1">
                <TreeView 
                  data={treeData} 
                  onNodeSelect={handleNodeSelect}
                  initialExpandedIds={expandedIds}
                  activeNodeId={activeNodeId}
                  onToggleNode={handleToggleNode}
                />
              </div>
            ) : (
              <div className="p-4">
                <div className="h-8 bg-muted/40 rounded animate-pulse mb-2"></div>
                <div className="h-8 bg-muted/40 rounded animate-pulse mb-2"></div>
                <div className="h-8 bg-muted/40 rounded animate-pulse mb-2"></div>
              </div>
            )}
          </div>
        ) : (
          <div className="h-full"></div>
        )}
      </div>
      
      {/* Collapse/expand button */}
      <div className={cn(
        "flex-none p-2 border-t bg-background flex items-center shadow-[0_-1px_2px_rgba(0,0,0,0.05)]",
        collapsed ? "justify-center" : "justify-end"
      )}>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setCollapsed(!collapsed)}
          className="h-6 w-6"
          title={collapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          <Menu className="h-3 w-3" />
        </Button>
      </div>
    </div>
  )
} 