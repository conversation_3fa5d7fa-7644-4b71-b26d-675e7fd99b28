"use client"

import { DataTable } from "~/components/ui/data-table/data-table"
import { getColumns } from "./columns"
import type { InterfaceEvent } from "~/db/types/events"
import React from "react"
import { usePageTitle } from "~/contexts/page-title-context"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "~/components/ui/breadcrumb"

interface EventsDataTableProps {
  data: InterfaceEvent[]
  isLoading?: boolean
  interfaceName: string
  datasetName: string
  onNavigateToInterface?: (preserveParams?: boolean) => void
  onNavigateToDataset?: (preserveParams?: boolean) => void
}

export function EventsDataTable({ 
  data, 
  isLoading = false,
  interfaceName,
  datasetName,
  onNavigateToInterface,
  onNavigateToDataset,
}: EventsDataTableProps) {
  const { setPageTitle } = usePageTitle();
  const columns = React.useMemo(
    () => getColumns(),
    []
  )

  // Set the page title when the component mounts
  React.useEffect(() => {
    setPageTitle("Events", `View and manage events for dataset ${datasetName}`);
  }, [datasetName, setPageTitle]);

  // Calculate valid filter options based on data
  const validOptions = React.useMemo(() => {
    const validValues = new Set(data.map(event => event.valid).filter(Boolean));
    
    // If the data has both "Y" and "N" values, return both options
    // Otherwise, return just the options that exist in the data
    const options = [];
    if (validValues.has("Y")) {
      options.push({ label: "Valid", value: "Y" });
    }
    if (validValues.has("N")) {
      options.push({ label: "Invalid", value: "N" });
    }
    
    return options.length ? options : [
      { label: "Valid", value: "Y" },
      { label: "Invalid", value: "N" },
    ];
  }, [data]);

  return (
    <div>
      <div className="mb-4">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink 
                onClick={() => onNavigateToInterface?.(true)} 
                className="hover:underline cursor-pointer"
              >
                Interface
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink 
                onClick={() => onNavigateToDataset?.(true)} 
                className="hover:underline cursor-pointer"
              >
                Datasets of {interfaceName}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Events of {datasetName}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <div className="space-y-4">
        <div className="relative">
          {isLoading && (
            <div className="absolute inset-0 bg-background/50 flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          )}
          <DataTable
            columns={columns}
            data={data}
            filterColumn="msgId"
            facetedFilters={data.length ? [
              {
                column: "valid",
                title: "Status",
                options: validOptions,
              },
            ] : []}
          />
        </div>
      </div>
    </div>
  )
} 