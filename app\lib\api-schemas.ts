import type { ApiEndpointProps } from '~/components/api-docs/api-endpoint';

/**
 * API Schema for Applications Sync endpoint
 */
export const applicationsSyncApiSchema: ApiEndpointProps = {
  id: 'api-applications-sync',
  path: '/oms/api/applications/sync',
  method: 'POST',
  description: 'Synchronize applications from HEET and their interfaces from DLAS',
  parameters: [
    {
      name: 'pladaServiceIds[]',
      type: 'string[]',
      description: 'Array of PLADA service IDs to synchronize',
      required: true,
      example: 'service-123',
    },
    {
      name: 'orgLevel4',
      type: 'string',
      description: 'Organization level 4 filter (use "all" to include all)',
      required: false,
      example: 'Department A',
    },
    {
      name: 'orgLevel5',
      type: 'string',
      description: 'Organization level 5 filter (use "all" to include all)',
      required: false,
      example: 'Team B',
    },
  ],
  responses: [
    {
      status: 200,
      description: 'Successfully synchronized applications and interfaces',
      example: {
        success: true,
      },
    },
    {
      status: 400,
      description: 'Bad request - No applications selected',
      example: {
        success: false,
        error: "No applications selected",
      },
    },
    {
      status: 405,
      description: 'Method not allowed - Only POST requests are supported',
      example: {
        success: false,
        error: "Method not allowed",
      },
    },
    {
      status: 500,
      description: 'Server error',
      example: {
        success: false,
        error: "Internal server error",
      },
    },
  ],
};

/**
 * API Schema for DLAS Sync GET endpoint
 */
export const dlasSyncGetApiSchema: ApiEndpointProps = {
  id: 'api-dlas-sync-get',
  path: '/oms/api/dlas-sync',
  method: 'GET',
  description: 'Get current DLAS synchronization status and configuration',
  parameters: [],
  responses: [
    {
      status: 200,
      description: 'Successfully retrieved sync status',
      example: {
        state: 'idle',
        progress: {
          total: 0,
          processed: 0,
          failed: 0,
          startTime: null,
          endTime: null,
        },
        config: {
          enabled: true,
        },
        nextScheduledRunTime: '2025-03-10T06:00:00Z',
        scheduledSyncInfo: {
          hour: 6,
          minute: 0,
          description: 'Daily sync at 6:00 AM'
        }
      },
    },
    {
      status: 500,
      description: 'Server error',
      example: {
        error: 'Internal server error',
      },
    },
  ],
};

/**
 * API Schema for DLAS Sync POST endpoint
 */
export const dlasSyncPostApiSchema: ApiEndpointProps = {
  id: 'api-dlas-sync-post',
  path: '/oms/api/dlas-sync',
  method: 'POST',
  description: 'Control DLAS synchronization operations',
  parameters: [
    {
      name: 'action',
      type: 'string',
      description: 'Sync action to perform (start or stop)',
      required: true,
      example: 'start',
    },
    {
      name: 'applicationId',
      type: 'string',
      description: 'Optional application ID to sync only that application. If not provided, all applications will be synced.',
      required: false,
      example: 'app-123',
    }
  ],
  responses: [
    {
      status: 200,
      description: 'Successfully performed the sync action',
      example: {
        success: true,
        message: 'Sync started',
      },
    },
    {
      status: 400,
      description: 'Bad request - Missing or invalid action parameter',
      example: {
        error: 'Missing action parameter',
      },
    },
    {
      status: 500,
      description: 'Server error',
      example: {
        error: 'Internal server error',
      },
    },
  ],
};

/**
 * Collection of all API schemas
 */
export const apiSchemas: ApiEndpointProps[] = [
  applicationsSyncApiSchema,
  dlasSyncGetApiSchema,
  dlasSyncPostApiSchema,
  // Add more schemas as they are created
]; 