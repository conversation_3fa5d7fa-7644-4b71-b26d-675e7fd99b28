/**
 * Type definition for interface sync status
 */
export type SyncStatus = 'success' | 'error' | 'pending';

/**
 * Response data structure for interface operations
 */
export interface SyncResponseData {
  success: boolean;
  message?: string;
  error?: string;
  newEventCount?: number;
  totalEvents?: number;
  skippedEvents?: number;
  updatedDatasets?: string[];
}

/**
 * Client-side service for handling interface operations
 * This is a client-safe version that doesn't include server-only code
 */
export class InterfaceClient {
  /**
   * Syncs events for a specific interface
   * @param interfaceId The OMS interface ID
   * @param relatedDrilldownKey The related drilldown key
   * @returns Response data from the sync operation
   */
  static async syncEvents(interfaceId: string, relatedDrilldownKey: number): Promise<SyncResponseData> {
    try {
      const formData = new FormData();
      formData.append('relatedDrilldownKey', relatedDrilldownKey.toString());
      
      const response = await fetch(`/oms/api/interfaces/${interfaceId}/events/sync`, {
        method: 'POST',
        body: formData,
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        return {
          success: false,
          error: data.error || `Error ${response.status}: ${response.statusText}`
        };
      }
      
      return data;
    } catch (error) {
      if (error instanceof Error) {
        return {
          success: false,
          error: error.message
        };
      }
      
      return {
        success: false,
        error: 'An unknown error occurred'
      };
    }
  }

  /**
   * Updates the sync status for an interface
   * @param interfaceId The OMS interface ID
   * @param status The new sync status
   */
  static async updateSyncStatus(interfaceId: string, status: SyncStatus): Promise<void> {
    try {
      const response = await fetch(`/oms/api/interfaces/${interfaceId}/sync-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update sync status: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error updating sync status:', error);
      throw error;
    }
  }
} 