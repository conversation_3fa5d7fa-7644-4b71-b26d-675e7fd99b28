import { sql } from "drizzle-orm"
import { 
  text,
  integer,
  sqliteTable
} from "drizzle-orm/sqlite-core"

export const events = sqliteTable("events", {
  msgId: text("msg_id").primaryKey(),
  businessDate: text("business_date"),
  createdDateTime: integer("created_date_time", { mode: "timestamp" }).notNull(),
  datasetName: text("dataset_name").notNull(),
  endNodeId: text("end_node_id"),
  endNodeName: text("end_node_name"),
  frequency: text("frequency"),
  rawJson: text("raw_json"),
  startNodeId: text("start_node_id"),
  startNodeName: text("start_node_name"),
  valid: text("valid", { length: 1 }),
  reportedForId: text("reported_for_id").notNull(),
  reportedForName: text("reported_for_name"),
  milestoneType: text("milestone_type").notNull(),
  createdAt: integer("created_at", { mode: "timestamp" })
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
}) 