node_modules

/.cache
/build
.env
dev.db

# Dependencies
node_modules
package-lock.json
yarn.lock
pnpm-lock.yaml

# Testing
coverage

# Production
build
dist
public/build
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Remix specific
.cache
.mf
.remix
api/_build

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.pem

# TypeScript
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Environment variables
.env