"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Badge } from "~/components/ui/badge"
import { DataTableColumnHeader } from "~/components/ui/data-table/data-table-column-header"
import { ApplicationWithRagStatus } from "~/models/application.server"
import { 
  APPLICATION_STATUS_OPTIONS, 
  CRITICALITY_OPTIONS,
  STRATEGIC_STATUS_OPTIONS 
} from "../constants"
import { Mail, Link, AlertCircle, AlertTriangle, CheckCircle2, RefreshCw } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog"
import { useState } from "react"
import { Button } from "~/components/ui/button"

function DescriptionCell({ description }: { description: string }) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const words = description.split(" ");
  const isTruncated = words.length > 50;
  const truncatedText = isTruncated 
    ? words.slice(0, 50).join(" ") 
    : description;

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm">{truncatedText}</span>
      {isTruncated && (
        <>
          <button
            onClick={() => setIsDialogOpen(true)}
            className="text-sm text-primary hover:underline"
          >
            view more...
          </button>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Full Description</DialogTitle>
              </DialogHeader>
              <div className="mt-4 text-sm whitespace-pre-wrap">
                {description}
              </div>
            </DialogContent>
          </Dialog>
        </>
      )}
    </div>
  );
}

// Helper function to provide tooltip text for RAG status
function getStatusTooltip(status: string): string {
  switch (status) {
    case "red":
      return "SLA Breached - Dataset completed after Expected Delivery Time or is overdue";
    case "amber":
      return "SLA at Risk - Dataset is within 30 minutes of Expected Delivery Time";
    case "green":
      return "On Schedule - All datasets are on time or completed as expected";
    default:
      return "Unknown Status";
  }
}

// Helper function to derive the overall status from RAG counts
function deriveRagStatus(counts: { red: number; amber: number; green: number }): string {
  if (counts.red > 0) return "red";
  if (counts.amber > 0) return "amber";
  return "green";
}

export const columns: ColumnDef<ApplicationWithRagStatus>[] = [
  {
    accessorKey: "applicationInstanceId",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="ID" />
    ),
    cell: ({ row }) => {
      const applicationId = row.getValue("applicationInstanceId") as string;
      
      return (
        <div className="w-[80px]">
          {applicationId ? (
            <a
              href={`https://itid.service-now.com/nav_to.do?uri=%2Fcmdb_ci_business_app.do%3Fsysparm_query%3Du_app_instance_idSTARTSWITH${applicationId}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 underline hover:text-blue-800"
            >
              {applicationId}
            </a>
          ) : (
            applicationId
          )}
        </div>
      );
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <Badge variant="outline">{row.original.shortName}</Badge>
          <span className="max-w-[500px] truncate font-medium">
            {row.getValue("name")}
          </span>
        </div>
      )
    },
  },
  {
    accessorKey: "description",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Description" />
    ),
    cell: ({ row }) => (
      <DescriptionCell description={row.original.description || ""} />
    ),
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = APPLICATION_STATUS_OPTIONS.find(
        (status) => status.value === (row.getValue("status") as string).toLowerCase()
      )

      if (!status) {
        return null
      }

      return (
        <div className="flex w-[100px] items-center">
          <Badge variant={status.variant}>
            <status.icon className="mr-2 h-4 w-4" />
            {status.label}
          </Badge>
        </div>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: "criticality",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Criticality" />
    ),
    cell: ({ row }) => {
      const criticality = CRITICALITY_OPTIONS.find(
        (crit) => crit.value === row.getValue("criticality")
      )

      if (!criticality) {
        return row.getValue("criticality")
      }

      return (
        <Badge variant="outline">
          {criticality.label}
        </Badge>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: "strategicStatus",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Strategic Status" />
    ),
    cell: ({ row }) => {
      const strategicStatus = STRATEGIC_STATUS_OPTIONS.find(
        (status) => status.value === row.getValue("strategicStatus")
      )

      return (
        <Badge variant="secondary">
          {strategicStatus?.label ?? row.getValue("strategicStatus")}
        </Badge>
      )
    },
  },
  {
    accessorKey: "orgLevel4",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Organization" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <Badge variant="outline">{row.original.orgLevel4}</Badge>
          <Badge variant="secondary">{row.original.orgLevel5}</Badge>
        </div>
      )
    },
  },
  {
    accessorKey: "ownerDisplayName",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Owner" />
    ),
    cell: ({ row }) => {
      const ownerDisplayName = row.getValue("ownerDisplayName") as string
      const ownerEmail = row.original.ownerEmail
      return ownerEmail ? (
        <a 
          href={`mailto:${ownerEmail}`}
          className="text-primary hover:underline flex items-center gap-2"
        >
          <span>{ownerDisplayName}</span>
          <Mail className="h-4 w-4" />
        </a>
      ) : (
        <span>{ownerDisplayName}</span>
      )
    },
  },
  {
    accessorKey: "interfaceRagStatus",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Interfaces Status" />
    ),
    meta: {
      width: "100px",
    },
    cell: ({ row }) => {
      const application = row.original;
      const interfaceCount = application.interfaceCount || 0;
      const ragStatus = application.interfaceRagStatus || { red: 0, amber: 0, green: 0 };
      const { red, amber, green } = ragStatus;
      const total = red + amber + green;
      
      // If there are no interfaces, just show a dash
      if (interfaceCount === 0) {
        return (
          <div className="text-center">
            <span className="text-muted-foreground">—</span>
          </div>
        );
      }
      
      // Derive status from counts
      const status = deriveRagStatus(ragStatus);
      
      // Calculate relative widths for the RAG bar segments
      // If total is 0, we'll set more visible default values 
      const redWidth = total > 0 ? (red / total) * 100 : 0;
      const amberWidth = total > 0 ? (amber / total) * 100 : 0;
      const greenWidth = total > 0 ? (green / total) * 100 : 100; // Show full green bar when no data
      
      return (
        <div className="flex flex-col w-full gap-1">
          {/* Status indicator */}
          <div className="flex items-center justify-center">
            <div title={getStatusTooltip(status)}>
              {red > 0 ? (
                <AlertCircle className="h-5 w-5 text-red-500" />
              ) : amber > 0 ? (
                <AlertTriangle className="h-5 w-5 text-amber-500" />
              ) : (
                <CheckCircle2 className="h-5 w-5 text-green-500" />
              )}
            </div>
          </div>
        </div>
      );
    },
    sortingFn: (rowA, rowB) => {
      // Sorting priority: red first, then amber, then green
      const statusA = deriveRagStatus(rowA.original.interfaceRagStatus || { red: 0, amber: 0, green: 0 });
      const statusB = deriveRagStatus(rowB.original.interfaceRagStatus || { red: 0, amber: 0, green: 0 });
      
      const statusOrder: Record<string, number> = { "red": 0, "amber": 1, "green": 2 };
      return statusOrder[statusA] - statusOrder[statusB];
    },
    filterFn: (row, id, value) => {
      // For RAG status filtering, check if any of the selected statuses have counts > 0
      const ragStatus = row.original.interfaceRagStatus || { red: 0, amber: 0, green: 0 };
      
      if (!value || !value.length) return true;
      
      return value.some((status: string) => {
        if (status === "red" && ragStatus.red > 0) return true;
        if (status === "amber" && ragStatus.amber > 0) return true;
        if (status === "green" && ragStatus.green > 0) return true;
        return false;
      });
    },
  },
  {
    accessorKey: "interfaceCount",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Interfaces" />
    ),
    enableSorting: true,
    cell: ({ row, table }) => {
      const application = row.original;
      const onViewInterfaces = (table.options.meta as { onViewInterfaces?: (id: string) => void })?.onViewInterfaces;
      const count = application.interfaceCount || 0;
      
      return (
        <div className="text-center">
          {count > 0 ? (
            <button
              onClick={() => onViewInterfaces?.(application.applicationInstanceId)}
              className="flex items-center justify-center gap-1 mx-auto px-2 py-1 text-primary rounded-md hover:bg-primary/10 transition-colors font-medium"
              title="View Interfaces"
            >
              <span>{count}</span>
              <Link className="h-3.5 w-3.5" />
            </button>
          ) : (
            <span className="text-muted-foreground">0</span>
          )}
        </div>
      );
    },
  },
] 