"use client"

import { Clock } from 'lucide-react';
import { useCallback, useEffect } from 'react';
import { Application } from '~/db/types/applications';
import { MagnifyingGlassIcon } from "@radix-ui/react-icons"
import { usePageTitle } from "~/contexts/page-title-context";

interface InterfaceHeaderProps {
  error?: string;
  onPin?: () => void;
  lastUpdated?: string;
  selectedApplication?: Application;
  searchQuery?: string;
  interfaces?: any[]; // Use any[] to avoid type conflicts
  showSearch?: boolean;
  showFilterDrawer?: boolean;
  onToggleFilterDrawer?: () => void;
  isLoading?: boolean;
}

export function InterfaceHeader({ 
  error,
  onPin,
  lastUpdated,
  selectedApplication,
  searchQuery,
  interfaces = [],
  showSearch = true,
  showFilterDrawer = true,
  onToggleFilterDrawer,
  isLoading = false
}: InterfaceHeaderProps) {
  const { setPageTitle } = usePageTitle();

  useEffect(() => {
    setPageTitle("Interfaces", "Manage and monitor your interface configurations");
  }, [setPageTitle]);

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center justify-between">
        <div>
          {error && (
            <p className="text-sm text-red-500 mt-2">
              {error}
            </p>
          )}
        </div>
      </div>
    </div>
  );
} 