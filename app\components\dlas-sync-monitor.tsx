import { useState, useEffect } from 'react';
import { useNavigation, useFetcher } from '@remix-run/react';

// Import types
import { 
  DLASSyncState, 
  DLASSyncMonitorProps, 
  ScheduledSyncInfo,
  SyncStatus 
} from '~/types/dlas-sync';

// Import custom hook for SSE
import { useSyncEventSource } from '~/hooks/use-sync-event-source';

// Import utility functions
import { createDefaultStatus, createStatusFromApiData } from '~/utils/dlas-sync-utils';

// Import sub-components
import { StatusCard } from './dlas-sync/status-card';
import { ProgressDisplay } from './dlas-sync/progress-display';
import { ActionButtons } from './dlas-sync/action-buttons';
import { ConnectionStatus } from './dlas-sync/connection-status';

export function DLASSyncMonitor({
  initialStatus,
  refreshInterval = 30000, // Keep a fallback refresh interval
}: DLASSyncMonitorProps) {
  // State management
  const [status, setStatus] = useState<SyncStatus>(
    initialStatus || createDefaultStatus()
  );
  
  // Track last and next sync time
  const [lastSync, setLastSync] = useState<string | null>(null);
  const [nextSync, setNextSync] = useState<string | null>(null);
  const [scheduledSyncInfo, setScheduledSyncInfo] = useState<ScheduledSyncInfo | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [actionError, setActionError] = useState<string | null>(null);
  
  // Remix hooks
  const navigation = useNavigation();
  const fetcher = useFetcher();
  
  // Use our SSE hook
  const { connect, connected, error } = useSyncEventSource();

  // Calculate whether an action is in progress
  const isActionPending = (action: string = '') => {
    // If checking for 'stop' action and sync is running, never block it
    if (action === 'stop' && status.state === DLASSyncState.RUNNING) {
      return false;
    }
    
    // Otherwise, check if any submission is in progress
    return isSubmitting || (navigation.state === 'submitting');
  };

  /**
   * Perform a sync action (start or stop)
   */
  const performAction = async (action: string, applicationId?: string, date?: string) => {
    if (isActionPending(action)) return;
    
    // Clear any previous errors
    if (actionError) {
      setActionError(null);
    }
    
    setIsSubmitting(true);
    
    // Optimistically update the UI state immediately for better UX
    if (action === 'start') {
      // Show running state right away
      setStatus(prevStatus => ({
        ...prevStatus,
        state: DLASSyncState.RUNNING,
        progress: {
          ...prevStatus.progress,
          startTime: new Date().toISOString(),
          endTime: null
        }
      }));
    } else if (action === 'stop') {
      // Show idle state right away
      setStatus(prevStatus => ({
        ...prevStatus,
        state: DLASSyncState.IDLE,
        progress: {
          ...prevStatus.progress,
          endTime: new Date().toISOString()
        }
      }));
    }
    
    try {
      const formData = new FormData();
      formData.append('action', action);
      
      // Add applicationId if provided
      if (applicationId) {
        formData.append('applicationId', applicationId);
      }
      
      // Add selected date if provided and valid
      if (action === 'start' && date) {
        formData.append('date', date);
      }
      
      const response = await fetch('/oms/api/dlas-sync', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }
      
      // Get the updated status after a short delay to ensure
      // the backend had time to process the request
      setTimeout(async () => {
        await fetchSyncStatus();
      }, 500);
      
      console.log(`Sync ${action} command accepted`);
    } catch (error) {
      console.error('Error performing sync action:', error);
      setActionError(`Failed to ${action} sync: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      // Revert the optimistic UI update in case of error
      await fetchSyncStatus();
    } finally {
      // We still need to disable isSubmitting to prevent double-clicks
      setIsSubmitting(false);
    }
  };
  
  // Fetch current sync status from the API
  const fetchSyncStatus = async () => {
    try {
      const response = await fetch('/oms/api/dlas-sync');
      
      if (response.ok) {
        const data = await response.json();
        
        // Extract data with safe fallbacks
        const apiStatus = data.state || DLASSyncState.IDLE;
        const safeProgress = data.progress || {};
        
        // Update state
        updateStatusFromData(apiStatus, safeProgress, data);
      } else {
        console.error('Failed to fetch sync status, received non-OK response:', response.status);
      }
    } catch (error) {
      console.error('Failed to fetch sync status:', error);
    }
  };
  
  // Helper function to update status from data (reused for both SSE and fetch)
  const updateStatusFromData = (apiStatus: any, safeProgress: any, data: any) => {
    // Set status with safe values
    setStatus(createStatusFromApiData(apiStatus, safeProgress));
    
    // Get last sync time from endTime if available
    if (safeProgress.endTime) {
      setLastSync(safeProgress.endTime);
    }
    
    // Get next scheduled run time directly from the API
    if (data.nextScheduledRunTime) {
      setNextSync(data.nextScheduledRunTime);
    }

    // Get scheduled sync info
    if (data.scheduledSyncInfo) {
      setScheduledSyncInfo(data.scheduledSyncInfo);
    }
  };
  
  // Handle SSE message
  const handleSseMessage = (data: any) => {
    if (data?.type === 'syncStatus' && data?.data) {
      const apiStatus = data.data;
      
      // Always update the UI with the latest state from the server
      // This ensures we're in sync with the backend
      setStatus({
        state: apiStatus.state || DLASSyncState.IDLE,
        progress: {
          // Application level
          totalItems: apiStatus.totalItems || 0,
          processedItems: apiStatus.processedItems || 0,
          successCount: apiStatus.successCount || 0,
          errorCount: apiStatus.errorCount || 0,
          errors: apiStatus.errors || [],
          
          // Interface level
          totalInterfaces: apiStatus.totalInterfaces || 0,
          processedInterfaces: apiStatus.processedInterfaces || 0,
          successInterfaces: apiStatus.successInterfaces || 0,
          
          // Dataset level
          totalDatasets: apiStatus.totalDatasets || 0,
          processedDatasets: apiStatus.processedDatasets || 0,
          successDatasets: apiStatus.successDatasets || 0,
          
          // Timing
          startTime: apiStatus.startTime || null,
          endTime: apiStatus.endTime || null,
          elapsedTimeMs: apiStatus.elapsedTimeMs || 0,
        }
      });
      
      // Update last sync time if available
      if (apiStatus.endTime && apiStatus.state === DLASSyncState.IDLE) {
        setLastSync(apiStatus.endTime);
      }
      
      // Get next scheduled run time if available
      if (apiStatus.nextScheduledRunTime) {
        setNextSync(apiStatus.nextScheduledRunTime);
      }
      
      // Clear any action errors when state changes
      if (actionError) {
        setActionError(null);
      }
    }
  };
  
  // Set up SSE connection
  useEffect(() => {
    const cleanup = connect(handleSseMessage);
    return cleanup;
  }, []);
  
  // Fallback to polling if SSE disconnects
  useEffect(() => {
    if (!connected) {
      // Initial fetch
      fetchSyncStatus();
      
      // Set up polling interval as backup
      const intervalId = setInterval(fetchSyncStatus, refreshInterval);
      
      return () => {
        clearInterval(intervalId);
      };
    }
  }, [connected, refreshInterval]);

  return (
    <div className="flex-1 space-y-8 p-6 pt-6">
      
      <div className="space-y-4">
        <div className="space-y-2">          
          {/* Status Cards */}
          <StatusCard 
            status={status}
            lastSync={lastSync}
            nextSync={nextSync}
            scheduledSyncInfo={scheduledSyncInfo}
          />
          
          {/* Progress Display */}
          <ProgressDisplay progress={status.progress} />
        </div>
      </div>
              
      {/* Action Buttons */}
      <div className="rounded-lg border p-4 space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-lg font-medium">Start Manual Sync</span>
        </div>
        
        <ActionButtons 
          state={status.state}
          isSubmitting={isSubmitting}
          isActionPending={isActionPending}
          performAction={performAction}
        />        
        
        <div className="flex items-center justify-between">
          {/* Connection Status & Errors */}
          <ConnectionStatus 
            connected={connected}
            actionError={actionError}
          />
        </div>
      </div>
    </div>
  );
} 