import { useState, useRef, useEffect } from 'react';
import { toast } from 'sonner';
import { SyncStatus } from '~/db/types/interfaces';
import type { Interface } from '~/db/types/interfaces';
import type { RAGStatus } from '~/models/interface.server';
import { InterfaceClient } from "~/services/interfaces.client";

// Type for interfaces with their RAG status
export type InterfaceWithStatus = Interface & {
  ragStatus: {
    counts: {
      red: number;
      amber: number;
      green: number;
    }
  };
  datasetCount?: number;
};

/**
 * Helper function to derive RAG status from counts
 * - If red > 0, status is "red"
 * - Else if amber > 0, status is "amber"
 * - Otherwise, status is "green"
 */
export function deriveRagStatus(counts: { red: number; amber: number; green: number }): RAGStatus {
  if (counts.red > 0) return "red";
  if (counts.amber > 0) return "amber";
  return "green";
}

/**
 * Custom hook for managing interface sync operations
 */
export function useSyncInterfaces(interfaces: InterfaceWithStatus[], onRefresh: () => void) {
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncingRows, setSyncingRows] = useState<Set<string>>(new Set());
  const syncAbortControllerRef = useRef<AbortController | null>(null);
  const lastManualSyncRef = useRef<number>(Date.now());
  
  // Clean up when unmounting
  useEffect(() => {
    return () => {
      if (syncAbortControllerRef.current) {
        syncAbortControllerRef.current.abort();
        syncAbortControllerRef.current = null;
      }
    };
  }, []);
  
  /**
   * Handles syncing a single interface
   * @param interfaceId The interface ID to sync
   * @param relatedDrilldownKey The related drilldown key
   * @param options Options for controlling the sync behavior
   * @returns The sync result
   */
  const syncInterface = async (
    interfaceId: string, 
    relatedDrilldownKey: number,
    options: { 
      showToasts?: boolean;  // Whether to show success/error toasts for this sync
    } = { 
      showToasts: true 
    }
  ) => {
    // Update the last manual sync time
    lastManualSyncRef.current = Date.now();
    
    // Add this interface to syncing rows
    setSyncingRows(prev => new Set([...prev, interfaceId]));
    
    try {
      const result = await InterfaceClient.syncEvents(interfaceId, relatedDrilldownKey);
      
      // Check for error
      if (!result.success) {
        // Update sync status to error
        try {
          await InterfaceClient.updateSyncStatus(interfaceId, SyncStatus.ERROR);
        } catch (statusUpdateError) {
          console.error("Failed to update sync status:", statusUpdateError);
        }
        
        // Show error toast if enabled and there's an error message
        if (options.showToasts && result.error) {
          // Show the error toast first
          toast.error(result.error);
          // Then mark it as handled to prevent duplicate displays
          throw new Error(`HANDLED_ERROR: ${result.error}`);
        }
        
        throw new Error(result.error || "Failed to sync interface");
      }
      
      // Successfully synced - show toast if enabled
      if (options.showToasts) {
        toast.success("Events synced successfully");
      }
      
      // Refresh data to reflect changes
      onRefresh();
      
      return result;
    } catch (caughtError: unknown) {
      console.error("Error syncing events:", caughtError);
      
      // Only show error toast if it hasn't been handled already and toasts are enabled
      if (options.showToasts && caughtError instanceof Error) {
        // Skip toast if error is already marked as handled
        if (caughtError.message.startsWith("HANDLED_ERROR:")) {
          // Just rethrow, toast already shown
          throw caughtError;
        }
        
        // Check for SQLite constraint errors and other database errors
        const isSqliteConstraintError = caughtError.message.includes("SQLITE_CONSTRAINT");
        
        if (isSqliteConstraintError) {
          // For SQLite constraint errors, show a nicer message and mark as handled
          toast.error("Database constraint error: This record already exists");
          throw new Error(`HANDLED_ERROR: ${caughtError.message}`);
        } else {
          // For other errors, just show the message
          toast.error(caughtError.message);
          // Mark the error as handled
          throw new Error(`HANDLED_ERROR: ${caughtError.message}`);
        }
      }
      
      // If we're not showing toasts, just pass the error through
      throw caughtError;
    } finally {
      // Remove this interface from syncing rows
      setSyncingRows(prev => {
        const newSet = new Set(prev);
        newSet.delete(interfaceId);
        return newSet;
      });
    }
  };
  
  /**
   * Handles syncing multiple interfaces
   */
  const syncAll = async () => {
    // Check if already syncing
    if (isSyncing) {
      toast.error("Sync already in progress");
      return;
    }
    
    // Prepare abort controller
    syncAbortControllerRef.current = new AbortController();
    const signal = syncAbortControllerRef.current.signal;
    
    // Update last manual sync time
    lastManualSyncRef.current = Date.now();
    
    // Prepare the list of interfaces to sync
    const interfacesToSync = interfaces.filter(i => 
      i.relatedDrilldownKey && i.omsInterfaceId
    );
    
    if (interfacesToSync.length === 0) {
      toast.info("No interfaces available to sync");
      return;
    }
    
    // Set syncing state
    setIsSyncing(true);
    setSyncingRows(new Set(interfacesToSync.map(i => i.omsInterfaceId)));
    
    let syncedCount = 0;
    let failedCount = 0;
    const totalCount = interfacesToSync.length;
    const errors = new Set<string>();
    
    // Notify start
    toast.info(`Starting sync for ${totalCount} interfaces`);
    
    try {
      for (const intf of interfacesToSync) {
        if (signal.aborted) {
          toast.info("Sync operation was aborted");
          break;
        }
        
        // Update interface sync status
        try {
          await InterfaceClient.updateSyncStatus(intf.omsInterfaceId, SyncStatus.PENDING);
        } catch (error) {
          console.error("Failed to update sync status:", error);
        }
        
        try {
          // Skip if signal aborted
          if (signal.aborted) break;
          
          // Sync the interface but don't show individual toasts for bulk operations
          await syncInterface(
            intf.omsInterfaceId, 
            intf.relatedDrilldownKey,
            { showToasts: false }
          );
          syncedCount++;
          
          // Provide progress update every few interfaces
          if (syncedCount % 5 === 0 || syncedCount === totalCount) {
            toast.info(`Synced ${syncedCount} of ${totalCount} interfaces`);
          }
        } catch (error) {
          failedCount++;
          
          // Track unique error messages for reporting
          if (error instanceof Error) {
            // Strip HANDLED_ERROR prefix if present
            const errorMsg = error.message.startsWith("HANDLED_ERROR:")
              ? error.message.substring("HANDLED_ERROR:".length).trim()
              : error.message;
            
            // Add to our error set
            errors.add(errorMsg);
            
            // Log the error for debugging
            console.error(`Error syncing interface ${intf.omsInterfaceId}:`, errorMsg);
          } else {
            // For non-Error objects
            console.error(`Error syncing interface ${intf.omsInterfaceId}:`, error);
            errors.add("Unknown error");
          }
          continue;
        }
      }
      
      // Show final summary based on success/failure counts
      if (signal.aborted) {
        // Don't show a summary if operation was aborted
        // (The abort message was already shown)
      } else if (failedCount === 0) {
        // All succeeded
        toast.success(`Successfully synced all ${totalCount} interfaces`);
      } else if (syncedCount === 0) {
        // All failed
        if (errors.size === 1) {
          // If there's just one error type, show it
          toast.error(`Failed to sync interfaces: ${Array.from(errors)[0]}`);
        } else {
          // For multiple errors, show a summary
          toast.error(`Failed to sync ${failedCount} interfaces. See console for details.`);
        }
      } else {
        // Mixed results
        toast.info(`Completed with ${syncedCount} of ${totalCount} interfaces synced`);
        if (errors.size > 0) {
          toast.warning(`${failedCount} interfaces failed to sync. See console for details.`);
        }
      }
    } catch (error) {
      console.error("Error in bulk sync operation:", error);
      toast.error("Error syncing interfaces");
    } finally {
      if (signal.aborted) {
        // Update all interface sync statuses to ERROR
        try {
          await Promise.all(
            Array.from(syncingRows).map(id => 
              InterfaceClient.updateSyncStatus(id, SyncStatus.ERROR)
            )
          );
        } catch (error) {
          console.error("Failed to update sync statuses:", error);
        }
      }
      
      // Clean up
      setIsSyncing(false);
      setSyncingRows(new Set());
      syncAbortControllerRef.current = null;
      
      // Force refresh of the data
      onRefresh();
    }
  };
  
  /**
   * Aborts any ongoing sync operations
   */
  const abortSync = () => {
    if (syncAbortControllerRef.current) {
      syncAbortControllerRef.current.abort();
      syncAbortControllerRef.current = null;
      setIsSyncing(false);
      setSyncingRows(new Set());
      toast.info("Interface synchronization aborted");
    }
  };
  
  /**
   * Checks if a given interface is currently being synced
   */
  const isSyncingInterface = (interfaceId: string) => {
    return syncingRows.has(interfaceId);
  };
  
  return {
    isSyncing,
    syncingRows,
    syncInterface,
    syncAll,
    abortSync,
    isSyncingInterface,
    lastManualSyncTime: lastManualSyncRef
  };
} 