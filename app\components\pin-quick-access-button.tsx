import { useState, useEffect, useCallback } from 'react';
import { Pin, PinOff } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { toast } from 'sonner';
import { useLocation } from '@remix-run/react';
import { 
  getQuickAccessItems, 
  togglePinnedStatus, 
  addPinnedQuickAccessItem, 
  type QuickAccessItem
} from '~/utils/quick-access';

export interface PinQuickAccessButtonProps {
  id: string;
  name: string;
  type: QuickAccessItem['type'];
  path: string;
  variant?: 'default' | 'outline';
  size?: 'default' | 'sm';
  className?: string;
  showToast?: boolean;
  comparePathsIgnoringParamOrder?: boolean;
  applicationId?: string;
  interfaceId?: string;
}

export function PinQuickAccessButton({
  id,
  name,
  type,
  path,
  variant = 'outline',
  size = 'sm',
  className = '',
  showToast = true,
  comparePathsIgnoringParamOrder = true,
  applicationId,
  interfaceId,
}: PinQuickAccessButtonProps) {
  const [isPinned, setIsPinned] = useState(false);
  const [visualFeedback, setVisualFeedback] = useState(false);
  const location = useLocation();

  // Helper function to compare paths while ignoring parameter order
  const comparePaths = useCallback((path1: string, path2: string): boolean => {
    if (!comparePathsIgnoringParamOrder) {
      return path1 === path2;
    }
    
    // Extract the base path and parameters
    const [basePath1, paramStr1 = ''] = path1.split('?');
    const [basePath2, paramStr2 = ''] = path2.split('?');
    
    // If base paths don't match, return false
    if (basePath1 !== basePath2) return false;
    
    // Convert parameters to objects for easier comparison
    const params1 = new URLSearchParams(paramStr1);
    const params2 = new URLSearchParams(paramStr2);
    
    // Get all parameter entries from both URLs
    const entries1 = Array.from(params1.entries());
    const entries2 = Array.from(params2.entries());
    
    // If different numbers of parameters, they can't match
    if (entries1.length !== entries2.length) return false;
    
    // Check if all parameters in params1 exist in params2 with the same values
    // This handles multi-value parameters as well
    for (const [key, value] of entries1) {
      const values2 = params2.getAll(key);
      if (!values2.includes(value)) return false;
    }
    
    return true;
  }, [comparePathsIgnoringParamOrder]);

  // Check if the item is pinned on mount and when props change
  const checkPinStatus = useCallback(() => {
    if (typeof window === 'undefined') return;
    
    try {
      const items = getQuickAccessItems();
      const isPinned = items.some(item => 
        item.type === type && 
        item.id === id && 
        item.pinned &&
        (comparePathsIgnoringParamOrder ? comparePaths(item.path, path) : item.path === path)
      );
      setIsPinned(isPinned);
    } catch (error) {
      console.error("Error checking pin status:", error);
    }
  }, [id, type, path, comparePaths, comparePathsIgnoringParamOrder]);

  useEffect(() => {
    checkPinStatus();
    
    // Set up a storage event listener to detect changes from other tabs
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'oms_quick_access') {
        checkPinStatus();
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [checkPinStatus]);

  const handleTogglePin = () => {
    if (isPinned) {
        // Instead of toggling status, directly remove the item
        try {
          const currentItems = getQuickAccessItems();
          
          // Filter out this item entirely (remove it completely)
          const updatedItems = currentItems.filter(item => 
            !(item.type === type && item.id === id && 
              (comparePathsIgnoringParamOrder ? comparePaths(item.path, path) : item.path === path))
          );
          
          // Save back to localStorage
          localStorage.setItem('oms_quick_access', JSON.stringify(updatedItems));
          
          setIsPinned(false);
          
          // Visual feedback
          setVisualFeedback(true);
          setTimeout(() => setVisualFeedback(false), 1000);
          
          // Show toast notification
          if (showToast) {
            toast.success('Removed from Quick Access', {
              description: name
            });
          }
        } catch (error) {
          console.error('Error removing item from quick access:', error);
        }
      } else {
      // Get current org level parameters from URL
      const searchParams = new URLSearchParams(location.search);
      const orgLevel4 = searchParams.get('level4') || undefined;
      const orgLevel5 = searchParams.get('level5') || undefined;
      
      // Add to pinned items with org level information
      addPinnedQuickAccessItem({
        id,
        name,
        type,
        path,
        applicationId,
        interfaceId,
        orgLevel4,
        orgLevel5
      });
      
      setIsPinned(true);
      
      // Visual feedback
      setVisualFeedback(true);
      setTimeout(() => setVisualFeedback(false), 1000);
      
      // Show toast notification
      if (showToast) {
        toast.success('Added to Quick Access', {
          description: name
        });
      }
    }
  };

  return (
    <Button 
      variant="ghost" 
      size={size} 
      onClick={handleTogglePin}
      title={isPinned ? "Remove from quick access" : "Add to quick access"}
      className={`transition-colors ${visualFeedback ? 'bg-green-100 text-green-700' : ''} ${className}`}
    >
      {isPinned ? (
        <>
          <PinOff className="mr-2 h-4 w-4" />
        </>
      ) : (
        <>
          <Pin className="mr-2 h-4 w-4" />
        </>
      )}
    </Button>
  );
} 