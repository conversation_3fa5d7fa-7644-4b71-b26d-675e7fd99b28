import { CommonSearchForm, type CommonSearchFormProps } from "~/components/common-search-form";

/**
 * Props for the application search form component
 */
interface ApplicationSearchFormProps {
  searchValue: string; 
  setSearchValue: (value: string) => void;
  hasFilters: boolean;
  clearFilters: () => void;
  formRef: React.RefObject<HTMLFormElement>;
  onSubmit: (event: React.FormEvent<HTMLFormElement>) => void;
  searchParams?: URLSearchParams;
  setSearchParams?: (params: URLSearchParams) => void;
}

/**
 * Search form component for applications
 */
export function ApplicationSearchForm({ 
  searchValue, 
  setSearchValue, 
  hasFilters,
  clearFilters, 
  formRef,
  onSubmit,
  searchParams,
  setSearchParams
}: ApplicationSearchFormProps) {
  const onSearchChange = setSearchValue;
  
  return (
    <CommonSearchForm
      searchValue={searchValue}
      onSearchChange={onSearchChange}
      hasFilters={hasFilters}
      onClearFilters={clearFilters}
      formRef={formRef}
      onSubmit={onSubmit}
      placeholder="Search by application ID or name... (press Enter to search)"
      showActiveFilters={false} // We use a separate ActiveFilters component
      searchParams={searchParams}
      setSearchParams={setSearchParams}
    />
  );
} 