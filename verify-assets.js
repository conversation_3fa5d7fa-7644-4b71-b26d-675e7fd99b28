import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Paths to check
const publicDir = path.join(__dirname, 'public');
const buildClientAssetsDir = path.join(__dirname, 'build', 'client', 'assets');

console.log('Verifying asset directories...');

// Check if directories exist
console.log(`\nChecking public directory: ${publicDir}`);
if (fs.existsSync(publicDir)) {
  console.log('✅ Public directory exists');
  const publicFiles = fs.readdirSync(publicDir);
  console.log(`Found ${publicFiles.length} files in public directory:`);
  publicFiles.forEach(file => console.log(`  - ${file}`));
} else {
  console.log('❌ Public directory does not exist');
}

console.log(`\nChecking build/client/assets directory: ${buildClientAssetsDir}`);
if (fs.existsSync(buildClientAssetsDir)) {
  console.log('✅ build/client/assets directory exists');
  const assetsFiles = fs.readdirSync(buildClientAssetsDir);
  console.log(`Found ${assetsFiles.length} files in build/client/assets directory:`);
  
  // Display first 10 files as sample
  assetsFiles.slice(0, 10).forEach(file => console.log(`  - ${file}`));
  if (assetsFiles.length > 10) {
    console.log(`  ... and ${assetsFiles.length - 10} more files`);
  }
} else {
  console.log('❌ build/client/assets directory does not exist');
}

// Check for specific files mentioned in the error logs
const specificFiles = [
  'tailwind-BN1N3yEO.css',
  'entry.client-2d2WHheQ.js',
  'jsx-runtime-BoP3Okkf.js',
  'components-D4Fayz6G.js',
  'popover-Brs4iTGv.js',
  'index-Z_O0UVu5.js',
  'root-QE6UOZjE.js',
  '_index-COi4C9Nv.js'
];

console.log('\nChecking for specific files mentioned in error logs:');
specificFiles.forEach(file => {
  const filePath = path.join(buildClientAssetsDir, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} exists in build/client/assets`);
  } else {
    console.log(`❌ ${file} is missing from build/client/assets`);
  }
}); 