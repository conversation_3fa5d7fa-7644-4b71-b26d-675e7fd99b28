#!/usr/bin/env node

/**
 * Database migration helper script
 * 
 * This script automates common database migration tasks:
 * - Backing up your database before migrations
 * - Generating migration files based on schema changes
 * - Applying migrations to update the database
 * 
 * Usage:
 *   node scripts/migrate.js [generate|apply|backup]
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Default database path (from .env)
const DEFAULT_DB_PATH = 'dev.db';

// Get database path from env file or use default
function getDatabasePath() {
  try {
    const envContent = fs.readFileSync('.env', 'utf8');
    const match = envContent.match(/DATABASE_URL=["']?file:\.\/([^"'\s]+)/);
    return match ? match[1] : DEFAULT_DB_PATH;
  } catch (error) {
    console.warn('Could not read .env file, using default database path');
    return DEFAULT_DB_PATH;
  }
}

// Backup the database
function backupDatabase() {
  const dbPath = getDatabasePath();
  const backupPath = `${dbPath}.backup-${new Date().toISOString().replace(/[:.]/g, '-')}`;
  
  if (!fs.existsSync(dbPath)) {
    console.warn(`Database file ${dbPath} not found, skipping backup`);
    return;
  }
  
  try {
    fs.copyFileSync(dbPath, backupPath);
    console.log(`✅ Database backed up to ${backupPath}`);
  } catch (error) {
    console.error('❌ Failed to backup database:', error.message);
    process.exit(1);
  }
}

// Generate migration files
function generateMigrations() {
  try {
    console.log('Generating migration files...');
    execSync('npx drizzle-kit generate', { stdio: 'inherit' });
    console.log('✅ Migration files generated');
  } catch (error) {
    console.error('❌ Failed to generate migrations:', error.message);
    process.exit(1);
  }
}

// Apply migrations
function applyMigrations() {
  try {
    console.log('Applying migrations...');
    execSync('npx drizzle-kit migrate', { stdio: 'inherit' });
    console.log('✅ Migrations applied successfully');
  } catch (error) {
    console.error('❌ Failed to apply migrations:', error.message);
    process.exit(1);
  }
}

// Main function
function main() {
  const command = process.argv[2] || 'help';

  switch (command) {
    case 'generate':
      generateMigrations();
      break;
    
    case 'apply':
      backupDatabase();
      applyMigrations();
      break;
      
    case 'backup':
      backupDatabase();
      break;
      
    case 'all':
      backupDatabase();
      generateMigrations();
      applyMigrations();
      break;
      
    case 'help':
    default:
      console.log(`
Database Migration Helper

Usage:
  node scripts/migrate.js [command]

Commands:
  generate    Generate migration files based on schema changes
  apply       Apply pending migrations to update the database (includes backup)
  backup      Create a backup of the current database
  all         Perform backup, generate, and apply in sequence
  help        Show this help message
`);
      break;
  }
}

// Run the script
main(); 