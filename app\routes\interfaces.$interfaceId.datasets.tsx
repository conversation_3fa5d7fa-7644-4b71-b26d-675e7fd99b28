import { json, type LoaderFunction } from "@remix-run/node";
import { Outlet, useLoaderD<PERSON>, useNavi<PERSON>, use<PERSON><PERSON><PERSON>, <PERSON>, useParams, useLocation } from "@remix-run/react";
import { useState, useEffect } from "react";
import { DataTable } from "~/modules/interfaces/components/dataset-sheet/data-table";
import { SLAForm } from "~/modules/interfaces/components/dataset-sheet/sla-form";
import { useSetPageTitle } from "~/hooks/use-set-page-title";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "~/components/ui/breadcrumb"
import type { DatasetWithSLA } from "~/db/types/datasets";
import { DatasetModel } from "~/models/dataset.server";
import { eq } from "drizzle-orm";
import { db } from "~/db/db.server";
import { interfaces as interfacesTable } from "~/db/schema/interfaces";
import { handleApiError } from "~/utils/error-handling.server";
import { InterfaceModel } from "~/models/interface.server";
import { getPathWithFilters } from "~/utils/quick-access";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { Label } from "~/components/ui/label";
import { toast } from "sonner";

export type RAGStatus = 'red' | 'amber' | 'green';

interface DatasetWithRAG extends DatasetWithSLA {
  ragStatus: RAGStatus;
}

interface LoaderData {
  datasets: DatasetWithRAG[];
  interfaceName: string;
  error?: string;
}

function calculateRAGStatus(dataset: DatasetWithSLA): RAGStatus {
  // Use UTC dates for consistency with RRule TZID=UTC
  const now = new Date();
  const expectedArrivalTime = dataset.expectedArrivalTime ? new Date(dataset.expectedArrivalTime) : null;
  const lastArrivalTime = dataset.lastArrivalTime ? new Date(dataset.lastArrivalTime) : null;

  if (!expectedArrivalTime) return 'green';

  const today = new Date(Date.UTC(
    now.getUTCFullYear(),
    now.getUTCMonth(),
    now.getUTCDate()
  ));

  const isExpectedToday = expectedArrivalTime.getUTCDate() === today.getUTCDate() &&
                         expectedArrivalTime.getUTCMonth() === today.getUTCMonth() &&
                         expectedArrivalTime.getUTCFullYear() === today.getUTCFullYear();

  // Red: SLA breached - either completed after expected time or overdue
  if (expectedArrivalTime < now && isExpectedToday && 
      (!lastArrivalTime || 
       expectedArrivalTime < lastArrivalTime || 
       lastArrivalTime.getUTCDate() !== today.getUTCDate())) {
    return 'red';
  }

  // Amber: Within 30 minutes of expected time and not completed
  const thirtyMinutes = 30 * 60 * 1000; // 30 minutes in milliseconds
  if ((expectedArrivalTime.getTime() - now.getTime() < thirtyMinutes) && 
      (!lastArrivalTime || lastArrivalTime.getUTCDate() !== today.getUTCDate())) {
    return 'amber';
  }

  // Green: All other cases
  return 'green';
}

export const loader: LoaderFunction = async ({ params, request }) => {
  const { interfaceId } = params;
  
  if (!interfaceId) {
    return json({ error: "Interface ID is required" }, { status: 400 });
  }

  try {
    const [datasets, interface_] = await Promise.all([
      DatasetModel.findByInterfaceId(interfaceId),
      InterfaceModel.findById(interfaceId)
    ]);

    if (!interface_) {
      return json({ error: "Interface not found" }, { status: 404 });
    }

    // Calculate RAG status for each dataset
    const datasetsWithRAG = datasets.map(dataset => ({
      ...dataset,
      ragStatus: calculateRAGStatus(dataset)
    }));

    return json<LoaderData>({ 
      datasets: datasetsWithRAG,
      interfaceName: interface_.interfaceName || ''
    });
  } catch (error) {
    return handleApiError(error);
  }
};

export default function DatasetsRoute() {
  const { datasets, interfaceName, error } = useLoaderData<LoaderData>();
  const [selectedDatasets, setSelectedDatasets] = useState<string[]>([]);
  const [slaDialogOpen, setSlaDialogOpen] = useState(false);
  const [ragInclusionDialogOpen, setRagInclusionDialogOpen] = useState(false);
  const [ragInclusionValue, setRagInclusionValue] = useState<boolean>(true);
  const params = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const fetcher = useFetcher();
  
  // Set page title
  useSetPageTitle("Datasets", `View and manage datasets for interface ${interfaceName}`);
  
  const handleSLAUpdate = async (sla: string) => {
    const formData = new FormData();
    selectedDatasets.forEach(id => formData.append("datasetIds[]", id));
    formData.append("sla", sla);

    fetcher.submit(formData, {
      method: "POST",
      action: "/api/datasets/sla",
    });
    setSlaDialogOpen(false);
    setSelectedDatasets([]);
  };

  const handleToggleRagInclusion = (datasetName: string, include: boolean) => {
    const formData = new FormData();
    formData.append("datasetName", datasetName);
    formData.append("include", include.toString());
    
    fetcher.submit(formData, {
      method: "POST",
      action: "/api/datasets/rag-inclusion",
    });
  };

  const handleBulkToggleRagInclusion = () => {
    setRagInclusionDialogOpen(true);
  };

  const submitBulkRagInclusionUpdate = () => {
    const formData = new FormData();
    
    selectedDatasets.forEach(datasetId => {
      formData.append("datasetIds[]", datasetId);
    });
    
    formData.append("include", ragInclusionValue.toString());
    
    fetcher.submit(formData, {
      method: "POST",
      action: "/api/datasets/rag-inclusion",
    });
    
    setRagInclusionDialogOpen(false);
  };

  const handleViewEvents = (datasetName: string) => {
    // Base URL for the events view
    const eventsUrl = `/interfaces/${params.interfaceId}/datasets/${encodeURIComponent(datasetName)}/events`;
    
    // Use getPathWithFilters to preserve all filter parameters
    const urlWithFilters = getPathWithFilters(eventsUrl, location);
    
    // Navigate to the events page with all filter parameters preserved
    navigate(urlWithFilters);
  };

  const handleNavigateToInterface = (preserveParams = false) => {
    const baseUrl = '/interfaces';
    
    if (preserveParams) {
      // Use getPathWithFilters to preserve all filter parameters
      const urlWithFilters = getPathWithFilters(baseUrl, location);
      navigate(urlWithFilters);
    } else {
      navigate(baseUrl);
    }
  };

  const handleNavigateToDataset = (preserveParams = false) => {
    const baseUrl = `/interfaces/${params.interfaceId}/datasets`;
    
    if (preserveParams) {
      // Use getPathWithFilters to preserve all filter parameters
      const urlWithFilters = getPathWithFilters(baseUrl, location);
      navigate(urlWithFilters);
    } else {
      navigate(baseUrl);
    }
  };

  // Get the current path with filters for pinning
  const getCurrentPath = () => {
    const baseUrl = `/interfaces/${params.interfaceId}/datasets`;
    return getPathWithFilters(baseUrl, location);
  };

  const content = location.pathname !== `/interfaces/${params.interfaceId}/datasets` ? (
    <Outlet context={{ 
      interfaceName,
      navigateToInterface: handleNavigateToInterface,
      navigateToDataset: handleNavigateToDataset 
    }} />
  ) : (
    <>
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <Link 
              to={getPathWithFilters('/interfaces', location)}
              className="hover:underline"
            >
              Interfaces
            </Link>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Datasets for {interfaceName}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex justify-between items-center">
        {error && (
          <p className="text-sm text-red-500 mt-2">
            {error}
          </p>
        )}
      </div>

      <DataTable
        data={datasets}
        selectedDatasets={selectedDatasets}
        onSelectionChange={setSelectedDatasets}
        onViewEvents={handleViewEvents}
        onUpdateSLA={() => setSlaDialogOpen(true)}
        onToggleRagInclusion={handleToggleRagInclusion}
        onBulkToggleRagInclusion={handleBulkToggleRagInclusion}
      />

      <SLAForm 
        isOpen={slaDialogOpen}
        onClose={() => setSlaDialogOpen(false)}
        onSubmit={handleSLAUpdate}
        selectedCount={selectedDatasets.length}
        isSubmitting={fetcher.state !== "idle"}
        existingSla={
          selectedDatasets.length > 0
            ? datasets.find(d => d.datasetName === selectedDatasets[0])?.sla || undefined
            : undefined
        }
      />

      <Dialog open={ragInclusionDialogOpen} onOpenChange={setRagInclusionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update RAG Status Inclusion</DialogTitle>
            <DialogDescription>
              Choose whether to include or exclude the selected datasets from RAG status calculations.
            </DialogDescription>
          </DialogHeader>
          
          <RadioGroup
            value={ragInclusionValue ? "include" : "exclude"}
            onValueChange={(value) => setRagInclusionValue(value === "include")}
            className="mt-4"
          >
            <div className="flex items-center space-x-2 mb-2">
              <RadioGroupItem value="include" id="include" />
              <Label htmlFor="include">Include in RAG status</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="exclude" id="exclude" />
              <Label htmlFor="exclude">Exclude from RAG status</Label>
            </div>
          </RadioGroup>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setRagInclusionDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button 
              onClick={submitBulkRagInclusionUpdate}
            >
              Update {selectedDatasets.length} dataset{selectedDatasets.length !== 1 ? 's' : ''}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );

  return (
    <div className="space-y-4">
      {content}
    </div>
  );
} 