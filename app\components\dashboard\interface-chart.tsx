import { useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from "~/components/ui/card";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip
} from "recharts";

interface InterfaceChartProps {
  stats: {
    red: number;
    amber: number;
    green: number;
  };
}

export function InterfaceChart({ stats }: InterfaceChartProps) {
  const chartData = useMemo(() => [
    {
      name: "SLA Breached",
      value: stats.red,
      fill: "#ef4444" // Red color
    },
    {
      name: "SLA at Risk",
      value: stats.amber,
      fill: "#f59e0b" // Amber color
    },
    {
      name: "On Schedule",
      value: stats.green,
      fill: "#10b981" // Green color
    }
  ], [stats.red, stats.amber, stats.green]);

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-sm font-small"></CardTitle>
      </CardHeader>
      <CardContent className="px-2">
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%" key="interface-chart">
            <BarChart
              data={chartData}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 30,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} horizontal={false} />
              <XAxis 
                dataKey="name" 
                tick={{ fontSize: 12 }}
                tickLine={false}
                axisLine={true}
              />
              <Bar 
                dataKey="value" 
                radius={[4, 4, 0, 0]} 
                fill="#8884d8"
                label={{ position: 'top', fontSize: 12 }}
              />
              <Tooltip cursor={{ fill: 'transparent' }} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
} 