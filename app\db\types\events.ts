import { z } from "zod"
import type { createInsertSchema } from "drizzle-zod"
import type { events } from "../schema/events"

export type InterfaceEventRawJson = {
  businessDate: string
  createdDateTime: string
  interface: {
    fromApp: {
      id: string
      name: string
    }
    toApp: {
      id: string
      name: string
    }
  }
}

export type InterfaceEvent = {
  msgId: string
  businessDate: string
  createdDateTime: Date
  datasetName: string
  endNodeId: string
  endNodeName: string
  frequency: string
  rawJson: string // Stored as JSON string
  startNodeId: string
  startNodeName: string
  valid: string
  reportedForId: string
  reportedForName: string
  milestoneType: string
}

export type NewInterfaceEvent = typeof events.$inferInsert
export type InsertInterfaceEvent = z.infer<ReturnType<typeof createInsertSchema<typeof events>>>

// API response type from DLAS
export interface DLASEventIDsResponse {
  events: string[];
  interface_serial: number;
  last_update_date: string;
  log_date: string;
  receiverapplicationid: string;
  senderapplicationid: string;
}

export type DLASInterfaceEvent = {
  msgId: string
  businessDate: string
  createdDateTime: string // Format: "YYYY-MM-DDThh:mm:ss.sssUTC"
  datasetName: string
  endNodeId: string
  endNodeName: string
  frequency: string
  rawJson: InterfaceEventRawJson
  startNodeId: string
  startNodeName: string
  valid: string
  reportedForId: string
  reportedForName: string
  milestoneType: string
} 