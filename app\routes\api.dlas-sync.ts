import { json, type ActionFunction, type LoaderFunction } from "@remix-run/node";
import { getDLASSyncService, getNextScheduledRunTime } from "~/services/dlas-sync.server";
import { handleApiError } from "~/utils/error-handling.server";

/**
 * Loader for fetching current DLAS sync status
 */
export const loader: LoaderFunction = async ({ request }) => {
  try {
    const syncService = getDLASSyncService();
    const { state, progress } = syncService.getSyncStatus();
    const config = syncService.getConfig();
    
    // Get the next scheduled run time
    const nextScheduledRunTime = getNextScheduledRunTime();
    
    // Prepare the response data
    const responseData = {
      state,
      progress: {
        ...progress,
        // Convert Date objects to ISO strings for JSON serialization
        startTime: progress.startTime ? progress.startTime.toISOString() : null,
        endTime: progress.endTime ? progress.endTime.toISOString() : null,
      },
      config,
      nextScheduledRunTime: nextScheduledRunTime.toISOString(),
      scheduledSyncInfo: {
        hour: 6,
        minute: 0,
        description: 'Daily sync at 6:00 AM'
      }
    };
    
    return json(responseData);
  } catch (error) {
    return handleApiError(error);
  }
};

/**
 * Action for controlling DLAS sync operations
 * Supported actions:
 * - start: Start a new sync operation
 * - stop: Stop the current sync operation
 */
export const action: ActionFunction = async ({ request }) => {
  try {
    const formData = await request.formData();
    const action = formData.get('action')?.toString();
    const applicationId = formData.get('applicationId')?.toString();
    const date = formData.get('date')?.toString();
 
    if (!action) {
      return json(
        { error: 'Missing action parameter' },
        { status: 400 }
      );
    }
    
    const syncService = getDLASSyncService();
    
    switch (action) {
      case 'start':
        const result = await syncService.startSync({
          applicationId,
          date
        });
        return json({
          success: result.success,
          message: result.message || 'Sync started'
        });
        
      case 'stop':
        const stopResult = await syncService.stopSync();
        return json({
          success: stopResult.success,
          message: stopResult.message || 'Sync stopped'
        });
        
      default:
        return json(
          { error: 'Invalid action. Supported actions are: start, stop' },
          { status: 400 }
        );
    }
  } catch (error) {
    return handleApiError(error);
  }
};
