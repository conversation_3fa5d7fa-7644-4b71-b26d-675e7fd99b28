import { json, type ActionFunction } from "@remix-run/node";
import { InterfaceModel } from "~/models/interface.server";
import { ApplicationModel } from "~/models/application.server";
import type { RAGStatusResult } from "~/models/interface/interface-types.server";

/**
 * API endpoint to fetch RAG status for quick access items (both interfaces and applications)
 */
export const action: ActionFunction = async ({ request }) => {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const formData = await request.formData();
    const interfaceIds = formData.getAll("interfaceIds[]").map(id => id.toString());
    const applicationIds = formData.getAll("applicationIds[]").map(id => id.toString());

    const results: {
      interfaces: Record<string, RAGStatusResult>;
      applications: Record<string, { counts: { red: number; amber: number; green: number } }>;
    } = {
      interfaces: {},
      applications: {}
    };

    // Fetch RAG status for interfaces and applications in parallel
    const [interfaceResults, applicationResults] = await Promise.all([
      // Only fetch interface RAG status if we have interface IDs
      interfaceIds.length > 0 
        ? InterfaceModel.calculateBulkRagStatus(interfaceIds)
        : Promise.resolve({}),
      
      // Only fetch application RAG status if we have application IDs
      applicationIds.length > 0
        ? ApplicationModel.getBatchInterfaceRagStatus(applicationIds)
        : Promise.resolve({})
    ]) as [
      Record<string, RAGStatusResult>,
      Record<string, { red: number; amber: number; green: number }>
    ];

    // Format interface results
    results.interfaces = interfaceResults;

    // Format application results
    for (const [appId, ragStatus] of Object.entries(applicationResults)) {
      results.applications[appId] = {
        counts: ragStatus
      };
    }

    return json({ success: true, results });
  } catch (error) {
    console.error("Error fetching RAG status for quick access:", error);
    return json(
      { error: "Failed to fetch RAG status", message: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}; 