# OMS v3 - Operations Management System

An advanced system for monitoring and managing application interfaces, datasets, and events across your organization.

## Features

- **Dashboard Overview**: Monitor applications, interfaces, and datasets with real-time statistics
- **Interface Management**: Track communication channels between applications with SLA monitoring
- **Dataset Tracking**: Monitor datasets associated with interfaces
- **Event Logging**: Track events for interfaces and datasets
- **Organization Structure**: View and filter by organizational hierarchy

## Technology Stack

- **Framework**: [Remix](https://remix.run/docs) with React
- **Styling**: [Tailwind CSS](https://tailwindcss.com/) with [Shadcn UI](https://ui.shadcn.com/) components
- **Database**: SQLite with [Drizzle ORM](https://orm.drizzle.team/)
- **Charts**: [Recharts](https://recharts.org/) for data visualization
- **Form Handling**: [React Hook Form](https://react-hook-form.com/) with [Zod](https://zod.dev/) validation
- **API Integration**: Custom API utilities with caching and retry mechanisms

## Development

Start the development server:

```sh
npm run dev
```

This starts your app in development mode, rebuilding assets on file changes.

## Database

This project uses SQLite with Drizzle ORM. The database schema is defined in `app/db/schema/` and migrations are managed with Drizzle Kit.

## Environment Variables

Copy `.env.example` to `.env` and configure the required environment variables:

```sh
cp .env.example .env
```

## Deployment

Build the app for production:

```sh
npm run build
```

Run the app in production mode:

```sh
npm start
```

## Project Structure

- `app/`: Application source code
  - `components/`: Reusable UI components
  - `db/`: Database configuration and schema
  - `models/`: Data models and business logic
  - `routes/`: Application routes and page components
  - `services/`: External service integrations
  - `utils/`: Utility functions and helpers

## Recent Updates

- Refactored API and sync utilities for improved maintainability
- Consolidated utility structure following Remix conventions
- Enhanced sync utilities with improved memory monitoring
- Added proper server-side code isolation with `.server.ts` suffix

## Additional Documentation

See `docs/` directory for more detailed documentation on specific features and components.
