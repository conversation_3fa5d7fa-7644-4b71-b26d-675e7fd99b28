import { sql } from 'drizzle-orm';
import { text, integer, sqliteTable, index } from 'drizzle-orm/sqlite-core';

// Application table schema
export const applications = sqliteTable('applications', {
  // Primary Key
  applicationInstanceId: text('application_instance_id').primaryKey(),
  
  // Basic Information
  name: text('name').notNull(),
  shortName: text('short_name'),
  description: text('description'),
  criticality: text('criticality'),
  status: text('status'),
  strategicStatus: text('strategic_status'),
  
  // IT Organization (Flattened)
  orgLevel2: text('org_level2').notNull(),
  orgLevel3: text('org_level3').notNull(),
  orgLevel4: text('org_level4').notNull(),
  orgLevel5: text('org_level5').notNull(),
  
  // PLADA Information
  pladaServiceId: text('plada_service_id').notNull(),
  pladaServiceName: text('plada_service_name'),
  
  // Owner Information (Flattened - First Entry)
  ownerPsid: text('owner_psid'),
  ownerDisplayName: text('owner_display_name'),
  ownerEmail: text('owner_email'),
  
  // Delegate Information (Flattened - First Entry)
  delegatePsid: text('delegate_psid'),
  delegateDisplayName: text('delegate_display_name'),
  delegateEmail: text('delegate_email'),
  
  // Metadata
  createdAt: integer('created_at', { mode: 'timestamp' })
    .default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' })
    .default(sql`CURRENT_TIMESTAMP`),
    
  // Derived counts
  interfaceCount: integer('interface_count').default(0),
}, (table) => ({
  statusIdx: index('status_idx').on(table.status),
  orgIdx: index('org_idx').on(table.orgLevel4, table.orgLevel5),
  pladaServiceIdx: index('plada_service_idx').on(table.pladaServiceId),
})); 