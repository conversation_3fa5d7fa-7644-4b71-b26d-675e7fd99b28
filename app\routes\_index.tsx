import { redirect } from "@remix-run/node";
import type { LoaderFunctionArgs } from "@remix-run/node";

/**
 * Root route that redirects to the dashboard
 */
export async function loader({ request }: LoaderFunctionArgs) {
  // Redirect to the dashboard route
  return redirect("/dashboard");
}

// No component needed as we're redirecting in the loader
export default function Index() {
  return null;
}
