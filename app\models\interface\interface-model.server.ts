import { db } from "~/db/db.server";
import { interfaces } from "~/db/schema/interfaces";
import { datasets } from "~/db/schema/datasets";
import { applications } from "~/db/schema";
import { eq, and, or, desc, inArray, like, sql, lt, isNull } from "drizzle-orm";
import type { Interface } from "~/db/types/interfaces";
import type { InterfaceSearchOptions, RAGStatusResult, InterfaceWithRagStatus } from "./interface-types.server";
import { DatasetModel } from "~/models/dataset.server";
import { DLASService } from "~/services/dlas.server";
import { getDatasets, setDatasets, getRagStatus, setRagStatus } from "./interface-cache.server";
import { FindAllOptions } from ".";

/**
 * InterfaceModel - Main class for interface operations
 * 
 * This class provides core operations for finding and managing interfaces
 * More specialized operations are delegated to helper classes
 */
export class InterfaceModel {
  /**
   * Find a single interface by ID
   */
  static async findById(interfaceId: string): Promise<Interface | null> {
    const results = await db
      .select()
      .from(interfaces)
      .where(eq(interfaces.omsInterfaceId, interfaceId))
      .limit(1);
    return results[0] ?? null;
  }

  /**
   * Retrieves all interfaces from the database with optional filters
   */
  static async findAll(options: InterfaceSearchOptions = {}): Promise<Interface[]> {
    const { orgLevel4, orgLevel5, appId, applicationIds, searchQuery } = options;
    const conditions = [];
    if (orgLevel4) {
      conditions.push(eq(applications.orgLevel4, orgLevel4));
      if (orgLevel5) {
        conditions.push(eq(applications.orgLevel5, orgLevel5));
      }
    }
    if (appId) {
      conditions.push(
        or(
          eq(interfaces.sendAppId, appId),
          eq(interfaces.receivedAppId, appId)
        )
      );
    }
    if (applicationIds?.length) {
      conditions.push(
        or(
          inArray(interfaces.sendAppId, applicationIds),
          inArray(interfaces.receivedAppId, applicationIds)
        )
      );
    }
    if (searchQuery) {
      conditions.push(
        or(
          like(interfaces.omsInterfaceId, `%${searchQuery}%`),
          like(interfaces.interfaceName, `%${searchQuery}%`),
          like(interfaces.sendAppName, `%${searchQuery}%`),
          like(interfaces.receivedAppName, `%${searchQuery}%`)
        )
      );
    }
    const senderQuery = db
      .select()
      .from(interfaces)
      .leftJoin(
        applications,
        eq(interfaces.sendAppId, applications.applicationInstanceId)
      );
    const receiverQuery = db
      .select()
      .from(interfaces)
      .leftJoin(
        applications,
        eq(interfaces.receivedAppId, applications.applicationInstanceId)
      );
    const [senderResults, receiverResults] = await Promise.all([
      conditions.length > 0
        ? senderQuery.where(and(...conditions)).orderBy(desc(interfaces.updatedAt))
        : senderQuery.orderBy(desc(interfaces.updatedAt)),
      conditions.length > 0
        ? receiverQuery.where(and(...conditions)).orderBy(desc(interfaces.updatedAt))
        : receiverQuery.orderBy(desc(interfaces.updatedAt))
    ]);
    const uniqueInterfaces = new Map<string, Interface>();
    senderResults.forEach(result => {
      if (result.interfaces) {
        uniqueInterfaces.set(result.interfaces.omsInterfaceId, result.interfaces);
      }
    });
    receiverResults.forEach(result => {
      if (result.interfaces) {
        uniqueInterfaces.set(result.interfaces.omsInterfaceId, result.interfaces);
      }
    });
    return Array.from(uniqueInterfaces.values());
  }

  /**
   * Gets dataset names for an interface using the cache
   */
  static async getInterfaceDatasetNames(interfaceId: string): Promise<string[]> {
    const cached = getDatasets(interfaceId);
    if (cached) return cached;

    const datasetsResult = await DatasetModel.getDatasetsByInterfaceId(interfaceId);
    const datasetNames = datasetsResult.map(dataset => dataset.name);
    setDatasets(interfaceId, datasetNames);
    return datasetNames;
  }

  /**
   * Update dataset count for an interface
   */
  static async updateDatasetCount(interfaceId: string): Promise<void> {
    try {
      const result = await db
        .select({ count: sql<number>`count(*)` })
        .from(datasets)
        .where(eq(datasets.omsInterfaceId, interfaceId));
      
      const count = result[0]?.count || 0;
      await db
        .update(interfaces)
        .set({ datasetCount: count, updatedAt: new Date() })
        .where(eq(interfaces.omsInterfaceId, interfaceId));
      
      console.log(`Updated dataset count for interface ${interfaceId}: ${count} datasets`);
    } catch (error) {
      console.error(`Failed to update dataset count for interface ${interfaceId}:`, error);
      throw error;
    }
  }

  static async transformDatasetsChunk(
    datasetChunk: any[],
    datasetToInterfaceMap: Map<string, { omsInterfaceId: string; interfaceCount: number; firstInterfaceName: string; }>,
    skippedDatasets: string[],
    multiRefDatasets: string[]
  ): Promise<any[]> {
    const transformedDatasets: any[] = [];
    for (const dlasDataset of datasetChunk) {
      const mapping = datasetToInterfaceMap.get(dlasDataset.DatasetName);
      if (!mapping) {
        skippedDatasets.push(dlasDataset.DatasetName);
        continue;
      }
      if (mapping.interfaceCount > 1) {
        multiRefDatasets.push(dlasDataset.DatasetName);
      }
      const transformedDataset = DLASService.transformDataset(
        dlasDataset,
        mapping.omsInterfaceId
      );
      transformedDatasets.push(transformedDataset);
    }
    return transformedDatasets;
  }

  static logDatasetWarnings(
    skippedDatasets: string[],
    multiRefDatasets: string[],
    logger: any
  ): void {
    if (skippedDatasets.length > 0) {
      logger.warn(
        `${skippedDatasets.length} datasets have no matching interface and were skipped.`,
        skippedDatasets.length <= 5 ? skippedDatasets : `First 5: ${skippedDatasets.slice(0, 5).join(', ')}...`
      );
    }
    if (multiRefDatasets.length > 0) {
      logger.warn(
        `${multiRefDatasets.length} datasets are referenced by multiple interfaces.`,
        multiRefDatasets.length <= 5 ? multiRefDatasets : `First 5: ${multiRefDatasets.slice(0, 5).join(', ')}...`
      );
    }
  }

  // Application-related methods
  static async countByApplicationId(applicationId: string): Promise<number> {
    const result = await db
      .select({ count: sql<number>`count(*)` })
      .from(interfaces)
      .where(
        or(
          eq(interfaces.sendAppId, applicationId),
          eq(interfaces.receivedAppId, applicationId)
        )
      );
    return result[0]?.count || 0;
  }

  static async countByApplicationIds(applicationIds: string[]): Promise<Record<string, number>> {
    if (!applicationIds.length) return {};
    const senderResults = await db
      .select({ applicationId: interfaces.sendAppId, count: sql<number>`count(*)` })
      .from(interfaces)
      .where(inArray(interfaces.sendAppId, applicationIds))
      .groupBy(interfaces.sendAppId);
    const receiverResults = await db
      .select({ applicationId: interfaces.receivedAppId, count: sql<number>`count(*)` })
      .from(interfaces)
      .where(inArray(interfaces.receivedAppId, applicationIds))
      .groupBy(interfaces.receivedAppId);
    const counts: Record<string, number> = {};
    applicationIds.forEach(id => { counts[id] = 0; });
    senderResults.forEach(result => {
      counts[result.applicationId] = (counts[result.applicationId] || 0) + result.count;
    });
    receiverResults.forEach(result => {
      counts[result.applicationId] = (counts[result.applicationId] || 0) + result.count;
    });
    return counts;
  }

  static async updateApplicationInterfaceCount(applicationId: string): Promise<void> {
    const count = await this.countByApplicationId(applicationId);
    await db
      .update(applications)
      .set({ interfaceCount: count, updatedAt: sql`CURRENT_TIMESTAMP` })
      .where(eq(applications.applicationInstanceId, applicationId));
  }

  static async updateAllApplicationInterfaceCounts(): Promise<void> {
    const allApps = await db.select().from(applications);
    const appIds = allApps.map(app => app.applicationInstanceId);
    const counts = await this.countByApplicationIds(appIds);
    const batchSize = 100;
    const batches = [];
    for (let i = 0; i < appIds.length; i += batchSize) {
      const batch = appIds.slice(i, i + batchSize);
      batches.push(
        Promise.all(
          batch.map(appId =>
            db
              .update(applications)
              .set({ interfaceCount: counts[appId] || 0, updatedAt: sql`CURRENT_TIMESTAMP` })
              .where(eq(applications.applicationInstanceId, appId))
          )
        )
      );
    }
    await Promise.all(batches);
  }

  static async validateApplication(
    appId: string,
    orgLevel4?: string,
    orgLevel5?: string
  ): Promise<boolean> {
    const conditions = [];
    if (orgLevel4) {
      conditions.push(eq(applications.orgLevel4, orgLevel4));
      if (orgLevel5) {
        conditions.push(eq(applications.orgLevel5, orgLevel5));
      }
    }
    const app = await db
      .select()
      .from(applications)
      .where(and(
        eq(applications.applicationInstanceId, appId),
        ...conditions
      ))
      .limit(1);
    return app.length > 0;
  }

  // RAG status methods
  static async calculateBulkRagStatus(interfaceIds: string[]): Promise<Record<string, RAGStatusResult>> {
    if (interfaceIds.length === 0) return {};

    // Check cache first
    const results: Record<string, RAGStatusResult> = {};
    const uncachedIds = [];

    for (const id of interfaceIds) {
      const cached = getRagStatus(id);
      if (cached) {
        results[id] = cached;
      } else {
        uncachedIds.push(id);
        results[id] = { counts: { red: 0, amber: 0, green: 0 } };
      }
    }

    if (uncachedIds.length === 0) return results;

    const now = new Date();
    const utcNow = new Date(now.toISOString());

    // Get total counts
    const totalDatasetCounts = await db
      .select({ omsInterfaceId: datasets.omsInterfaceId, count: sql<number>`count(*)` })
      .from(datasets)
      .where(and(
        inArray(datasets.omsInterfaceId, uncachedIds),
        eq(datasets.includeInRagStatus, true)
      ))
      .groupBy(datasets.omsInterfaceId);

    const totalCounts: Record<string, number> = {};
    totalDatasetCounts.forEach(item => {
      if (item.omsInterfaceId) {
        totalCounts[item.omsInterfaceId] = item.count;
      }
    });

    // Get red counts
    const redDatasets = await db
      .select({ omsInterfaceId: datasets.omsInterfaceId, count: sql<number>`count(*)` })
      .from(datasets)
      .where(and(
        inArray(datasets.omsInterfaceId, uncachedIds),
        eq(datasets.includeInRagStatus, true),
        lt(datasets.expectedArrivalTime, utcNow),
        sql`date(${datasets.expectedArrivalTime}) = date('now')`,
        or(
          isNull(datasets.lastArrivalTime),
          lt(datasets.lastArrivalTime, datasets.expectedArrivalTime),
          sql`date(${datasets.lastArrivalTime}) != date('now')`
        )
      ))
      .groupBy(datasets.omsInterfaceId);

    redDatasets.forEach(item => {
      if (item.omsInterfaceId) {
        results[item.omsInterfaceId].counts.red = item.count;
      }
    });

    // Get amber counts
    const amberDatasets = await db
      .select({ omsInterfaceId: datasets.omsInterfaceId, count: sql<number>`count(*)` })
      .from(datasets)
      .where(and(
        inArray(datasets.omsInterfaceId, uncachedIds),
        eq(datasets.includeInRagStatus, true),
        sql`(julianday(${datasets.expectedArrivalTime}) - julianday('now')) * 24 * 60 < 30`,
        sql`(julianday(${datasets.expectedArrivalTime}) - julianday('now')) * 24 * 60 >= 0`,
        or(
          isNull(datasets.lastArrivalTime),
          sql`date(${datasets.lastArrivalTime}) != date('now')`
        )
      ))
      .groupBy(datasets.omsInterfaceId);

    amberDatasets.forEach(item => {
      if (item.omsInterfaceId) {
        results[item.omsInterfaceId].counts.amber = item.count;
      }
    });

    // Calculate green counts and cache results
    uncachedIds.forEach(id => {
      if (totalCounts[id]) {
        const total = totalCounts[id];
        const red = results[id].counts.red;
        const amber = results[id].counts.amber;
        results[id].counts.green = Math.max(0, total - (red + amber));
        
        // Cache the result
        setRagStatus(id, results[id]);
      }
    });

    return results;
  }

  /**
   * Enhance interface data with RAG status
   */
  static async enhanceWithDatasetRagStatus(interfaces: Interface[]): Promise<InterfaceWithRagStatus[]> {
    if (interfaces.length === 0) return [];
    
    const interfaceIds = interfaces.map(interface_ => interface_.omsInterfaceId);
    const ragStatuses = await this.calculateBulkRagStatus(interfaceIds);
    
    return interfaces.map(interface_ => ({
      ...interface_,
      ragStatus: ragStatuses[interface_.omsInterfaceId] || {
        counts: { red: 0, amber: 0, green: 0 }
      }
    }));
  }

  /**
   * Synchronize interfaces from DLAS for a specific application
   */
  static async syncFromDLAS(appId: string, options: FindAllOptions = {}): Promise<void> {
    // Import dynamically to avoid circular references
    const { syncFromDLAS } = await import("./interface-sync.server");
    return syncFromDLAS(appId, options);
  }

  // Sync status methods
  static async updateSyncStatus(interfaceId: string, status: string): Promise<void> {
    // Import dynamically to avoid circular references
    const { updateSyncStatus } = await import("./interface-sync.server");
    return updateSyncStatus(interfaceId, status);
  }

  /**
   * Update expected arrival times for all datasets of an interface
   */
  static async updateExpectedArrivalTimes(interfaceId: string): Promise<void> {
    return DatasetModel.updateExpectedArrivalTimes(interfaceId);
  }

} 