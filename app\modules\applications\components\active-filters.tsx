import { But<PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { X } from "lucide-react";

/**
 * Props for the active filters component
 */
interface ActiveFiltersProps { 
  searchParams: URLSearchParams; 
  setSearchParams: (params: URLSearchParams) => void;
  setSearchValue: (value: string) => void;
}

/**
 * Active filters display component 
 * Note: Consider using CommonSearchForm with showActiveFilters=true instead
 * for a more consistent UI across the application
 */
export function ActiveFilters({ 
  searchParams, 
  setSearchParams, 
  setSearchValue 
}: ActiveFiltersProps) {
  // Check if any filters are applied
  const hasFilters = searchParams.get("query") || 
    searchParams.get("level4") !== "all" || 
    searchParams.get("level5") !== "all";
    
  if (!hasFilters) return null;
  
  // Remove a specific filter
  const removeFilter = (paramName: string) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.delete(paramName);
    setSearchParams(newParams);
    
    // Reset search value if query filter is removed
    if (paramName === "query") {
      setSearchValue("");
    }
  };
  
  return (
    <div className="flex flex-wrap gap-2 mt-2">
      {searchParams.get("query") && (
        <Badge variant="secondary" className="flex items-center gap-1">
          Search: {searchParams.get("query")}
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-3 w-3 ml-1 p-0" 
            onClick={() => removeFilter("query")}
            type="button"
          >
            <X className="h-3 w-3" />
          </Button>
        </Badge>
      )}
      
      {searchParams.get("level4") !== "all" && searchParams.get("level4") && (
        <Badge variant="outline" className="flex items-center gap-1">
          Level 4: {searchParams.get("level4")}
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-3 w-3 ml-1 p-0" 
            onClick={() => removeFilter("level4")}
            type="button"
          >
            <X className="h-3 w-3" />
          </Button>
        </Badge>
      )}
      
      {searchParams.get("level5") !== "all" && searchParams.get("level5") && (
        <Badge variant="outline" className="flex items-center gap-1">
          Level 5: {searchParams.get("level5")}
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-3 w-3 ml-1 p-0" 
            onClick={() => removeFilter("level5")}
            type="button"
          >
            <X className="h-3 w-3" />
          </Button>
        </Badge>
      )}
    </div>
  );
} 