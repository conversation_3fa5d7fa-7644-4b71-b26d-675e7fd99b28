PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_interfaces` (
	`oms_interface_id` text PRIMARY KEY NOT NULL,
	`status` text NOT NULL,
	`direction` text NOT NULL,
	`eim_interface_id` text,
	`interface_name` text NOT NULL,
	`send_app_id` text NOT NULL,
	`send_app_name` text NOT NULL,
	`received_app_id` text NOT NULL,
	`received_app_name` text NOT NULL,
	`transfer_type` text NOT NULL,
	`frequency` text NOT NULL,
	`technology` text,
	`pattern` text NOT NULL,
	`related_drilldown_key` integer NOT NULL,
	`related_dataset_list` text NOT NULL,
	`demise_date` text,
	`last_sync_time` integer,
	`last_sync_status` text DEFAULT 'pending',
	`created_at` integer DEFAULT CURRENT_TIMESTAMP,
	`updated_at` integer DEFAULT CURRENT_TIMESTAMP
);
--> statement-breakpoint
INSERT INTO `__new_interfaces`("oms_interface_id", "status", "direction", "eim_interface_id", "interface_name", "send_app_id", "send_app_name", "received_app_id", "received_app_name", "transfer_type", "frequency", "technology", "pattern", "related_drilldown_key", "related_dataset_list", "demise_date", "last_sync_time", "last_sync_status", "created_at", "updated_at") SELECT "oms_interface_id", "status", "direction", "eim_interface_id", "interface_name", "send_app_id", "send_app_name", "received_app_id", "received_app_name", "transfer_type", "frequency", "technology", "pattern", "related_drilldown_key", "related_dataset_list", "demise_date", "last_sync_time", "last_sync_status", "created_at", "updated_at" FROM `interfaces`;--> statement-breakpoint
DROP TABLE `interfaces`;--> statement-breakpoint
ALTER TABLE `__new_interfaces` RENAME TO `interfaces`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE INDEX `send_app_idx` ON `interfaces` (`send_app_id`);--> statement-breakpoint
CREATE INDEX `received_app_idx` ON `interfaces` (`received_app_id`);--> statement-breakpoint
CREATE INDEX `interface_name_idx` ON `interfaces` (`interface_name`);--> statement-breakpoint
ALTER TABLE `applications` ADD `interface_count` integer DEFAULT 0;