import { SyncStatus } from '~/db/types/interfaces';

/**
 * DLAS Sync Job States
 */
export const DLASSyncState = {
  IDLE: 'idle',
  RUNNING: 'running',
  ERROR: 'error',
} as const;

export type DLASSyncState = typeof DLASSyncState[keyof typeof DLASSyncState];

/**
 * DLAS Sync Progress Status
 */
export interface DLASSyncProgress {
  state: DLASSyncState;
  // Application level tracking
  totalItems: number;
  processedItems: number;
  successCount: number;
  errorCount: number;
  errors: Array<{ message: string, item?: string }>;
  
  // Interface level tracking
  totalInterfaces: number;
  processedInterfaces: number;
  successInterfaces: number;
  
  // Dataset level tracking
  totalDatasets: number;
  processedDatasets: number;
  successDatasets: number;
  
  // Timing information
  startTime: Date | null;
  endTime: Date | null;
  elapsedTimeMs: number;
}

/**
 * Handles tracking progress for DLAS sync operations
 */
export class DLASSyncProgressTracker {
  private progress: DLASSyncProgress;
  private state: DLASSyncState;
  private broadcaster?: { broadcastSyncStatus: (progress: DLASSyncProgress) => void };

  constructor(broadcaster?: { broadcastSyncStatus: (progress: DLASSyncProgress) => void }) {
    this.state = DLASSyncState.IDLE;
    this.progress = this.resetProgress();
    this.broadcaster = broadcaster;
  }

  public setBroadcaster(broadcaster: { broadcastSyncStatus: (progress: DLASSyncProgress) => void }) {
    this.broadcaster = broadcaster;
  }

  /**
   * Reset progress to initial state
   */
  public resetProgress(): DLASSyncProgress {
    this.progress = {
      state: this.state,
      // Application level
      totalItems: 0,
      processedItems: 0,
      successCount: 0,
      errorCount: 0,
      errors: [],
      
      // Interface level
      totalInterfaces: 0,
      processedInterfaces: 0,
      successInterfaces: 0,
      
      // Dataset level
      totalDatasets: 0,
      processedDatasets: 0,
      successDatasets: 0,
      
      // Timing
      startTime: null,
      endTime: null,
      elapsedTimeMs: 0,
    };
    return this.progress;
  }

  /**
   * Start tracking a new sync operation
   */
  public startSync(totalItems: number, totalInterfaces: number, totalDatasets: number): void {
    this.state = DLASSyncState.RUNNING;
    this.resetProgress();
    
    this.progress.state = DLASSyncState.RUNNING;
    this.progress.totalItems = totalItems;
    this.progress.totalInterfaces = totalInterfaces;
    this.progress.totalDatasets = totalDatasets;
    this.progress.startTime = new Date();
    
    this.notify();
  }

  /**
   * Record a successful application
   */
  public recordApplicationSuccess(item: string, itemName: string): void {
    this.progress.successCount++;
    this.progress.processedItems++;
    this.notify();
  }

  /**
   * Record an application error
   */
  public recordApplicationError(item: string, itemName: string, error: unknown): void {
    this.progress.errorCount++;
    this.progress.processedItems++;
    
    this.addError(`Error processing application ${itemName}`, error, item);
    this.notify();
  }

  /**
   * Record a successful interface sync
   */
  public recordInterfaceSuccess(interfaceId: string, interfaceName: string): void {
    this.progress.successInterfaces++;
    this.progress.processedInterfaces++;
    this.notify();
  }

  /**
   * Record an interface sync error
   */
  public recordInterfaceError(interfaceId: string, interfaceName: string, error: unknown): void {
    this.progress.processedInterfaces++;
    
    this.addError(`Error processing interface ${interfaceName}`, error, interfaceId);
    this.notify();
  }

  /**
   * Record successful dataset sync
   */
  public recordDatasetSuccess(count: number = 1): void {
    this.progress.successDatasets += count;
    this.progress.processedDatasets += count;
    this.notify();
  }

  /**
   * Record dataset sync error
   */
  public recordDatasetError(interfaceId: string, count: number = 0, error: unknown): void {
    this.progress.processedDatasets += count;
    
    this.addError(`Error processing dataset ${interfaceId}`, error, interfaceId);
    this.notify();
  }

  /**
   * Add error details to the list
   */
  private addError(context: string, error: unknown, item?: string): void {
    let errorMessage = 'Unknown error';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    
    this.progress.errors.push({
      message: `${context}: ${errorMessage}`,
      item
    });
  }

  /**
   * Update batch progress
   */
  public updateBatch(batchIndex: number): void {
    this.notify();
  }

  /**
   * Mark the sync operation as completed
   */
  public completeSync(): void {
    this.state = DLASSyncState.IDLE;
    this.progress.state = DLASSyncState.IDLE;
    this.progress.endTime = new Date();
    
    if (this.progress.startTime) {
      this.progress.elapsedTimeMs = this.progress.endTime.getTime() - this.progress.startTime.getTime();
    }
    
    this.notify();
  }

  /**
   * Mark the sync operation as failed due to error
   */
  public errorSync(error: unknown): void {
    this.state = DLASSyncState.ERROR;
    this.progress.state = DLASSyncState.ERROR;
    this.progress.endTime = new Date();
    
    if (this.progress.startTime) {
      this.progress.elapsedTimeMs = this.progress.endTime.getTime() - this.progress.startTime.getTime();
    }
    
    this.addError('Sync error', error);
    this.notify();
  }

  /**
   * Get the current sync state
   */
  public getState(): DLASSyncState {
    return this.state;
  }

  /**
   * Get the current progress data
   */
  public getProgress(): DLASSyncProgress {
    return { ...this.progress };
  }

  private notify(): void {
    if (this.broadcaster) {
      this.broadcaster.broadcastSyncStatus({ ...this.progress });
    }
  }
} 