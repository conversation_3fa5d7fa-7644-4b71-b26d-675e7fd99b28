import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "~/components/ui/sheet";
import { DataTable } from "./data-table";
import { SLAForm } from "./sla-form";
import type { DatasetWithSLA } from "~/db/types/datasets";

interface DatasetSheetProps {
  isOpen: boolean;
  onClose: () => void;
  interfaceId: string;
  interfaceName: string;
  datasets: DatasetWithSLA[];
  onUpdateSLA: (datasetIds: string[], sla: string) => Promise<void>;
  onViewEvents?: (datasetName: string) => void;
  isLoading?: boolean;
  isSaving?: boolean;
}

export function DatasetSheet({
  isOpen,
  onClose,
  interfaceId,
  interfaceName,
  datasets,
  onUpdateSLA,
  onViewEvents,
  isLoading = false,
  isSaving = false,
}: DatasetSheetProps) {
  const [selectedDatasets, setSelectedDatasets] = useState<string[]>([]);
  const [isSLAFormOpen, setIsSLAFormOpen] = useState(false);

  // Get the SLA from the first selected dataset
  const getExistingSla = (): string | undefined => {
    if (selectedDatasets.length === 0) return undefined;
    
    // Find the first selected dataset in the datasets array
    const firstSelectedDataset = datasets.find(
      dataset => dataset.datasetName === selectedDatasets[0]
    );
    
    return firstSelectedDataset?.sla || undefined;
  };

  const handleSLAUpdate = async (sla: string) => {
    await onUpdateSLA(selectedDatasets, sla);
    setIsSLAFormOpen(false);
    setSelectedDatasets([]);
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent 
        side="right" 
        className="w-full lg:w-[80vw] xl:w-[85vw] 2xl:w-[90vw] p-0"
      >
        <div className="h-full flex flex-col">
          <div className="p-6 border-b">
            <SheetHeader>
              <SheetTitle>Datasets for {interfaceName}</SheetTitle>
            </SheetHeader>
          </div>

          <div className="flex-1 p-6 overflow-hidden flex flex-col">
            <div className="relative flex-1 overflow-hidden">
              {isLoading && (
                <div className="absolute inset-0 bg-background/50 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              )}
              <div className="h-full overflow-auto">
                <DataTable
                  data={datasets}
                  selectedDatasets={selectedDatasets}
                  onSelectionChange={setSelectedDatasets}
                  onViewEvents={onViewEvents}
                  onUpdateSLA={() => setIsSLAFormOpen(true)}
                />
              </div>
            </div>
          </div>
        </div>

        <SLAForm
          isOpen={isSLAFormOpen}
          onClose={() => setIsSLAFormOpen(false)}
          onSubmit={handleSLAUpdate}
          selectedCount={selectedDatasets.length}
          isSubmitting={isSaving}
          existingSla={getExistingSla()}
        />
      </SheetContent>
    </Sheet>
  );
} 