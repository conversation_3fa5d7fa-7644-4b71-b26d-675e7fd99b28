/**
 * Options for syncing applications
 */
export interface SyncOptions {
  orgLevel4?: string;
  orgLevel5?: string;
  pladaServiceIds?: string[];
}

export interface SyncResponseData {
  success: boolean;
  error?: string;
}

/**
 * Client-side service for handling application operations
 * This is a client-safe version that doesn't include server-only code
 */
export class ApplicationClient {
  /**
   * Syncs applications and their interfaces 
   * @param options Sync options including filters
   * @returns Response data from the sync operation
   */
  static async syncApplications(options: SyncOptions = {}): Promise<SyncResponseData> {
    try {
      const formData = new FormData();
      
      if (options.orgLevel4) {
        formData.append('orgLevel4', options.orgLevel4);
      }
      
      if (options.orgLevel5) {
        formData.append('orgLevel5', options.orgLevel5);
      }
      
      if (options.pladaServiceIds && options.pladaServiceIds.length > 0) {
        options.pladaServiceIds.forEach(id => {
          formData.append('pladaServiceIds[]', id);
        });
      }
      
      const response = await fetch('/oms/api/applications/sync', {
        method: 'POST',
        body: formData,
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        return { 
          success: false, 
          error: data.error || `Error ${response.status}: ${response.statusText}` 
        };
      }
      
      return data;
    } catch (error) {
      if (error instanceof Error) {
        return {
          success: false,
          error: error.message
        };
      }
      
      return {
        success: false,
        error: 'An unknown error occurred'
      };
    }
  }
} 