{"version": "6", "dialect": "sqlite", "id": "d1a40a81-0c14-4c74-a71a-87831d55d6d6", "prevId": "2983ab06-4ccb-46d3-b7a7-006520c69432", "tables": {"applications": {"name": "applications", "columns": {"application_instance_id": {"name": "application_instance_id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "short_name": {"name": "short_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "criticality": {"name": "criticality", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "strategic_status": {"name": "strategic_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "org_level2": {"name": "org_level2", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "org_level3": {"name": "org_level3", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "org_level4": {"name": "org_level4", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "org_level5": {"name": "org_level5", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "plada_service_id": {"name": "plada_service_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "plada_service_name": {"name": "plada_service_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "owner_psid": {"name": "owner_psid", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "owner_display_name": {"name": "owner_display_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "owner_email": {"name": "owner_email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "delegate_psid": {"name": "delegate_psid", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "delegate_display_name": {"name": "delegate_display_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "delegate_email": {"name": "delegate_email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"status_idx": {"name": "status_idx", "columns": ["status"], "isUnique": false}, "org_idx": {"name": "org_idx", "columns": ["org_level4", "org_level5"], "isUnique": false}, "plada_service_idx": {"name": "plada_service_idx", "columns": ["plada_service_id"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "interfaces": {"name": "interfaces", "columns": {"oms_interface_id": {"name": "oms_interface_id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "direction": {"name": "direction", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "eim_interface_id": {"name": "eim_interface_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "interface_name": {"name": "interface_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "send_app_id": {"name": "send_app_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "send_app_name": {"name": "send_app_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "received_app_id": {"name": "received_app_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "received_app_name": {"name": "received_app_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "transfer_type": {"name": "transfer_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "frequency": {"name": "frequency", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "technology": {"name": "technology", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "pattern": {"name": "pattern", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "related_drilldown_key": {"name": "related_drilldown_key", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "related_dataset_list": {"name": "related_dataset_list", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "demise_date": {"name": "demise_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_sync_time": {"name": "last_sync_time", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"send_app_idx": {"name": "send_app_idx", "columns": ["send_app_id"], "isUnique": false}, "received_app_idx": {"name": "received_app_idx", "columns": ["received_app_id"], "isUnique": false}, "interface_name_idx": {"name": "interface_name_idx", "columns": ["interface_name"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "datasets": {"name": "datasets", "columns": {"dataset_name": {"name": "dataset_name", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "direction": {"name": "direction", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "eim_interface_id": {"name": "eim_interface_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "dataset_status": {"name": "dataset_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "interface_serial": {"name": "interface_serial", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "oms_interface_id": {"name": "oms_interface_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "transfer_type": {"name": "transfer_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "frequency": {"name": "frequency", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "primary_data_term": {"name": "primary_data_term", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "product_type": {"name": "product_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "legal_entity": {"name": "legal_entity", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "legal_entity_code": {"name": "legal_entity_code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "line_of_business_name": {"name": "line_of_business_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "related_drilldown_list": {"name": "related_drilldown_list", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sla": {"name": "sla", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_arrival_time": {"name": "last_arrival_time", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "expected_arrival_time": {"name": "expected_arrival_time", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"datasets_oms_interface_id_idx": {"name": "datasets_oms_interface_id_idx", "columns": ["oms_interface_id"], "isUnique": false}, "datasets_interface_serial_idx": {"name": "datasets_interface_serial_idx", "columns": ["interface_serial"], "isUnique": false}, "datasets_status_idx": {"name": "datasets_status_idx", "columns": ["status"], "isUnique": false}}, "foreignKeys": {"datasets_oms_interface_id_interfaces_oms_interface_id_fk": {"name": "datasets_oms_interface_id_interfaces_oms_interface_id_fk", "tableFrom": "datasets", "tableTo": "interfaces", "columnsFrom": ["oms_interface_id"], "columnsTo": ["oms_interface_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "events": {"name": "events", "columns": {"msg_id": {"name": "msg_id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "business_date": {"name": "business_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_date_time": {"name": "created_date_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "dataset_name": {"name": "dataset_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "end_node_id": {"name": "end_node_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "end_node_name": {"name": "end_node_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "frequency": {"name": "frequency", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "raw_json": {"name": "raw_json", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "start_node_id": {"name": "start_node_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "start_node_name": {"name": "start_node_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "valid": {"name": "valid", "type": "text(1)", "primaryKey": false, "notNull": true, "autoincrement": false}, "reported_for_id": {"name": "reported_for_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "reported_for_name": {"name": "reported_for_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "milestone_type": {"name": "milestone_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}