import { z } from "zod";

// API Types
export interface HeetOrganizationApiResponse {
  level1: string;
  level1Id: string;
  level2: string;
  level2Id: string;
  level3: string;
  level3Id: string;
  level4: string;
  level4Id: string;
  name: string;
  id: string;
  level: number;
}

// Zod Schema for API Response Validation
export const HeetOrganizationApiResponseSchema = z.array(
  z.object({
    level1: z.string(),
    level1Id: z.string(),
    level2: z.string(),
    level2Id: z.string(),
    level3: z.string(),
    level3Id: z.string(),
    level4: z.string(),
    level4Id: z.string(),
    name: z.string(),
    id: z.string(),
    level: z.number(),
  })
);

// UI Types
export interface OrgLevel {
  label: string;
  value: string;
}

// Form Types
export interface OrganizationFormData {
  level4Id: string;
  level4: string;
  level5Id: string;
  level5: string;
} 